{
  "extends": ["next/core-web-vitals", "next/typescript"],
  "rules": {
    // TypeScript 相关规则 - 调整为更宽松的设置
    "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/prefer-const": "off",
    "@typescript-eslint/no-var-requires": "warn",
    "@typescript-eslint/no-unsafe-function-type": "warn",

    // React 相关规则
    "react/no-unescaped-entities": "off",
    "react/display-name": "off",
    "react-hooks/exhaustive-deps": "warn",
    "react-hooks/rules-of-hooks": "warn",

    // 通用规则 - 调整为警告而非错误
    "no-console": "off",
    "no-debugger": "warn",
    "no-alert": "warn",
    "no-var": "warn",
    "prefer-const": "off",
    "eqeqeq": "warn",
    "curly": "off",

    // 代码风格 - 关闭严格的格式要求
    "indent": "off",
    "quotes": "off",
    "semi": "off",
    "comma-dangle": "off",
    "object-curly-spacing": "off",
    "array-bracket-spacing": "off",

    // 导入规则 - 调整为警告
    "import/order": "off",
    "import/no-anonymous-default-export": "warn",

    // Next.js 特定规则
    "@next/next/no-assign-module-variable": "warn",
    "@next/next/no-img-element": "warn",

    // TypeScript 特定规则
    "@typescript-eslint/no-empty-object-type": "warn",
    "@typescript-eslint/no-require-imports": "warn",

    // 其他规则
    "prefer-spread": "warn"
  },
  "overrides": [
    {
      "files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "no-console": "off"
      }
    }
  ]
}
