# TMH任务调度系统 - SSH 一键部署指南

## 🚀 快速开始

使用 SSH 一键部署脚本可以自动将应用构建并部署到 Windows Server 2016 (*************)。

## 📋 前提条件

### 开发机器要求

- **Node.js 18+** 和 **npm**
- **SSH 客户端** (Windows 10+ 自带 OpenSSH，或安装 Git Bash)
- **网络连接** 到目标服务器

### 服务器要求 (*************)

- **Windows Server 2016** 或更高版本
- **OpenSSH Server** 已启用
- **Node.js 18+** 已安装
- **PM2** 已安装 (`npm install -g pm2`)

## 🔧 服务器准备

### 1. 启用 OpenSSH Server

在 Windows Server 2016 上启用 SSH：

```powershell
# 以管理员身份运行 PowerShell

# 安装 OpenSSH Server
Add-WindowsCapability -Online -Name OpenSSH.Server~~~~0.0.1.0

# 启动并设置自启动
Start-Service sshd
Set-Service -Name sshd -StartupType 'Automatic'

# 配置防火墙
New-NetFirewallRule -Name sshd -DisplayName 'OpenSSH Server (sshd)' -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 22
```

### 2. 安装 Node.js 和 PM2

```cmd
# 下载并安装 Node.js 18+ LTS 版本
# https://nodejs.org/

# 验证安装
node --version
npm --version

# 安装 PM2
npm install -g pm2
npm install -g pm2-windows-service
```

### 3. 创建部署目录

```cmd
mkdir C:\inetpub\tmh-task-dispatcher
```

## 🚀 一键部署

### 方法1: PowerShell 脚本 (推荐)

```powershell
# 基本部署
.\ssh-deploy.ps1

# 自定义参数
.\ssh-deploy.ps1 -ServerIP ************* -ServerUser administrator

# 使用 SSH 密钥
.\ssh-deploy.ps1 -UseKey -KeyPath C:\Users\<USER>\.ssh\id_rsa

# 跳过构建 (如果已经构建过)
.\ssh-deploy.ps1 -SkipBuild
```

### 方法2: Bash 脚本 (Linux/macOS/Git Bash)

```bash
# 设置执行权限
chmod +x ssh-deploy.sh

# 基本部署
./ssh-deploy.sh

# 自定义参数
./ssh-deploy.sh -s ************* -u administrator

# 使用 SSH 密钥
./ssh-deploy.sh -k ~/.ssh/id_rsa

# 跳过构建
./ssh-deploy.sh --skip-build
```

### 方法3: npm 命令

```bash
# PowerShell 版本
npm run deploy:ssh

# Bash 版本
npm run deploy:ssh-bash
```

## 📝 脚本参数

### PowerShell 版本参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-ServerIP` | 目标服务器IP | ************* |
| `-ServerUser` | SSH用户名 | administrator |
| `-ServerPort` | SSH端口 | 22 |
| `-DeployPath` | 部署路径 | C:\inetpub\tmh-task-dispatcher |
| `-AppPort` | 应用端口 | 3000 |
| `-SkipBuild` | 跳过构建步骤 | false |
| `-UseKey` | 使用SSH密钥认证 | false |
| `-KeyPath` | SSH私钥路径 | - |

### Bash 版本参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-s, --server` | 目标服务器IP | ************* |
| `-u, --user` | SSH用户名 | administrator |
| `-p, --port` | SSH端口 | 22 |
| `-d, --deploy-path` | 部署路径 | C:\inetpub\tmh-task-dispatcher |
| `-a, --app-port` | 应用端口 | 3000 |
| `-k, --key` | SSH私钥路径 | - |
| `--skip-build` | 跳过构建步骤 | false |

## 🔄 部署流程

脚本会自动执行以下步骤：

1. **检查本地依赖** - 验证 SSH、npm 等工具
2. **测试 SSH 连接** - 确保能连接到服务器
3. **构建应用** - 使用内网配置构建 Next.js 应用
4. **创建部署包** - 打包所有必要文件
5. **上传到服务器** - 通过 SCP 传输部署包
6. **服务器部署** - 自动安装依赖并启动服务
7. **验证部署** - 检查应用和服务状态

## 🔍 部署验证

部署完成后，脚本会自动验证：

- **HTTP 响应** - 测试 `http://*************:3000`
- **PM2 服务状态** - 检查服务是否正常运行

## 🛠️ 管理命令

部署完成后，您可以使用以下命令管理应用：

```bash
# 查看服务状态
ssh administrator@************* "pm2 status"

# 查看实时日志
ssh administrator@************* "pm2 logs tmh-task-dispatcher"

# 重启服务
ssh administrator@************* "pm2 restart tmh-task-dispatcher"

# 停止服务
ssh administrator@************* "pm2 stop tmh-task-dispatcher"

# 查看系统资源
ssh administrator@************* "pm2 monit"
```

## 🔧 故障排除

### 常见问题

1. **SSH 连接失败**
   ```bash
   # 检查 SSH 服务
   ssh administrator@************* "Get-Service sshd"
   
   # 检查防火墙
   ssh administrator@************* "Get-NetFirewallRule -DisplayName '*ssh*'"
   ```

2. **权限问题**
   ```bash
   # 确保用户有管理员权限
   # 或使用具有足够权限的用户账户
   ```

3. **端口占用**
   ```bash
   # 检查端口占用
   ssh administrator@************* "netstat -ano | findstr :3000"
   ```

4. **Node.js 未找到**
   ```bash
   # 检查 Node.js 安装
   ssh administrator@************* "node --version"
   ssh administrator@************* "npm --version"
   ```

### 调试模式

如果部署失败，可以手动执行步骤进行调试：

```bash
# 1. 手动上传文件
scp tmh-deploy-*.tar.gz administrator@*************:~/

# 2. 登录服务器
ssh administrator@*************

# 3. 手动解压和安装
cd C:\inetpub\tmh-task-dispatcher
tar -xzf ~/tmh-deploy-*.tar.gz
npm ci --only=production

# 4. 手动启动
npm start
# 或
pm2 start npm --name "tmh-task-dispatcher" -- start
```

## 🔐 SSH 密钥认证

为了提高安全性，建议使用 SSH 密钥认证：

### 1. 生成 SSH 密钥对

```bash
# 在开发机器上生成密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

### 2. 复制公钥到服务器

```bash
# 复制公钥到服务器
ssh-copy-id administrator@*************

# 或手动复制
cat ~/.ssh/id_rsa.pub | ssh administrator@************* "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### 3. 使用密钥部署

```bash
# PowerShell
.\ssh-deploy.ps1 -UseKey -KeyPath C:\Users\<USER>\.ssh\id_rsa

# Bash
./ssh-deploy.sh -k ~/.ssh/id_rsa
```

## 📊 性能监控

部署完成后，可以通过以下方式监控应用：

```bash
# PM2 监控界面
ssh administrator@************* "pm2 monit"

# 查看系统资源
ssh administrator@************* "tasklist | findstr node"

# 查看网络连接
ssh administrator@************* "netstat -an | findstr :3000"
```

## 🔄 更新部署

要更新应用，只需重新运行部署脚本：

```bash
# 脚本会自动备份当前版本并部署新版本
.\ssh-deploy.ps1
```

---

**现在您可以通过一条命令完成整个部署流程！** 🎉

访问地址：`http://*************:3000`
