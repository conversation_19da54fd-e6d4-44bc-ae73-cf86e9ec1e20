# TMH任务调度系统 - SSH 内网部署指南

## 🚀 改造完成

我已经成功改造了 `deploy-intranet.js` 脚本，使其支持 SSH 登录远程 Windows Server 完成自动部署。

## 📋 主要改进

### ✨ 新增功能

1. **SSH 自动连接** - 支持密码和密钥认证
2. **自动部署流程** - 一键完成从构建到部署的全过程
3. **Windows Server 支持** - 针对 Windows Server 2016 优化
4. **跨平台兼容** - 支持 Windows 和 Linux 开发环境
5. **智能错误处理** - 完善的错误检测和回滚机制

### 🔧 配置更新

```javascript
const intranetConfig = {
  serverIP: '*************',
  serverPort: 9001, // 应用端口（已更新）
  sshPort: 22, // SSH 端口
  serverUser: 'administrator', // SSH 用户
  deployPath: 'C:\\inetpub\\tmh-task-dispatcher', // Windows 路径
  serviceName: 'tmh-task-dispatcher',
  
  // SSH 配置
  useSSH: true, // 启用 SSH 自动部署
  sshKeyPath: '', // SSH 私钥路径，为空则使用密码认证
  
  // 构建配置
  buildCommand: 'npm run build:no-lint', // 跳过 lint 加快构建
  envFile: '.env.intranet',
};
```

## 🚀 使用方法

### 方法1: npm 命令（推荐）

```bash
npm run deploy:intranet
```

### 方法2: 直接运行

```bash
node scripts/deploy-intranet.js
```

## 📋 部署流程

脚本会自动执行以下步骤：

```
1. 检查内网环境
   ├── 检查 .env.intranet 文件
   ├── 检查 SSH 客户端
   └── 测试 SSH 连接

2. 准备构建环境
   ├── 备份当前 .env.local
   └── 应用内网环境配置

3. 构建应用
   ├── 清理之前的构建
   └── 执行 npm run build:no-lint

4. 创建部署包
   ├── 复制必要文件 (.next, public, package.json 等)
   ├── 创建 Windows 启动脚本
   └── 打包为 tar.gz 或 zip

5. SSH 自动部署
   ├── 上传部署包到服务器
   ├── 停止现有服务
   ├── 备份当前版本
   ├── 解压新版本
   ├── 安装依赖
   ├── 配置防火墙
   └── 启动 PM2 服务

6. 验证部署
   ├── 检查 PM2 服务状态
   └── 测试 HTTP 响应

7. 清理临时文件
```

## 🔐 SSH 认证配置

### 方法1: 密码认证（默认）

脚本会提示输入 SSH 密码，适合临时使用。

### 方法2: SSH 密钥认证（推荐）

1. **生成 SSH 密钥对**：
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

2. **复制公钥到服务器**：
   ```bash
   ssh-copy-id administrator@*************
   ```

3. **更新脚本配置**：
   ```javascript
   sshKeyPath: 'C:\\Users\\<USER>\\.ssh\\id_rsa', // Windows 路径
   // 或
   sshKeyPath: '~/.ssh/id_rsa', // Linux/macOS 路径
   ```

## 🖥️ 服务器准备

确保 Windows Server 2016 已准备：

### 1. 启用 OpenSSH Server

```powershell
# 以管理员身份运行
Add-WindowsCapability -Online -Name OpenSSH.Server~~~~*******
Start-Service sshd
Set-Service -Name sshd -StartupType 'Automatic'

# 配置防火墙
New-NetFirewallRule -Name sshd -DisplayName 'OpenSSH Server (sshd)' -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 22
```

### 2. 安装 Node.js 和 PM2

```cmd
# 下载并安装 Node.js 18+ LTS
# https://nodejs.org/

# 验证安装
node --version
npm --version

# 安装 PM2
npm install -g pm2
npm install -g pm2-windows-service
```

### 3. 创建部署目录

```cmd
mkdir C:\inetpub\tmh-task-dispatcher
```

## 🌐 部署结果

部署成功后：

- **访问地址**: `http://*************:9001`
- **部署路径**: `C:\inetpub\tmh-task-dispatcher`
- **服务名称**: `tmh-task-dispatcher`

## 🔧 管理命令

```bash
# 查看服务状态
ssh administrator@************* "pm2 status"

# 查看实时日志
ssh administrator@************* "pm2 logs tmh-task-dispatcher"

# 重启服务
ssh administrator@************* "pm2 restart tmh-task-dispatcher"

# 停止服务
ssh administrator@************* "pm2 stop tmh-task-dispatcher"

# 查看系统资源
ssh administrator@************* "pm2 monit"
```

## 🔍 故障排除

### 常见问题

1. **SSH 连接失败**
   - 检查服务器 SSH 服务状态
   - 验证用户名和密码
   - 检查防火墙设置
   - 确认 SSH 端口（默认 22）

2. **部署包上传失败**
   - 检查网络连接
   - 确认服务器磁盘空间
   - 验证用户权限

3. **服务启动失败**
   - 检查 Node.js 安装
   - 验证 PM2 安装
   - 查看应用日志

4. **端口冲突**
   - 检查端口 9001 是否被占用
   - 修改配置中的 serverPort

### 调试模式

如果遇到问题，可以：

1. **查看详细日志**：脚本会显示每个步骤的执行结果
2. **手动执行步骤**：可以逐步执行 SSH 命令进行调试
3. **检查服务器状态**：登录服务器查看具体错误

## 📊 性能优化

- **跳过类型检查**：使用 `build:no-lint` 加快构建
- **并行处理**：优化文件复制和压缩过程
- **增量部署**：保留依赖缓存，只更新应用代码

## 🔄 回滚机制

脚本包含自动备份功能：
- 部署前自动备份当前版本
- 部署失败时可手动恢复备份
- 备份路径包含时间戳便于识别

## 📈 监控建议

部署完成后建议：
1. 设置应用监控和告警
2. 配置日志轮转
3. 定期检查系统资源使用
4. 建立定期备份策略

---

**现在您可以通过 `npm run deploy:intranet` 命令一键完成从构建到部署的全过程！** 🎉

这个改造后的脚本大大简化了内网部署流程，提高了部署效率和可靠性。
