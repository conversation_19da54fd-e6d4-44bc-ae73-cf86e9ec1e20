<!DOCTYPE html>
<html>
<head>
    <title>音频测试</title>
</head>
<body>
    <h1>音频文件测试</h1>
    <audio controls>
        <!-- <source src="/sounds/alert.wav" type="audio/wav">
        <source src="/sounds/alert.mp3" type="audio/mpeg"> -->
        您的浏览器不支持音频播放。
    </audio>
    <br><br>
    <button onclick="playSound()">播放提示音</button>
    
    <script>
        function playSound() {
            const audio = document.querySelector('audio');
            audio.play().catch(error => {
                console.error('播放失败:', error);
                alert('播放失败: ' + error.message);
            });
        }
    </script>
</body>
</html>