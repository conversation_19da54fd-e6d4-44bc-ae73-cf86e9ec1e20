/**
 * Service Worker for TMH Task Dispatcher
 * 实现静态资源缓存和网络优化
 */

const CACHE_NAME = 'tmh-app-v1.0.0';
const STATIC_CACHE_NAME = 'tmh-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'tmh-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  // CSS 文件会在运行时动态添加
  // JS 文件会在运行时动态添加
];

// 需要缓存的 API 路径
const API_CACHE_PATTERNS = [
  /^\/api\/tasks/,
  /^\/api\/vehicles/,
  /^\/api\/config/,
];

// 缓存策略配置
const CACHE_STRATEGIES = {
  // 静态资源：缓存优先
  static: 'cache-first',
  // API 数据：网络优先，失败时使用缓存
  api: 'network-first',
  // 图片：缓存优先
  images: 'cache-first',
  // 字体：缓存优先
  fonts: 'cache-first',
};

// 缓存时间配置（秒）
const CACHE_DURATIONS = {
  static: 7 * 24 * 60 * 60, // 7天
  api: 5 * 60, // 5分钟
  images: 30 * 24 * 60 * 60, // 30天
  fonts: 365 * 24 * 60 * 60, // 1年
};

// ==================== Service Worker 事件处理 ====================

// 安装事件
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// 激活事件
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            // 删除旧版本的缓存
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName.startsWith('tmh-')) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return;
  }

  // 根据请求类型选择缓存策略
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isFontRequest(request)) {
    event.respondWith(handleFontRequest(request));
  } else {
    // 默认策略：网络优先
    event.respondWith(handleDefault(request));
  }
});

// ==================== 请求类型判断 ====================

function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.endsWith('.js') || 
         url.pathname.endsWith('.css') ||
         url.pathname.endsWith('.html') ||
         url.pathname === '/' ||
         url.pathname.startsWith('/_next/static/');
}

function isAPIRequest(request) {
  const url = new URL(request.url);
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

function isImageRequest(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/i);
}

function isFontRequest(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(woff|woff2|ttf|eot)$/i);
}

// ==================== 缓存策略实现 ====================

// 缓存优先策略
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    // 检查缓存是否过期
    const cacheTime = cachedResponse.headers.get('sw-cache-time');
    if (cacheTime) {
      const age = Date.now() - parseInt(cacheTime);
      const maxAge = getCacheMaxAge(request) * 1000;
      
      if (age > maxAge) {
        // 缓存过期，尝试更新
        try {
          const networkResponse = await fetch(request);
          if (networkResponse.ok) {
            await updateCache(cache, request, networkResponse.clone());
            return networkResponse;
          }
        } catch (error) {
          console.warn('Network failed, using stale cache', error);
        }
      }
    }
    
    return cachedResponse;
  }
  
  // 缓存中没有，从网络获取
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await updateCache(cache, request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Network request failed', error);
    throw error;
  }
}

// 网络优先策略
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await updateCache(cache, request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('Network failed, trying cache', error);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// 更新缓存
async function updateCache(cache, request, response) {
  // 添加缓存时间戳
  const responseWithTimestamp = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...response.headers,
      'sw-cache-time': Date.now().toString(),
    },
  });
  
  await cache.put(request, responseWithTimestamp);
}

// 获取缓存最大时间
function getCacheMaxAge(request) {
  if (isStaticAsset(request)) return CACHE_DURATIONS.static;
  if (isAPIRequest(request)) return CACHE_DURATIONS.api;
  if (isImageRequest(request)) return CACHE_DURATIONS.images;
  if (isFontRequest(request)) return CACHE_DURATIONS.fonts;
  return CACHE_DURATIONS.static;
}

// ==================== 具体处理函数 ====================

function handleStaticAsset(request) {
  return cacheFirst(request, STATIC_CACHE_NAME);
}

function handleAPIRequest(request) {
  return networkFirst(request, DYNAMIC_CACHE_NAME);
}

function handleImageRequest(request) {
  return cacheFirst(request, STATIC_CACHE_NAME);
}

function handleFontRequest(request) {
  return cacheFirst(request, STATIC_CACHE_NAME);
}

function handleDefault(request) {
  return networkFirst(request, DYNAMIC_CACHE_NAME);
}

// ==================== 消息处理 ====================

self.addEventListener('message', event => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_CACHE_STATS':
      getCacheStats().then(stats => {
        event.ports[0].postMessage({ type: 'CACHE_STATS', payload: stats });
      });
      break;
      
    case 'CLEAR_CACHE':
      clearCache(payload.cacheName).then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;
      
    default:
      console.warn('Unknown message type:', type);
  }
});

// 获取缓存统计信息
async function getCacheStats() {
  const cacheNames = await caches.keys();
  const stats = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    stats[cacheName] = {
      count: keys.length,
      size: 0, // 浏览器不提供精确的缓存大小
    };
  }
  
  return stats;
}

// 清理缓存
async function clearCache(cacheName) {
  if (cacheName) {
    await caches.delete(cacheName);
  } else {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
  }
}
