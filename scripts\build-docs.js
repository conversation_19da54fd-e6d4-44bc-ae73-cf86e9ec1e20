#!/usr/bin/env node

/**
 * 文档构建脚本
 * 用于构建和部署项目文档
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出函数
function colorLog(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 检查文档完整性
 */
function checkDocumentationIntegrity() {
  colorLog('blue', '\n📋 检查文档完整性...\n');
  
  const requiredDocs = [
    'docs/README.md',
    'docs/index.md',
    'docs/overview/project-overview.md',
    'docs/overview/system-architecture.md',
    'docs/overview/tech-stack.md',
    'docs/getting-started/quick-start.md',
    'docs/development/coding-standards.md',
    'docs/features/task-management.md',
    'docs/api/overview.md',
    'docs/deployment/build-and-deploy.md',
    'docs/troubleshooting/common-issues.md',
    'docs/reference/contributing.md',
    'docs/reference/changelog.md',
    'docs/reference/faq.md',
    'docs/reference/glossary.md'
  ];
  
  const missingDocs = [];
  
  requiredDocs.forEach(doc => {
    if (!fs.existsSync(doc)) {
      missingDocs.push(doc);
    } else {
      colorLog('green', `✅ ${doc}`);
    }
  });
  
  if (missingDocs.length > 0) {
    colorLog('red', '\n❌ 缺失的文档:');
    missingDocs.forEach(doc => {
      colorLog('red', `   - ${doc}`);
    });
    return false;
  }
  
  colorLog('green', '\n🎉 所有必需文档都存在！');
  return true;
}

/**
 * 验证文档链接
 */
function validateDocumentationLinks() {
  colorLog('blue', '\n🔗 验证文档链接...\n');
  
  const docsDir = 'docs';
  const linkPattern = /\[([^\]]+)\]\(([^)]+)\)/g;
  const issues = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.')) {
        scanDirectory(itemPath);
      } else if (item.endsWith('.md')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        let match;
        
        while ((match = linkPattern.exec(content)) !== null) {
          const linkText = match[1];
          const linkUrl = match[2];
          
          // 检查内部链接
          if (linkUrl.startsWith('./') || linkUrl.startsWith('../')) {
            const resolvedPath = path.resolve(path.dirname(itemPath), linkUrl);
            if (!fs.existsSync(resolvedPath)) {
              issues.push({
                file: itemPath,
                link: linkUrl,
                text: linkText,
                type: 'broken_internal_link'
              });
            }
          }
        }
      }
    });
  }
  
  scanDirectory(docsDir);
  
  if (issues.length > 0) {
    colorLog('yellow', '⚠️ 发现链接问题:');
    issues.forEach(issue => {
      colorLog('yellow', `   ${issue.file}: ${issue.link}`);
    });
  } else {
    colorLog('green', '✅ 所有内部链接都有效！');
  }
  
  return issues.length === 0;
}

/**
 * 生成文档目录
 */
function generateTableOfContents() {
  colorLog('blue', '\n📚 生成文档目录...\n');
  
  const docsStructure = {
    '项目概述': [
      { name: '项目概述', path: './overview/project-overview.md' },
      { name: '系统架构', path: './overview/system-architecture.md' },
      { name: '技术栈', path: './overview/tech-stack.md' }
    ],
    '快速开始': [
      { name: '快速开始', path: './getting-started/quick-start.md' },
      { name: '环境搭建', path: './getting-started/environment-setup.md' },
      { name: '项目结构', path: './getting-started/project-structure.md' }
    ],
    '开发指南': [
      { name: '开发规范', path: './development/coding-standards.md' },
      { name: '组件开发', path: './development/component-development.md' },
      { name: '状态管理', path: './development/state-management.md' },
      { name: 'API集成', path: './development/api-integration.md' },
      { name: '测试指南', path: './development/testing-guide.md' }
    ],
    '功能模块': [
      { name: '任务管理', path: './features/task-management.md' },
      { name: '车辆调度', path: './features/vehicle-dispatch.md' },
      { name: '配比管理', path: './features/ratio-management.md' },
      { name: '设置中心', path: './features/settings.md' }
    ],
    'API文档': [
      { name: 'API概述', path: './api/overview.md' },
      { name: '任务API', path: './api/task-api.md' },
      { name: '车辆API', path: './api/vehicle-api.md' },
      { name: '配比API', path: './api/ratio-api.md' }
    ],
    'UI/UX指南': [
      { name: '设计系统', path: './ui-ux/design-system.md' },
      { name: '主题配置', path: './ui-ux/theming.md' },
      { name: '响应式设计', path: './ui-ux/responsive-design.md' },
      { name: '用户体验', path: './ui-ux/user-experience.md' }
    ],
    '部署运维': [
      { name: '构建部署', path: './deployment/build-and-deploy.md' },
      { name: '环境配置', path: './deployment/environment-config.md' },
      { name: '性能优化', path: './deployment/performance-optimization.md' },
      { name: '监控告警', path: './deployment/monitoring.md' }
    ],
    '故障排除': [
      { name: '常见问题', path: './troubleshooting/common-issues.md' },
      { name: '调试指南', path: './troubleshooting/debugging-guide.md' },
      { name: '错误处理', path: './troubleshooting/error-handling.md' }
    ],
    '参考资料': [
      { name: '更新日志', path: './reference/changelog.md' },
      { name: '贡献指南', path: './reference/contributing.md' },
      { name: 'FAQ', path: './reference/faq.md' },
      { name: '术语表', path: './reference/glossary.md' }
    ]
  };
  
  let tocContent = '# 📚 文档目录\n\n';
  
  Object.entries(docsStructure).forEach(([category, docs]) => {
    tocContent += `## ${category}\n\n`;
    docs.forEach(doc => {
      if (fs.existsSync(path.join('docs', doc.path))) {
        tocContent += `- [${doc.name}](${doc.path})\n`;
        colorLog('green', `✅ ${doc.name}`);
      } else {
        tocContent += `- ⚠️ ${doc.name} (缺失)\n`;
        colorLog('yellow', `⚠️ ${doc.name} - 文件不存在`);
      }
    });
    tocContent += '\n';
  });
  
  tocContent += `---\n\n**生成时间**: ${new Date().toLocaleString()}\n`;
  
  fs.writeFileSync('docs/table-of-contents.md', tocContent, 'utf8');
  colorLog('green', '\n📚 文档目录已生成: docs/table-of-contents.md');
}

/**
 * 生成文档统计
 */
function generateDocumentationStats() {
  colorLog('blue', '\n📊 生成文档统计...\n');
  
  const docsDir = 'docs';
  const stats = {
    totalFiles: 0,
    totalLines: 0,
    totalWords: 0,
    categories: {},
    lastUpdated: new Date().toISOString()
  };
  
  function scanDirectory(dir, category = 'root') {
    const items = fs.readdirSync(dir);
    
    if (!stats.categories[category]) {
      stats.categories[category] = { files: 0, lines: 0, words: 0 };
    }
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.')) {
        scanDirectory(itemPath, item);
      } else if (item.endsWith('.md')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        const lines = content.split('\n').length;
        const words = content.split(/\s+/).length;
        
        stats.totalFiles++;
        stats.totalLines += lines;
        stats.totalWords += words;
        
        stats.categories[category].files++;
        stats.categories[category].lines += lines;
        stats.categories[category].words += words;
      }
    });
  }
  
  scanDirectory(docsDir);
  
  // 生成统计报告
  let reportContent = '# 📊 文档统计报告\n\n';
  reportContent += `**生成时间**: ${new Date().toLocaleString()}\n\n`;
  reportContent += '## 总体统计\n\n';
  reportContent += `- **文档总数**: ${stats.totalFiles} 个\n`;
  reportContent += `- **总行数**: ${stats.totalLines.toLocaleString()} 行\n`;
  reportContent += `- **总字数**: ${stats.totalWords.toLocaleString()} 字\n\n`;
  reportContent += '## 分类统计\n\n';
  reportContent += '| 分类 | 文件数 | 行数 | 字数 |\n';
  reportContent += '|------|--------|------|------|\n';
  
  Object.entries(stats.categories).forEach(([category, data]) => {
    reportContent += `| ${category} | ${data.files} | ${data.lines.toLocaleString()} | ${data.words.toLocaleString()} |\n`;
  });
  
  fs.writeFileSync('docs/documentation-stats.md', reportContent, 'utf8');
  
  colorLog('green', '📊 文档统计报告已生成');
  colorLog('blue', `   - 文档总数: ${stats.totalFiles} 个`);
  colorLog('blue', `   - 总行数: ${stats.totalLines.toLocaleString()} 行`);
  colorLog('blue', `   - 总字数: ${stats.totalWords.toLocaleString()} 字`);
}

/**
 * 构建静态文档站点
 */
function buildDocumentationSite() {
  colorLog('blue', '\n🏗️ 构建文档站点...\n');
  
  try {
    // 检查是否安装了 VitePress
    execSync('npx vitepress --version', { stdio: 'pipe' });
    
    // 构建文档
    execSync('npx vitepress build docs', { stdio: 'inherit' });
    
    colorLog('green', '🎉 文档站点构建成功！');
    colorLog('blue', '   输出目录: dist/docs/');
    
  } catch (error) {
    colorLog('yellow', '⚠️ VitePress 未安装，跳过站点构建');
    colorLog('blue', '   可以运行以下命令安装: npm install -D vitepress');
  }
}

/**
 * 主函数
 */
function main() {
  colorLog('cyan', '\n📚 TMH车辆调度系统 - 文档构建工具\n');
  
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  switch (command) {
    case 'check':
      checkDocumentationIntegrity();
      validateDocumentationLinks();
      break;
      
    case 'toc':
      generateTableOfContents();
      break;
      
    case 'stats':
      generateDocumentationStats();
      break;
      
    case 'build':
      buildDocumentationSite();
      break;
      
    case 'all':
    default:
      const integrityOk = checkDocumentationIntegrity();
      const linksOk = validateDocumentationLinks();
      
      if (integrityOk && linksOk) {
        generateTableOfContents();
        generateDocumentationStats();
        buildDocumentationSite();
        
        colorLog('green', '\n🎉 文档构建完成！');
        colorLog('blue', '\n可用命令:');
        colorLog('yellow', '  npm run docs:dev    # 启动开发服务器');
        colorLog('yellow', '  npm run docs:build  # 构建生产版本');
        colorLog('yellow', '  npm run docs:serve  # 预览构建结果');
      } else {
        colorLog('red', '\n❌ 文档检查失败，请修复问题后重试');
        process.exit(1);
      }
      break;
  }
}

// 运行主函数
main();
