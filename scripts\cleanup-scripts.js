#!/usr/bin/env node

/**
 * TMH任务调度系统 - 脚本清理工具
 * 移除重复、过时和一次性使用的脚本
 */

const fs = require('fs');
const path = require('path');

const scriptsDir = __dirname;

// 需要移除的脚本列表
const scriptsToRemove = [
  // 重复的SSH配置脚本
  'auto-setup-ssh.ps1',
  'setup-ssh-key.bat', 
  'setup-ssh-key.ps1',
  'setup-auto-deploy.bat',
  
  // 一次性修复脚本
  'fix-deployment.ps1',
  'fix-pm2-windows.bat',
  'quick-fix-pm2.ps1',
  'quick-fix.bat',
  
  // 过时的部署脚本
  'deploy.js',
  
  // 已修复问题的临时脚本
  'build-without-problematic-pages.js'
];

// 需要保留的核心脚本
const coreScripts = [
  'deploy-intranet.js',      // 主部署脚本
  'setup-ssh.js',            // 整合的SSH配置脚本
  'build-docs.js',           // 文档构建
  'code-quality-optimizer.js', // 代码质量优化
  'setup-pre-commit.js',     // Git钩子配置
  'fix-import-paths.js',     // 导入路径修复
  'fix-styles.js',           // 样式修复
  'package-optimization.js', // 包优化
  'create-alert-sound.js',   // 功能脚本
  'cleanup-scripts.js',      // 清理脚本（自己）
  'README.md'                // 说明文档
];

function colorLog(color, message) {
  const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function cleanupScripts() {
  colorLog('blue', '🧹 TMH任务调度系统 - 脚本清理');
  colorLog('cyan', '🎯 目标: 移除重复、过时和一次性脚本');
  console.log('');
  
  let removedCount = 0;
  let keptCount = 0;
  
  // 获取当前scripts目录中的所有文件
  const currentFiles = fs.readdirSync(scriptsDir).filter(file => 
    fs.statSync(path.join(scriptsDir, file)).isFile()
  );
  
  colorLog('yellow', '📋 当前脚本文件:');
  currentFiles.forEach(file => {
    console.log(`   ${file}`);
  });
  console.log('');
  
  // 移除指定的脚本
  colorLog('red', '🗑️  移除以下脚本:');
  scriptsToRemove.forEach(script => {
    const scriptPath = path.join(scriptsDir, script);
    if (fs.existsSync(scriptPath)) {
      try {
        fs.unlinkSync(scriptPath);
        colorLog('red', `   ✓ 已删除: ${script}`);
        removedCount++;
      } catch (error) {
        colorLog('red', `   ✗ 删除失败: ${script} - ${error.message}`);
      }
    } else {
      colorLog('yellow', `   - 不存在: ${script}`);
    }
  });
  
  console.log('');
  
  // 显示保留的核心脚本
  colorLog('green', '✅ 保留的核心脚本:');
  coreScripts.forEach(script => {
    const scriptPath = path.join(scriptsDir, script);
    if (fs.existsSync(scriptPath)) {
      colorLog('green', `   ✓ ${script}`);
      keptCount++;
    } else {
      colorLog('yellow', `   ? 缺失: ${script}`);
    }
  });
  
  console.log('');
  
  // 检查是否有未分类的脚本
  const remainingFiles = fs.readdirSync(scriptsDir).filter(file => 
    fs.statSync(path.join(scriptsDir, file)).isFile() && 
    !coreScripts.includes(file) && 
    !scriptsToRemove.includes(file)
  );
  
  if (remainingFiles.length > 0) {
    colorLog('yellow', '⚠️  未分类的脚本:');
    remainingFiles.forEach(file => {
      colorLog('yellow', `   ? ${file}`);
    });
    console.log('');
  }
  
  // 统计信息
  colorLog('cyan', '📊 清理统计:');
  colorLog('red', `   删除脚本: ${removedCount} 个`);
  colorLog('green', `   保留脚本: ${keptCount} 个`);
  if (remainingFiles.length > 0) {
    colorLog('yellow', `   未分类: ${remainingFiles.length} 个`);
  }
  
  console.log('');
  colorLog('green', '✅ 脚本清理完成!');
  
  // 显示使用建议
  console.log('');
  colorLog('cyan', '📋 推荐的脚本使用方式:');
  colorLog('white', '   部署到内网: npm run deploy:intranet');
  colorLog('white', '   配置SSH: npm run setup:ssh');
  colorLog('white', '   代码优化: npm run optimize:code');
  colorLog('white', '   构建文档: npm run build:docs');
}

// 添加确认提示
function confirmCleanup() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  colorLog('yellow', '⚠️  即将删除以下脚本:');
  scriptsToRemove.forEach(script => {
    const scriptPath = path.join(scriptsDir, script);
    if (fs.existsSync(scriptPath)) {
      colorLog('red', `   - ${script}`);
    }
  });
  console.log('');
  
  rl.question('确认执行清理? (y/N): ', (answer) => {
    rl.close();
    
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      cleanupScripts();
    } else {
      colorLog('yellow', '取消清理操作');
    }
  });
}

if (require.main === module) {
  // 检查是否有 --force 参数
  if (process.argv.includes('--force')) {
    cleanupScripts();
  } else {
    confirmCleanup();
  }
}

module.exports = { cleanupScripts };
