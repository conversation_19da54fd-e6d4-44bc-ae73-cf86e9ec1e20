#!/usr/bin/env node

/**
 * 代码质量优化脚本
 * 自动修复常见的ESLint警告和TypeScript问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 优化配置
const optimizationConfig = {
  // 需要处理的文件模式
  filePatterns: [
    'src/**/*.ts',
    'src/**/*.tsx',
  ],
  
  // 排除的文件
  excludePatterns: [
    'src/**/*.test.ts',
    'src/**/*.test.tsx',
    'src/**/*.d.ts',
    'node_modules/**',
  ],
  
  // 优化规则
  rules: {
    // 移除未使用的导入
    removeUnusedImports: true,
    // 移除未使用的变量
    removeUnusedVariables: true,
    // 修复React Hook依赖
    fixHookDependencies: false, // 需要手动处理
    // 添加缺失的类型
    addMissingTypes: false, // 需要手动处理
    // 修复代码风格
    fixCodeStyle: true,
  },
};

// 统计信息
const stats = {
  filesProcessed: 0,
  issuesFixed: 0,
  issuesRemaining: 0,
  errors: 0,
};

/**
 * 获取所有需要处理的文件
 */
function getFilesToProcess() {
  const files = [];
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过node_modules等目录
          if (!item.startsWith('.') && item !== 'node_modules') {
            scanDirectory(fullPath);
          }
        } else if (stat.isFile()) {
          // 检查文件扩展名
          if (fullPath.endsWith('.ts') || fullPath.endsWith('.tsx')) {
            // 检查是否在排除列表中
            const shouldExclude = optimizationConfig.excludePatterns.some(pattern => {
              const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
              return regex.test(fullPath);
            });
            
            if (!shouldExclude) {
              files.push(fullPath);
            }
          }
        }
      });
    } catch (error) {
      colorLog('red', `扫描目录失败: ${dir} - ${error.message}`);
    }
  }
  
  scanDirectory('src');
  return files;
}

/**
 * 移除未使用的导入
 */
function removeUnusedImports(content, filePath) {
  let modified = false;
  const lines = content.split('\n');
  const newLines = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检查是否是导入行
    if (line.trim().startsWith('import ') && line.includes(' from ')) {
      // 提取导入的变量名
      const importMatch = line.match(/import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from/);
      
      if (importMatch) {
        let importedItems = [];
        
        if (importMatch[1]) {
          // 命名导入 { a, b, c }
          importedItems = importMatch[1].split(',').map(item => {
            const cleaned = item.trim();
            // 处理 "as" 重命名
            const asMatch = cleaned.match(/(\w+)\s+as\s+(\w+)/);
            return asMatch ? asMatch[2] : cleaned;
          });
        } else if (importMatch[2]) {
          // 命名空间导入 * as name
          importedItems = [importMatch[2]];
        } else if (importMatch[3]) {
          // 默认导入
          importedItems = [importMatch[3]];
        }
        
        // 检查这些导入是否在文件中被使用
        const usedItems = importedItems.filter(item => {
          const regex = new RegExp(`\\b${item}\\b`, 'g');
          const restOfFile = lines.slice(i + 1).join('\n');
          return regex.test(restOfFile);
        });
        
        if (usedItems.length === 0) {
          // 整行导入都未使用，删除整行
          colorLog('yellow', `移除未使用的导入: ${line.trim()} in ${filePath}`);
          modified = true;
          stats.issuesFixed++;
          continue;
        } else if (usedItems.length < importedItems.length && importMatch[1]) {
          // 部分导入未使用，只保留使用的部分
          const newImportLine = line.replace(/\{[^}]+\}/, `{ ${usedItems.join(', ')} }`);
          newLines.push(newImportLine);
          colorLog('yellow', `优化导入: ${line.trim()} -> ${newImportLine.trim()} in ${filePath}`);
          modified = true;
          stats.issuesFixed++;
          continue;
        }
      }
    }
    
    newLines.push(line);
  }
  
  return { content: newLines.join('\n'), modified };
}

/**
 * 移除未使用的变量声明
 */
function removeUnusedVariables(content, filePath) {
  let modified = false;
  const lines = content.split('\n');
  const newLines = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检查是否是变量声明行
    const varMatch = line.match(/^\s*(const|let|var)\s+(\w+)\s*=/);
    
    if (varMatch) {
      const varName = varMatch[2];
      
      // 检查变量是否在后续代码中被使用
      const restOfFile = lines.slice(i + 1).join('\n');
      const regex = new RegExp(`\\b${varName}\\b`, 'g');
      
      if (!regex.test(restOfFile)) {
        colorLog('yellow', `移除未使用的变量: ${varName} in ${filePath}`);
        modified = true;
        stats.issuesFixed++;
        continue;
      }
    }
    
    newLines.push(line);
  }
  
  return { content: newLines.join('\n'), modified };
}

/**
 * 修复代码风格问题
 */
function fixCodeStyle(content, filePath) {
  let modified = false;
  let newContent = content;
  
  // 移除多余的空行
  const originalLineCount = newContent.split('\n').length;
  newContent = newContent.replace(/\n\s*\n\s*\n/g, '\n\n');
  const newLineCount = newContent.split('\n').length;
  
  if (originalLineCount !== newLineCount) {
    colorLog('yellow', `移除多余空行: ${originalLineCount - newLineCount}行 in ${filePath}`);
    modified = true;
    stats.issuesFixed++;
  }
  
  // 修复缩进问题（简单处理）
  const lines = newContent.split('\n');
  const fixedLines = lines.map(line => {
    // 将tab替换为2个空格
    if (line.includes('\t')) {
      modified = true;
      return line.replace(/\t/g, '  ');
    }
    return line;
  });
  
  if (modified) {
    newContent = fixedLines.join('\n');
  }
  
  return { content: newContent, modified };
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    colorLog('cyan', `处理文件: ${filePath}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    let currentContent = content;
    let fileModified = false;
    
    // 应用各种优化规则
    if (optimizationConfig.rules.removeUnusedImports) {
      const result = removeUnusedImports(currentContent, filePath);
      currentContent = result.content;
      fileModified = fileModified || result.modified;
    }
    
    if (optimizationConfig.rules.removeUnusedVariables) {
      const result = removeUnusedVariables(currentContent, filePath);
      currentContent = result.content;
      fileModified = fileModified || result.modified;
    }
    
    if (optimizationConfig.rules.fixCodeStyle) {
      const result = fixCodeStyle(currentContent, filePath);
      currentContent = result.content;
      fileModified = fileModified || result.modified;
    }
    
    // 如果文件被修改，写回文件
    if (fileModified) {
      fs.writeFileSync(filePath, currentContent, 'utf8');
      colorLog('green', `✅ 文件已优化: ${filePath}`);
    }
    
    stats.filesProcessed++;
    
  } catch (error) {
    colorLog('red', `❌ 处理文件失败: ${filePath} - ${error.message}`);
    stats.errors++;
  }
}

/**
 * 运行ESLint自动修复
 */
function runESLintFix() {
  colorLog('blue', '\n🔧 运行ESLint自动修复...\n');
  
  try {
    execSync('npx eslint src --fix --ext .ts,.tsx', { 
      stdio: 'pipe',
      timeout: 120000, // 2分钟超时
    });
    colorLog('green', '✅ ESLint自动修复完成');
  } catch (error) {
    colorLog('yellow', '⚠️ ESLint自动修复部分完成（有些问题需要手动处理）');
  }
}

/**
 * 生成优化报告
 */
function generateReport() {
  const report = [
    '# 代码质量优化报告',
    '',
    `生成时间: ${new Date().toLocaleString()}`,
    '',
    '## 📊 优化统计',
    '',
    `- 处理文件数: ${stats.filesProcessed}`,
    `- 修复问题数: ${stats.issuesFixed}`,
    `- 处理错误数: ${stats.errors}`,
    '',
    '## 🔧 应用的优化规则',
    '',
    '- ✅ 移除未使用的导入',
    '- ✅ 移除未使用的变量',
    '- ✅ 修复代码风格问题',
    '- ✅ ESLint自动修复',
    '',
    '## 📋 需要手动处理的问题',
    '',
    '- React Hook依赖优化',
    '- 复杂的类型定义',
    '- 业务逻辑相关的警告',
    '- 性能优化相关的警告',
    '',
    '## 💡 建议',
    '',
    '1. 定期运行此脚本进行代码质量维护',
    '2. 在pre-commit钩子中集成代码质量检查',
    '3. 逐步提高ESLint规则的严格程度',
    '4. 建立代码审查流程',
    '',
  ];
  
  const reportPath = path.join(process.cwd(), 'code-quality-report.md');
  fs.writeFileSync(reportPath, report.join('\n'));
  
  return reportPath;
}

/**
 * 主函数
 */
async function main() {
  colorLog('cyan', '\n🚀 开始代码质量优化...\n');
  
  try {
    // 获取需要处理的文件
    const files = getFilesToProcess();
    colorLog('blue', `发现 ${files.length} 个文件需要处理`);
    
    // 处理每个文件
    for (const file of files) {
      processFile(file);
    }
    
    // 运行ESLint自动修复
    runESLintFix();
    
    // 生成报告
    const reportPath = generateReport();
    
    // 显示结果
    colorLog('cyan', '\n📊 优化完成!\n');
    colorLog('green', `✅ 处理文件: ${stats.filesProcessed}`);
    colorLog('green', `✅ 修复问题: ${stats.issuesFixed}`);
    if (stats.errors > 0) {
      colorLog('red', `❌ 处理错误: ${stats.errors}`);
    }
    
    colorLog('blue', `\n📄 详细报告已保存到: ${reportPath}`);
    
    // 运行最终的构建检查
    colorLog('blue', '\n🔍 运行最终构建检查...');
    try {
      execSync('npm run build', { stdio: 'pipe', timeout: 120000 });
      colorLog('green', '✅ 构建检查通过');
    } catch (error) {
      colorLog('yellow', '⚠️ 构建检查发现问题，请查看详细日志');
    }
    
  } catch (error) {
    colorLog('red', `\n💥 优化过程失败: ${error.message}`);
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  colorLog('red', `\n💥 未捕获的异常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `\n💥 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

// 运行主函数
main();
