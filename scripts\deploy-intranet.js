#!/usr/bin/env node

/**
 * 内网部署脚本
 * 用于将应用部署到内网服务器 *************
 */

const { execSync } = require('child_process');
const path = require('path');
const os = require('os');
const fs = require('fs');

// 加载SSH配置
function loadDeployConfig() {
  const configFile = path.join(__dirname, '..', '.env.deploy');
  const defaultConfig = {
    SERVER_IP: '*************',
    SERVER_USER: 'administrator',
    SERVER_PASSWORD: '13834567299Goodbye!',
    SSH_PORT: '22',
    DEPLOY_PATH: 'C:\\inetpub\\tmh-task-dispatcher',
    SERVICE_NAME: 'tmh-task-dispatcher',
    SERVER_PORT: '9001'
  };

  if (fs.existsSync(configFile)) {
    try {
      const envContent = fs.readFileSync(configFile, 'utf8');
      const envLines = envContent.split('\n');

      envLines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          defaultConfig[key.trim()] = value.trim().replace(/['"]/g, '');
        }
      });
    } catch (error) {
      console.log('⚠️ 配置文件读取失败，使用默认配置');
    }
  }

  return defaultConfig;
}

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 检查并安装PuTTY工具
 */
function ensurePuttyInstalled() {
  const puttyPaths = [
    'plink', // PATH中的plink
    'C:\\Program Files\\PuTTY\\plink.exe',
    'C:\\Program Files (x86)\\PuTTY\\plink.exe',
    path.join(process.env.PROGRAMFILES || 'C:\\Program Files', 'PuTTY', 'plink.exe'),
    path.join(process.env['PROGRAMFILES(X86)'] || 'C:\\Program Files (x86)', 'PuTTY', 'plink.exe')
  ];

  // 检查是否已安装PuTTY
  for (const puttyPath of puttyPaths) {
    try {
      execSync(`"${puttyPath}" -V`, { stdio: 'pipe' });
      colorLog('green', `✅ 找到PuTTY: ${puttyPath}`);
      return puttyPath;
    } catch (error) {
      // 继续尝试下一个路径
    }
  }

  // 如果没有找到PuTTY，尝试自动安装
  colorLog('yellow', '⚠️ 未检测到PuTTY工具，正在尝试自动安装...');

  try {
    // 尝试使用PowerShell安装PuTTY
    const installScript = path.join(__dirname, 'install-putty-simple.ps1');
    if (fs.existsSync(installScript)) {
      colorLog('blue', '🔄 正在安装PuTTY...');
      execSync(`powershell -ExecutionPolicy Bypass -File "${installScript}"`, { stdio: 'inherit' });

      // 重新检查是否安装成功
      for (const puttyPath of puttyPaths) {
        try {
          execSync(`"${puttyPath}" -V`, { stdio: 'pipe' });
          colorLog('green', `✅ PuTTY安装成功: ${puttyPath}`);
          return puttyPath;
        } catch (error) {
          // 继续尝试下一个路径
        }
      }
    }
  } catch (error) {
    colorLog('red', `❌ PuTTY自动安装失败: ${error.message}`);
  }

  // 如果自动安装失败，提示用户手动安装
  colorLog('red', '❌ 无法找到或安装PuTTY工具');
  colorLog('yellow', '请手动安装PuTTY:');
  colorLog('cyan', '1. 运行: powershell scripts/install-putty-simple.ps1');
  colorLog('cyan', '2. 或从官网下载: https://www.putty.org/');
  colorLog('cyan', '3. 或使用winget: winget install PuTTY.PuTTY');

  throw new Error('PuTTY工具未安装，无法进行SSH连接');
}

/**
 * 构建 SSH 命令 - 强制使用PuTTY避免密码输入
 */
function buildSSHCommand(command) {
  const { serverIP, serverUser, sshPort, sshKeyPath, serverPassword } = intranetConfig;

  // 检查SSH密钥是否存在
  const keyExists = sshKeyPath && fs.existsSync(sshKeyPath);

  let sshCmd;

  if (keyExists) {
    // 优先使用SSH密钥认证，但仍然使用PuTTY以保持一致性
    const plinkPath = ensurePuttyInstalled();
    sshCmd = `"${plinkPath}" -ssh -P ${sshPort} -i "${sshKeyPath}" -batch ${serverIP} -l ${serverUser}`;
  } else {
    // 强制使用PuTTY进行密码认证
    const plinkPath = ensurePuttyInstalled();
    sshCmd = `"${plinkPath}" -ssh -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch ${serverIP}`;
  }

  if (command) {
    sshCmd += ` "${command}"`;
  }

  return sshCmd;
}

/**
 * 获取PuTTY PSCP路径
 */
function getPscpPath() {
  const pscpPaths = [
    'pscp', // PATH中的pscp
    'C:\\Program Files\\PuTTY\\pscp.exe',
    'C:\\Program Files (x86)\\PuTTY\\pscp.exe',
    path.join(process.env.PROGRAMFILES || 'C:\\Program Files', 'PuTTY', 'pscp.exe'),
    path.join(process.env['PROGRAMFILES(X86)'] || 'C:\\Program Files (x86)', 'PuTTY', 'pscp.exe')
  ];

  for (const pscpPath of pscpPaths) {
    try {
      execSync(`"${pscpPath}" -V`, { stdio: 'pipe' });
      return pscpPath;
    } catch (error) {
      // 继续尝试下一个路径
    }
  }

  throw new Error('PSCP工具未找到，请确保PuTTY已正确安装');
}

/**
 * 构建 SCP 命令 - 强制使用PuTTY的PSCP避免密码输入
 */
function buildSCPCommand(localPath, remotePath) {
  const { serverIP, serverUser, sshPort, sshKeyPath, serverPassword } = intranetConfig;

  // 检查SSH密钥是否存在
  const keyExists = sshKeyPath && fs.existsSync(sshKeyPath);

  let scpCmd;

  if (keyExists) {
    // 使用SSH密钥认证，但仍然使用PSCP以保持一致性
    const pscpPath = getPscpPath();
    scpCmd = `"${pscpPath}" -P ${sshPort} -i "${sshKeyPath}" -batch "${localPath}" ${serverUser}@${serverIP}:"${remotePath}"`;
  } else {
    // 强制使用PSCP进行密码认证
    const pscpPath = getPscpPath();
    scpCmd = `"${pscpPath}" -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch "${localPath}" ${serverIP}:"${remotePath}"`;
  }

  return scpCmd;
}

/**
 * 执行 SSH 命令
 */
function runSSHCommand(command, description, options = {}) {
  const sshCmd = buildSSHCommand(command);
  return runCommand(sshCmd, description, options);
}

// 加载动态配置
const deployConfig = loadDeployConfig();

// 内网部署配置
const intranetConfig = {
  serverIP: deployConfig.SERVER_IP,
  serverPort: parseInt(deployConfig.SERVER_PORT),
  sshPort: parseInt(deployConfig.SSH_PORT),
  serverUser: deployConfig.SERVER_USER,
  serverPassword: deployConfig.SERVER_PASSWORD, // 添加密码配置
  deployPath: deployConfig.DEPLOY_PATH,
  serviceName: deployConfig.SERVICE_NAME,

  // SSH 配置
  useSSH: true,
  sshKeyPath: path.join(os.homedir(), '.ssh', 'id_rsa'), // 默认SSH私钥路径
  sshConfigHost: 'tmh-server', // SSH配置中的主机别名

  // 构建配置
  buildCommand: 'cross-env ESLINT_NO_DEV_ERRORS=true DISABLE_ESLINT_PLUGIN=true next build',
  envFile: '.env.intranet',

  // 部署前检查
  preDeployChecks: [
    'npm run typecheck', // 暂时跳过类型检查以加快部署
  ],
};

/**
 * 执行命令并处理错误
 */
function runCommand(command, description, options = {}) {
  try {
    colorLog('blue', `\n🔄 ${description}...`);
    colorLog('cyan', `执行命令: ${command}`);
    
    const result = execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit',
      cwd: process.cwd(),
      encoding: 'utf8',
      ...options
    });
    
    colorLog('green', `✅ ${description} 完成`);
    return { success: true, output: result };
  } catch (error) {
    colorLog('red', `❌ ${description} 失败`);
    colorLog('red', `错误: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 检查内网环境
 */
function checkIntranetEnvironment() {
  colorLog('blue', '\n🔍 检查内网环境配置...');

  // 检查环境文件
  if (!fs.existsSync(intranetConfig.envFile)) {
    colorLog('red', `❌ 内网环境文件不存在: ${intranetConfig.envFile}`);
    return false;
  }

  // 确保PuTTY已安装
  try {
    colorLog('blue', '🔧 检查PuTTY工具...');
    ensurePuttyInstalled();
    colorLog('green', '✅ PuTTY工具检查通过');
  } catch (error) {
    colorLog('red', `❌ PuTTY工具检查失败: ${error.message}`);
    return false;
  }

  // 测试 SSH 连接
  colorLog('blue', `🔗 测试 SSH 连接到 ${intranetConfig.serverIP}...`);

  try {
    const sshTestResult = runSSHCommand('echo "SSH连接成功"', 'SSH 连接测试', { silent: true });

    if (!sshTestResult.success) {
      colorLog('red', `❌ SSH 连接失败到 ${intranetConfig.serverIP}`);
      colorLog('yellow', '请检查：');
      colorLog('yellow', '1. 服务器 SSH 服务是否启动');
      colorLog('yellow', '2. 用户名和密码是否正确');
      colorLog('yellow', '3. SSH 密钥配置是否正确（如果使用密钥认证）');
      colorLog('yellow', '4. 防火墙是否允许 SSH 连接');
      colorLog('yellow', '5. 服务器IP地址是否正确');
      return false;
    }

    colorLog('green', '✅ SSH 连接测试成功');
  } catch (error) {
    colorLog('red', `❌ SSH 连接测试失败: ${error.message}`);
    return false;
  }

  colorLog('green', '✅ 内网环境检查通过');
  return true;
}

/**
 * 准备构建环境
 */
function prepareBuildEnvironment() {
  colorLog('blue', '\n🔧 准备构建环境...');
  
  try {
    // 备份当前环境文件
    if (fs.existsSync('.env.local')) {
      fs.copyFileSync('.env.local', '.env.local.backup');
      colorLog('cyan', '📋 已备份当前环境配置');
    }
    
    // 复制内网环境配置
    fs.copyFileSync(intranetConfig.envFile, '.env.local');
    colorLog('cyan', '📋 已应用内网环境配置');
    
    colorLog('green', '✅ 构建环境准备完成');
    return true;
  } catch (error) {
    colorLog('red', `❌ 构建环境准备失败: ${error.message}`);
    return false;
  }
}

/**
 * 恢复构建环境
 */
function restoreBuildEnvironment() {
  colorLog('blue', '\n🔄 恢复构建环境...');
  
  try {
    // 删除临时环境文件
    if (fs.existsSync('.env.local')) {
      fs.unlinkSync('.env.local');
    }
    
    // 恢复原环境文件
    if (fs.existsSync('.env.local.backup')) {
      fs.renameSync('.env.local.backup', '.env.local');
      colorLog('cyan', '📋 已恢复原环境配置');
    }
    
    colorLog('green', '✅ 构建环境恢复完成');
    return true;
  } catch (error) {
    colorLog('red', `❌ 构建环境恢复失败: ${error.message}`);
    return false;
  }
}

/**
 * 构建应用
 */
function buildApplication() {
  colorLog('blue', '\n🏗️  构建应用...');

  // 清理之前的构建
  if (fs.existsSync('.next')) {
    const cleanCmd = process.platform === 'win32' ? 'rmdir /s /q .next' : 'rm -rf .next';
    runCommand(cleanCmd, '清理之前的构建');
  }
  
  // 执行构建
  const buildResult = runCommand(intranetConfig.buildCommand, '构建应用');
  
  if (!buildResult.success) {
    return false;
  }
  
  // 检查构建输出
  if (!fs.existsSync('.next')) {
    colorLog('red', '❌ 构建输出目录不存在');
    return false;
  }
  
  colorLog('green', '✅ 应用构建完成');
  return true;
}

/**
 * 创建部署包
 */
function createDeploymentPackage() {
  colorLog('blue', '\n📦 创建部署包...');

  const deployDir = 'deploy-package';
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
  const packageName = `tmh-deploy-${timestamp}.tar.gz`;

  try {
    // 创建部署目录
    if (fs.existsSync(deployDir)) {
      if (process.platform === 'win32') {
        runCommand(`rmdir /s /q ${deployDir}`, '清理旧部署包');
      } else {
        runCommand(`rm -rf ${deployDir}`, '清理旧部署包');
      }
    }
    fs.mkdirSync(deployDir);

    // 复制必要文件
    const filesToCopy = [
      '.next',
      'public',
      'package.json',
      'package-lock.json',
      '.env.intranet',
      'next.config.ts'
    ];

    filesToCopy.forEach(file => {
      if (fs.existsSync(file)) {
        if (process.platform === 'win32') {
          if (fs.statSync(file).isDirectory()) {
            runCommand(`xcopy "${file}" "${deployDir}\\${file}\\" /e /i /q`, `复制 ${file}`);
          } else {
            runCommand(`copy "${file}" "${deployDir}\\"`, `复制 ${file}`);
          }
        } else {
          runCommand(`cp -r ${file} ${deployDir}/`, `复制 ${file}`);
        }
      }
    });

    // 创建 Windows 启动脚本
    const startBat = `@echo off
echo Starting TMH Task Dispatcher...
set NODE_ENV=production
set PORT=${intranetConfig.serverPort}
copy .env.intranet .env.local
npm ci --only=production
npm start
`;

    const installBat = `@echo off
echo Installing dependencies...
npm ci --only=production
echo Installation completed!
`;

    // 创建Node.js启动脚本，避免Windows上npm命令的问题
    const startScript = `#!/usr/bin/env node
// TMH任务调度系统启动脚本 - Windows兼容版本
process.env.NODE_ENV = 'production';
process.env.PORT = '${intranetConfig.serverPort}';

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

console.log('🚀 启动TMH任务调度系统...');
console.log('环境:', process.env.NODE_ENV);
console.log('端口:', process.env.PORT);
console.log('平台:', os.platform());

// Windows和Unix平台的Next.js启动方式
let nextCommand, nextArgs;

if (os.platform() === 'win32') {
  // Windows平台：直接使用next的JavaScript入口
  const nextCliPath = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');
  if (require('fs').existsSync(nextCliPath)) {
    nextCommand = 'node';
    nextArgs = [nextCliPath, 'start'];
  } else {
    // 后备方案：使用npm start
    nextCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
    nextArgs = ['start'];
  }
} else {
  // Unix平台：使用标准的next命令
  nextCommand = 'node';
  nextArgs = [path.join(__dirname, 'node_modules', '.bin', 'next'), 'start'];
}

console.log('启动命令:', nextCommand, nextArgs.join(' '));

const child = spawn(nextCommand, nextArgs, {
  stdio: 'inherit',
  env: process.env,
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  console.error('尝试使用备用启动方式...');

  // 备用启动方式
  const fallbackChild = spawn('npm', ['start'], {
    stdio: 'inherit',
    env: process.env,
    cwd: __dirname,
    shell: true
  });

  fallbackChild.on('error', (fallbackError) => {
    console.error('❌ 备用启动也失败:', fallbackError);
    process.exit(1);
  });
});

child.on('exit', (code) => {
  console.log('应用退出，代码:', code);
  process.exit(code);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭...');
  child.kill('SIGTERM');
});
`;

    // 创建PM2生态系统配置文件
    const pm2Config = {
      apps: [{
        name: intranetConfig.serviceName,
        script: './start.js',
        cwd: '.',
        env: {
          NODE_ENV: 'production',
          PORT: intranetConfig.serverPort
        },
        node_args: '--max-old-space-size=2048',
        instances: 1,
        exec_mode: 'fork',
        watch: false,
        max_memory_restart: '2G',
        error_file: './logs/err.log',
        out_file: './logs/out.log',
        log_file: './logs/combined.log',
        time: true
      }]
    };

    const pm2StartBat = `@echo off
set NODE_ENV=production
set PORT=${intranetConfig.serverPort}
copy .env.intranet .env.local

REM 创建日志目录
if not exist logs mkdir logs

REM 启动PM2服务
pm2 start ecosystem.config.js
pm2 save
echo Service started with PM2!
`;

    fs.writeFileSync(path.join(deployDir, 'start.bat'), startBat);
    fs.writeFileSync(path.join(deployDir, 'install.bat'), installBat);
    fs.writeFileSync(path.join(deployDir, 'pm2-start.bat'), pm2StartBat);
    fs.writeFileSync(path.join(deployDir, 'start.js'), startScript);
    fs.writeFileSync(path.join(deployDir, 'ecosystem.config.js'), `module.exports = ${JSON.stringify(pm2Config, null, 2)};`);

    // 创建部署包 - 针对Windows服务器优化
    if (process.platform === 'win32') {
      // Windows环境下优先使用ZIP格式，更兼容Windows服务器
      const zipPackageName = packageName.replace('.tar.gz', '.zip');
      const zipResult = runCommand(`powershell -Command "Compress-Archive -Path '${deployDir}\\*' -DestinationPath '${zipPackageName}' -Force"`, '创建ZIP部署包');

      if (zipResult.success) {
        colorLog('green', `✅ 部署包创建完成: ${zipPackageName}`);
        return { success: true, packageName: zipPackageName, deployDir };
      } else {
        // 如果PowerShell失败，尝试tar
        const tarResult = runCommand(`tar -czf ${packageName} -C ${deployDir} .`, '创建TAR部署包', { silent: true });
        if (tarResult.success) {
          colorLog('green', `✅ 部署包创建完成: ${packageName}`);
          return { success: true, packageName, deployDir };
        } else {
          throw new Error('无法创建部署包：PowerShell和tar都不可用');
        }
      }
    } else {
      // Linux/macOS环境使用tar
      runCommand(`tar -czf ${packageName} -C ${deployDir} .`, '创建部署包');
    }

    colorLog('green', `✅ 部署包创建完成: ${packageName}`);
    return { success: true, packageName, deployDir };
  } catch (error) {
    colorLog('red', `❌ 部署包创建失败: ${error.message}`);
    return { success: false };
  }
}

/**
 * SSH 自动部署到服务器
 */
function deployToServer(packageName) {
  colorLog('blue', '\n🚀 开始 SSH 自动部署...');

  try {
    // 1. 上传部署包
    colorLog('blue', '📤 上传部署包到服务器...');
    const scpCmd = buildSCPCommand(packageName, '~/');
    const uploadResult = runCommand(scpCmd, '上传部署包');

    if (!uploadResult.success) {
      throw new Error('部署包上传失败');
    }

    // 2. 在服务器上执行部署
    colorLog('blue', '⚙️ 在服务器上执行部署...');

    // 停止现有服务
    runSSHCommand('pm2 stop tmh-task-dispatcher', '停止现有服务', { silent: true });

    // 备份现有部署
    const backupPath = `${intranetConfig.deployPath}_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%`;
    runSSHCommand(`if exist "${intranetConfig.deployPath}" move "${intranetConfig.deployPath}" "${backupPath}"`, '备份现有部署', { silent: true });

    // 创建部署目录
    runSSHCommand(`mkdir "${intranetConfig.deployPath}"`, '创建部署目录', { silent: true });

    // 解压部署包 - 在Windows服务器上优先使用PowerShell
    let extractResult;

    if (packageName.endsWith('.zip')) {
      // ZIP文件使用PowerShell解压
      const extractCmd = `powershell -Command "Expand-Archive -Path '~/${packageName}' -DestinationPath '${intranetConfig.deployPath}' -Force"`;
      extractResult = runSSHCommand(extractCmd, '解压部署包');
    } else {
      // TAR.GZ文件先尝试tar，失败则转换为ZIP
      const tarExtractCmd = `tar -xzf "~/${packageName}" -C "${intranetConfig.deployPath}"`;
      extractResult = runSSHCommand(tarExtractCmd, '解压部署包', { silent: true });

      if (!extractResult.success) {
        colorLog('yellow', '⚠️ tar命令不可用，尝试使用PowerShell解压...');

        // 使用PowerShell解压tar.gz文件（需要7-Zip或其他工具）
        const powershellExtractCmd = `powershell -Command "
          if (Get-Command 7z -ErrorAction SilentlyContinue) {
            7z x '~/${packageName}' -so | 7z x -si -ttar -o'${intranetConfig.deployPath}' -y
          } elseif (Get-Command tar -ErrorAction SilentlyContinue) {
            tar -xzf '~/${packageName}' -C '${intranetConfig.deployPath}'
          } else {
            Write-Error 'Neither 7-Zip nor tar is available. Please install 7-Zip or use ZIP format.'
            exit 1
          }
        "`;

        extractResult = runSSHCommand(powershellExtractCmd, '使用PowerShell解压部署包');
      }
    }

    if (!extractResult.success) {
      throw new Error('部署包解压失败');
    }

    // 进入部署目录并安装依赖
    const installResult = runSSHCommand(`cd /d "${intranetConfig.deployPath}" && install.bat`, '安装依赖');
    if (!installResult.success) {
      throw new Error('依赖安装失败');
    }

    // 配置防火墙
    runSSHCommand(`netsh advfirewall firewall delete rule name="TMH Task Dispatcher"`, '清理旧防火墙规则', { silent: true });
    runSSHCommand(`netsh advfirewall firewall add rule name="TMH Task Dispatcher" dir=in action=allow protocol=TCP localport=${intranetConfig.serverPort}`, '配置防火墙');

    // 启动服务
    const startResult = runSSHCommand(`cd /d "${intranetConfig.deployPath}" && pm2-start.bat`, '启动服务');
    if (!startResult.success) {
      throw new Error('服务启动失败');
    }

    // 清理部署包
    runSSHCommand(`del "~/${packageName}"`, '清理部署包', { silent: true });

    colorLog('green', '✅ SSH 自动部署完成');
    return true;

  } catch (error) {
    colorLog('red', `❌ SSH 自动部署失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证部署
 */
function verifyDeployment() {
  colorLog('blue', '\n🔍 验证部署...');

  // 等待服务启动
  colorLog('cyan', '⏳ 等待服务启动...');
  setTimeout(() => {
    // 检查服务状态
    const statusResult = runSSHCommand('pm2 status tmh-task-dispatcher', '检查服务状态', { silent: true });

    if (statusResult.success && statusResult.output && statusResult.output.includes('online')) {
      colorLog('green', '✅ PM2 服务运行正常');
    } else {
      colorLog('yellow', '⚠️ PM2 服务状态异常，请手动检查');
    }

    // 测试 HTTP 响应
    colorLog('cyan', '🌐 测试应用响应...');
    const testUrl = `http://${intranetConfig.serverIP}:${intranetConfig.serverPort}`;

    // 使用 curl 测试（如果可用）
    const curlResult = runCommand(`curl -f -s -o /dev/null -w "%{http_code}" ${testUrl}`, 'HTTP 响应测试', { silent: true });

    if (curlResult.success && curlResult.output && curlResult.output.trim() === '200') {
      colorLog('green', '✅ 应用响应正常');
      colorLog('cyan', `🌐 访问地址: ${testUrl}`);
    } else {
      colorLog('yellow', '⚠️ 应用可能需要更多时间启动，请稍后手动验证');
      colorLog('cyan', `🌐 访问地址: ${testUrl}`);
    }
  }, 10000); // 等待 10 秒
}

/**
 * 生成部署说明
 */
function generateDeploymentInstructions(packageName) {
  const instructions = `
🚀 TMH任务调度系统 - 内网部署说明

📦 部署包: ${packageName}
🖥️  目标服务器: ${intranetConfig.serverIP}
🔌 服务端口: ${intranetConfig.serverPort}

📋 部署步骤:

1. 上传部署包到服务器:
   scp ${packageName} ${intranetConfig.serverUser}@${intranetConfig.serverIP}:~/

2. 登录服务器:
   ssh ${intranetConfig.serverUser}@${intranetConfig.serverIP}

3. 解压部署包 (Windows Server):
   mkdir "${intranetConfig.deployPath}"
   ${packageName.endsWith('.zip')
     ? `powershell -Command "Expand-Archive -Path '~/${packageName}' -DestinationPath '${intranetConfig.deployPath}' -Force"`
     : `# 如果是tar.gz文件，需要安装7-Zip或使用以下命令:
   # 方法1: 使用7-Zip (推荐)
   7z x ~/${packageName} -so | 7z x -si -ttar -o"${intranetConfig.deployPath}" -y

   # 方法2: 如果有tar命令
   tar -xzf ~/${packageName} -C "${intranetConfig.deployPath}"

   # 方法3: 手动转换为ZIP格式后解压
   # 在开发机器上重新运行部署脚本，会自动创建ZIP格式`}

4. 进入部署目录:
   cd /d "${intranetConfig.deployPath}"

5. 安装依赖并启动应用:
   install.bat
   pm2-start.bat

6. 验证部署:
   # 检查PM2服务状态
   pm2 status
   pm2 logs ${intranetConfig.serviceName}

   # 测试HTTP响应
   curl http://localhost:${intranetConfig.serverPort}
   # 或使用PowerShell
   powershell -Command "Invoke-WebRequest -Uri http://localhost:${intranetConfig.serverPort} -UseBasicParsing"

🔧 Windows服务管理:
   # 查看PM2服务
   pm2 status
   pm2 restart ${intranetConfig.serviceName}
   pm2 stop ${intranetConfig.serviceName}

   # 查看实时日志
   pm2 logs ${intranetConfig.serviceName} --lines 50

📊 监控命令:
   pm2 monit
   pm2 logs ${intranetConfig.serviceName} -f

🌐 访问地址:
   http://${intranetConfig.serverIP}:${intranetConfig.serverPort}
`;

  fs.writeFileSync('DEPLOYMENT_INSTRUCTIONS.txt', instructions);
  colorLog('cyan', instructions);
  colorLog('green', '✅ 部署说明已保存到 DEPLOYMENT_INSTRUCTIONS.txt');
}

/**
 * 主部署流程
 */
async function deployToIntranet() {
  colorLog('magenta', '🚀 开始内网部署流程...\n');
  
  try {
    // 1. 检查内网环境
    if (!checkIntranetEnvironment()) {
      process.exit(1);
    }
    
    // 2. 准备构建环境
    if (!prepareBuildEnvironment()) {
      process.exit(1);
    }
    
    // 3. 预部署检查
    colorLog('blue', '\n🔍 执行预部署检查...');
    for (const check of intranetConfig.preDeployChecks) {
      const result = runCommand(check, `预部署检查: ${check}`);
      if (!result.success) {
        colorLog('yellow', '⚠️  预部署检查失败，但继续部署...');
      }
    }
    
    // 4. 构建应用
    if (!buildApplication()) {
      colorLog('red', '❌ 构建失败，终止部署');
      process.exit(1);
    }
    
    // 5. 创建部署包
    const packageResult = createDeploymentPackage();
    if (!packageResult.success) {
      process.exit(1);
    }

    // 6. SSH 自动部署
    if (intranetConfig.useSSH) {
      colorLog('blue', '\n🔄 开始 SSH 自动部署...');
      const deploySuccess = deployToServer(packageResult.packageName);

      if (deploySuccess) {
        colorLog('green', '\n🎉 SSH 自动部署完成！');

        // 验证部署
        verifyDeployment();

        colorLog('cyan', '\n📋 部署摘要:');
        colorLog('cyan', '  ✅ 内网环境检查通过');
        colorLog('cyan', '  ✅ 应用构建成功');
        colorLog('cyan', `  ✅ 部署包创建完成: ${packageResult.packageName}`);
        colorLog('cyan', '  ✅ SSH 自动部署成功');
        colorLog('cyan', `  🌐 访问地址: http://${intranetConfig.serverIP}:${intranetConfig.serverPort}`);

        // 管理命令提示 - 使用PuTTY命令
        colorLog('yellow', '\n🔧 管理命令 (使用PuTTY):');

        try {
          const plinkPath = ensurePuttyInstalled();
          const { serverIP, serverUser, sshPort, serverPassword } = intranetConfig;

          colorLog('yellow', `  查看状态:`);
          colorLog('cyan', `    "${plinkPath}" -ssh -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch ${serverIP} "pm2 status"`);

          colorLog('yellow', `  查看日志:`);
          colorLog('cyan', `    "${plinkPath}" -ssh -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch ${serverIP} "pm2 logs tmh-task-dispatcher"`);

          colorLog('yellow', `  重启服务:`);
          colorLog('cyan', `    "${plinkPath}" -ssh -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch ${serverIP} "pm2 restart tmh-task-dispatcher"`);

          colorLog('yellow', `  停止服务:`);
          colorLog('cyan', `    "${plinkPath}" -ssh -P ${sshPort} -l ${serverUser} -pw "${serverPassword}" -batch ${serverIP} "pm2 stop tmh-task-dispatcher"`);
        } catch (error) {
          colorLog('yellow', `  注意: 请确保PuTTY已安装以使用这些命令`);
        }

      } else {
        colorLog('red', '\n❌ SSH 自动部署失败，生成手动部署说明...');
        generateDeploymentInstructions(packageResult.packageName);
      }
    } else {
      // 7. 生成部署说明（手动部署模式）
      generateDeploymentInstructions(packageResult.packageName);

      colorLog('green', '\n🎉 内网部署包准备完成！');
      colorLog('cyan', '📋 部署摘要:');
      colorLog('cyan', '  ✅ 内网环境检查通过');
      colorLog('cyan', '  ✅ 应用构建成功');
      colorLog('cyan', `  ✅ 部署包创建完成: ${packageResult.packageName}`);
      colorLog('cyan', '  ✅ 部署说明生成完成');
    }

    // 8. 清理临时文件
    const cleanupCmd = process.platform === 'win32'
      ? `rmdir /s /q ${packageResult.deployDir}`
      : `rm -rf ${packageResult.deployDir}`;
    runCommand(cleanupCmd, '清理临时文件');

    // 清理部署包（SSH 部署成功后）
    if (intranetConfig.useSSH && fs.existsSync(packageResult.packageName)) {
      const deleteCmd = process.platform === 'win32'
        ? `del "${packageResult.packageName}"`
        : `rm "${packageResult.packageName}"`;
      runCommand(deleteCmd, '清理部署包');
    }
    
  } finally {
    // 恢复构建环境
    restoreBuildEnvironment();
  }
}

// 执行部署
if (require.main === module) {
  deployToIntranet().catch(error => {
    colorLog('red', `❌ 内网部署失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { deployToIntranet };
