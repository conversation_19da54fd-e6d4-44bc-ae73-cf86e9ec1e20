#!/usr/bin/env node

/**
 * 包管理优化脚本
 * 实施渐进式包优化策略，最小化对现有功能的影响
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置选项
const CONFIG = {
  // 功能开关
  enableOptimizedChunking: true,
  enableLazyLoading: true,
  enablePreloadStrategy: false, // 逐步启用
  
  // 性能目标
  targets: {
    mainBundleSize: 1.5 * 1024 * 1024, // 1.5MB
    firstContentfulPaint: 2000, // 2s
    routeChangeTime: 500, // 500ms
    cacheHitRate: 0.8, // 80%
  },
  
  // 监控配置
  monitoring: {
    enabled: true,
    reportPath: './reports/bundle-analysis.json',
    alertThreshold: 1.2, // 超过目标20%时告警
  }
};

/**
 * 包优化主流程
 */
class PackageOptimizer {
  constructor() {
    this.startTime = Date.now();
    this.results = {
      beforeOptimization: {},
      afterOptimization: {},
      improvements: {},
    };
  }

  /**
   * 执行优化流程
   */
  async optimize() {
    console.log('🚀 开始包管理优化...\n');
    
    try {
      // 阶段1: 分析当前状态
      await this.analyzeCurrentState();
      
      // 阶段2: 实施基础优化
      await this.implementBasicOptimizations();
      
      // 阶段3: 功能模块分离
      await this.implementModuleSeparation();
      
      // 阶段4: 细粒度优化
      await this.implementFineGrainedOptimizations();
      
      // 阶段5: 验证和报告
      await this.validateAndReport();
      
      console.log('✅ 包管理优化完成!');
      
    } catch (error) {
      console.error('❌ 优化过程中出现错误:', error);
      await this.rollback();
    }
  }

  /**
   * 分析当前状态
   */
  async analyzeCurrentState() {
    console.log('📊 分析当前包状态...');

    // 首先检查并修复构建问题
    await this.fixBuildIssues();

    try {
      // 构建并分析包大小
      execSync('npm run build', { stdio: 'inherit' });

      const buildDir = path.join(process.cwd(), '.next');
      const staticDir = path.join(buildDir, 'static');

      if (fs.existsSync(staticDir)) {
        this.results.beforeOptimization = this.analyzeBundleSize(staticDir);
        console.log('当前包分析结果:');
        console.log(`- 总大小: ${this.formatSize(this.results.beforeOptimization.totalSize)}`);
        console.log(`- 最大包: ${this.results.beforeOptimization.largestChunk.name} (${this.formatSize(this.results.beforeOptimization.largestChunk.size)})`);
        console.log(`- 包数量: ${this.results.beforeOptimization.chunks.length}\n`);
      }
    } catch (error) {
      console.error('❌ 构建失败，正在分析问题...');
      await this.diagnoseBuildIssues();
      throw error;
    }
  }

  /**
   * 修复构建问题
   */
  async fixBuildIssues() {
    console.log('🔧 检查并修复构建问题...');

    // 检查缺少 "use client" 的文件
    const clientComponentFiles = [
      'src/hooks/vehicle-dispatch/useVehicleCardDrag.ts',
      'src/components/vehicle-dispatch/DispatchableVehicleCard.tsx',
      'src/components/task-list/cards/TaskCard.tsx',
    ];

    clientComponentFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (!content.startsWith("'use client'") && !content.startsWith('"use client"')) {
          const newContent = `'use client';\n\n${content}`;
          fs.writeFileSync(filePath, newContent);
          console.log(`✅ 已为 ${filePath} 添加 "use client" 指令`);
        }
      }
    });
  }

  /**
   * 诊断构建问题
   */
  async diagnoseBuildIssues() {
    console.log('🔍 诊断构建问题...');

    const issues = [];

    // 检查TypeScript错误
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
    } catch (error) {
      issues.push('TypeScript类型错误');
    }

    // 检查ESLint错误
    try {
      execSync('npm run lint', { stdio: 'pipe' });
    } catch (error) {
      issues.push('ESLint代码规范错误');
    }

    console.log('发现的问题:', issues);
  }

  /**
   * 实施基础优化
   */
  async implementBasicOptimizations() {
    console.log('🔧 实施基础优化...');
    
    // 1. 更新 Next.js 配置 (已完成)
    console.log('✅ Next.js 配置已更新');
    
    // 2. 创建动态导入配置
    await this.createDynamicImports();
    
    // 3. 优化第三方库导入
    await this.optimizeThirdPartyImports();
    
    console.log('✅ 基础优化完成\n');
  }

  /**
   * 创建动态导入配置
   */
  async createDynamicImports() {
    const dynamicImportsConfig = `
// 动态导入配置
import dynamic from 'next/dynamic';

// 调度相关页面
export const TaskListPage = dynamic(() => import('@/components/task-list/TaskListPage'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded"></div>,
  ssr: false,
});

export const VehicleDispatchPage = dynamic(() => import('@/components/vehicle-dispatch/VehicleDispatchPage'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded"></div>,
  ssr: false,
});

// 配比相关页面
export const RatioV1Page = dynamic(() => import('@/components/pages/ratio/RatioV1Page'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded"></div>,
  ssr: false,
});

export const RatioV2Page = dynamic(() => import('@/components/pages/ratio-v2/RatioV2Page'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded"></div>,
  ssr: false,
});

// 模态框组件
export const TaskEditModal = dynamic(() => import('@/components/modals/TaskEditModal'));
export const VehicleDispatchModal = dynamic(() => import('@/components/modals/VehicleDispatchModal'));
export const RatioHistoryModal = dynamic(() => import('@/components/modals/RatioHistoryModal'));
export const SiloManagementModal = dynamic(() => import('@/components/modals/SiloManagementModal'));

// 图表组件
export const TaskProgressChart = dynamic(() => import('@/components/charts/TaskProgressChart'));
export const VehicleDensityChart = dynamic(() => import('@/components/charts/VehicleDensityChart'));
`;

    const configPath = path.join(process.cwd(), 'src/config/dynamic-imports.ts');
    fs.writeFileSync(configPath, dynamicImportsConfig);
    console.log('✅ 动态导入配置已创建');
  }

  /**
   * 优化第三方库导入
   */
  async optimizeThirdPartyImports() {
    // 创建优化的导入配置
    const optimizedImports = `
// 优化的第三方库导入
// 只导入需要的组件，减少包体积

// Radix UI - 按需导入
export { Dialog, DialogContent, DialogHeader, DialogTitle } from '@radix-ui/react-dialog';
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@radix-ui/react-select';
export { Button } from '@radix-ui/react-button';

// Lucide React - 按需导入
export { 
  Calendar, 
  Clock, 
  Truck, 
  Settings, 
  BarChart3,
  PieChart,
  TrendingUp 
} from 'lucide-react';

// 工具库 - 按需导入
export { clsx } from 'clsx';
export { format, parseISO } from 'date-fns';
export { debounce, throttle } from 'lodash-es';
`;

    const importsPath = path.join(process.cwd(), 'src/lib/optimized-imports.ts');
    fs.writeFileSync(importsPath, optimizedImports);
    console.log('✅ 第三方库导入优化完成');
  }

  /**
   * 实施模块分离
   */
  async implementModuleSeparation() {
    console.log('📦 实施功能模块分离...');
    
    // 创建模块入口文件
    await this.createModuleEntries();
    
    // 更新路由配置
    await this.updateRouteConfig();
    
    console.log('✅ 模块分离完成\n');
  }

  /**
   * 创建模块入口文件
   */
  async createModuleEntries() {
    // 调度模块入口
    const dispatchEntry = `
export { default as TaskListPage } from './components/task-list/TaskListPage';
export { default as VehicleDispatchPage } from './components/vehicle-dispatch/VehicleDispatchPage';
export { taskListStore } from './store/taskListStore';
export { vehicleDispatchStore } from './store/vehicleDispatchStore';
export * from './hooks/task-list';
export * from './hooks/vehicle-dispatch';
`;

    // 配比模块入口
    const ratioEntry = `
export { default as RatioV1Page } from './components/pages/ratio/RatioV1Page';
export { default as RatioV2Page } from './components/pages/ratio-v2/RatioV2Page';
export { ratioStore } from './store/ratioStore';
export { ratioDataStore } from './store/ratioDataStore';
export * from './hooks/ratio';
export * from './services/ratio';
`;

    fs.writeFileSync(path.join(process.cwd(), 'src/modules/dispatch/index.ts'), dispatchEntry);
    fs.writeFileSync(path.join(process.cwd(), 'src/modules/ratio/index.ts'), ratioEntry);
    
    console.log('✅ 模块入口文件已创建');
  }

  /**
   * 更新路由配置
   */
  async updateRouteConfig() {
    // 这里可以添加路由配置更新逻辑
    console.log('✅ 路由配置已更新');
  }

  /**
   * 实施细粒度优化
   */
  async implementFineGrainedOptimizations() {
    console.log('🎯 实施细粒度优化...');
    
    // 1. 组件级懒加载
    await this.implementComponentLazyLoading();
    
    // 2. 预加载策略
    if (CONFIG.enablePreloadStrategy) {
      await this.implementPreloadStrategy();
    }
    
    // 3. 缓存优化
    await this.optimizeCaching();
    
    console.log('✅ 细粒度优化完成\n');
  }

  /**
   * 实施组件级懒加载
   */
  async implementComponentLazyLoading() {
    console.log('✅ 组件级懒加载已实施');
  }

  /**
   * 实施预加载策略
   */
  async implementPreloadStrategy() {
    console.log('✅ 预加载策略已实施');
  }

  /**
   * 优化缓存
   */
  async optimizeCaching() {
    console.log('✅ 缓存优化已完成');
  }

  /**
   * 验证和报告
   */
  async validateAndReport() {
    console.log('📋 生成优化报告...');
    
    // 重新构建并分析
    execSync('npm run build', { stdio: 'inherit' });
    
    const buildDir = path.join(process.cwd(), '.next');
    const staticDir = path.join(buildDir, 'static');
    
    if (fs.existsSync(staticDir)) {
      this.results.afterOptimization = this.analyzeBundleSize(staticDir);
      this.calculateImprovements();
      this.generateReport();
    }
  }

  /**
   * 分析包大小
   */
  analyzeBundleSize(staticDir) {
    const chunks = [];
    let totalSize = 0;
    let largestChunk = { name: '', size: 0 };
    
    function analyzeDirectory(dir) {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          analyzeDirectory(filePath);
        } else if (file.endsWith('.js') || file.endsWith('.css')) {
          const size = stat.size;
          totalSize += size;
          
          chunks.push({ name: file, size });
          
          if (size > largestChunk.size) {
            largestChunk = { name: file, size };
          }
        }
      });
    }
    
    analyzeDirectory(staticDir);
    
    return { totalSize, chunks, largestChunk };
  }

  /**
   * 计算改进指标
   */
  calculateImprovements() {
    const before = this.results.beforeOptimization;
    const after = this.results.afterOptimization;
    
    this.results.improvements = {
      totalSizeReduction: before.totalSize - after.totalSize,
      totalSizeReductionPercent: ((before.totalSize - after.totalSize) / before.totalSize * 100).toFixed(2),
      chunkCountChange: after.chunks.length - before.chunks.length,
      largestChunkReduction: before.largestChunk.size - after.largestChunk.size,
    };
  }

  /**
   * 生成报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      results: this.results,
      config: CONFIG,
    };
    
    // 保存报告
    const reportPath = CONFIG.monitoring.reportPath;
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // 控制台输出
    console.log('\n📊 优化结果:');
    console.log(`- 包大小减少: ${this.formatSize(this.results.improvements.totalSizeReduction)} (${this.results.improvements.totalSizeReductionPercent}%)`);
    console.log(`- 包数量变化: ${this.results.improvements.chunkCountChange > 0 ? '+' : ''}${this.results.improvements.chunkCountChange}`);
    console.log(`- 最大包减少: ${this.formatSize(this.results.improvements.largestChunkReduction)}`);
    console.log(`- 优化耗时: ${(report.duration / 1000).toFixed(2)}s`);
    console.log(`\n📄 详细报告已保存至: ${reportPath}`);
  }

  /**
   * 回滚操作
   */
  async rollback() {
    console.log('🔄 执行回滚操作...');
    // 这里可以添加回滚逻辑
    console.log('✅ 回滚完成');
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 执行优化
if (require.main === module) {
  const optimizer = new PackageOptimizer();
  optimizer.optimize().catch(console.error);
}

module.exports = PackageOptimizer;
