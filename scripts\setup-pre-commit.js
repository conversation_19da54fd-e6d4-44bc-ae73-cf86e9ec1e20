#!/usr/bin/env node

/**
 * Pre-commit钩子设置脚本
 * 自动设置Git pre-commit钩子，确保代码质量
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Pre-commit钩子脚本内容
const preCommitHookContent = `#!/bin/sh
#
# TMH任务调度系统 Pre-commit钩子
# 在提交前自动运行代码质量检查和修复
#

echo "🔍 运行pre-commit检查..."

# 检查是否有暂存的文件
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E "\\.(ts|tsx)$")

if [ -z "$STAGED_FILES" ]; then
  echo "✅ 没有TypeScript文件需要检查"
  exit 0
fi

echo "📁 发现 $(echo "$STAGED_FILES" | wc -l) 个TypeScript文件需要检查"

# 运行ESLint检查和自动修复
echo "🔧 运行ESLint检查..."
npx eslint $STAGED_FILES --fix --max-warnings 50

if [ $? -ne 0 ]; then
  echo "❌ ESLint检查失败，请修复错误后重新提交"
  exit 1
fi

# 运行TypeScript类型检查
echo "🔍 运行TypeScript类型检查..."
npx tsc --noEmit

if [ $? -ne 0 ]; then
  echo "❌ TypeScript类型检查失败，请修复类型错误后重新提交"
  exit 1
fi

# 运行Prettier格式化
echo "💅 运行Prettier格式化..."
npx prettier --write $STAGED_FILES

if [ $? -ne 0 ]; then
  echo "❌ Prettier格式化失败"
  exit 1
fi

# 重新添加格式化后的文件到暂存区
echo "📝 重新添加格式化后的文件..."
git add $STAGED_FILES

# 运行简单的构建检查（可选，较慢）
# echo "🏗️ 运行构建检查..."
# npm run build > /dev/null 2>&1
# 
# if [ $? -ne 0 ]; then
#   echo "❌ 构建检查失败，请修复构建错误后重新提交"
#   exit 1
# fi

echo "✅ Pre-commit检查通过，准备提交..."
exit 0
`;

// Package.json脚本更新
const packageJsonScripts = {
  "pre-commit": "node scripts/setup-pre-commit.js",
  "pre-commit:fix": "eslint src --fix --ext .ts,.tsx && prettier --write \"src/**/*.{ts,tsx}\"",
  "pre-commit:check": "eslint src --ext .ts,.tsx --max-warnings 50 && tsc --noEmit",
  "quality:check": "npm run pre-commit:check",
  "quality:fix": "npm run pre-commit:fix",
  "quality:optimize": "node scripts/code-quality-optimizer.js",
};

/**
 * 检查Git仓库
 */
function checkGitRepository() {
  try {
    execSync('git rev-parse --git-dir', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 创建pre-commit钩子
 */
function createPreCommitHook() {
  const gitHooksDir = path.join(process.cwd(), '.git', 'hooks');
  const preCommitPath = path.join(gitHooksDir, 'pre-commit');
  
  // 确保hooks目录存在
  if (!fs.existsSync(gitHooksDir)) {
    fs.mkdirSync(gitHooksDir, { recursive: true });
  }
  
  // 写入pre-commit钩子
  fs.writeFileSync(preCommitPath, preCommitHookContent, { mode: 0o755 });
  
  colorLog('green', `✅ Pre-commit钩子已创建: ${preCommitPath}`);
}

/**
 * 更新package.json脚本
 */
function updatePackageJsonScripts() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    colorLog('yellow', '⚠️ 未找到package.json文件');
    return;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 确保scripts字段存在
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    // 添加新的脚本
    Object.assign(packageJson.scripts, packageJsonScripts);
    
    // 写回package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
    colorLog('green', '✅ Package.json脚本已更新');
    
  } catch (error) {
    colorLog('yellow', `⚠️ 更新package.json失败: ${error.message}`);
  }
}

/**
 * 安装必要的依赖
 */
function installDependencies() {
  colorLog('blue', '📦 检查必要的依赖...');
  
  const requiredDeps = [
    '@typescript-eslint/eslint-plugin',
    '@typescript-eslint/parser',
    'eslint',
    'prettier',
    'typescript',
  ];
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies,
    };
    
    const missingDeps = requiredDeps.filter(dep => !allDeps[dep]);
    
    if (missingDeps.length > 0) {
      colorLog('yellow', `⚠️ 缺少依赖: ${missingDeps.join(', ')}`);
      colorLog('blue', '请运行: npm install --save-dev ' + missingDeps.join(' '));
    } else {
      colorLog('green', '✅ 所有必要依赖已安装');
    }
    
  } catch (error) {
    colorLog('yellow', `⚠️ 检查依赖失败: ${error.message}`);
  }
}

/**
 * 创建ESLint配置文件（如果不存在）
 */
function createESLintConfig() {
  const eslintConfigPath = path.join(process.cwd(), '.eslintrc.js');
  
  if (fs.existsSync(eslintConfigPath)) {
    colorLog('green', '✅ ESLint配置文件已存在');
    return;
  }
  
  const eslintConfig = `module.exports = {
  extends: [
    'next/core-web-vitals',
    '@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  rules: {
    // 警告级别的规则
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    'react-hooks/exhaustive-deps': 'warn',
    
    // 错误级别的规则
    '@typescript-eslint/no-var-requires': 'error',
    'no-console': 'off', // 开发阶段允许console
    
    // 代码风格
    'prefer-const': 'warn',
    'no-var': 'error',
  },
  ignorePatterns: [
    'node_modules/',
    '.next/',
    'out/',
    'build/',
    'dist/',
  ],
};
`;
  
  fs.writeFileSync(eslintConfigPath, eslintConfig);
  colorLog('green', '✅ ESLint配置文件已创建');
}

/**
 * 创建Prettier配置文件（如果不存在）
 */
function createPrettierConfig() {
  const prettierConfigPath = path.join(process.cwd(), '.prettierrc.js');
  
  if (fs.existsSync(prettierConfigPath)) {
    colorLog('green', '✅ Prettier配置文件已存在');
    return;
  }
  
  const prettierConfig = `module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid',
  endOfLine: 'lf',
};
`;
  
  fs.writeFileSync(prettierConfigPath, prettierConfig);
  colorLog('green', '✅ Prettier配置文件已创建');
}

/**
 * 测试pre-commit钩子
 */
function testPreCommitHook() {
  colorLog('blue', '🧪 测试pre-commit钩子...');
  
  try {
    // 检查是否有暂存的文件
    const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' }).trim();
    
    if (!stagedFiles) {
      colorLog('yellow', '⚠️ 没有暂存的文件，无法测试pre-commit钩子');
      colorLog('blue', '💡 提示: 可以修改一个文件并使用 git add 来测试');
      return;
    }
    
    // 运行pre-commit检查
    execSync('npm run pre-commit:check', { stdio: 'pipe' });
    colorLog('green', '✅ Pre-commit钩子测试通过');
    
  } catch (error) {
    colorLog('yellow', '⚠️ Pre-commit钩子测试发现问题，这是正常的');
    colorLog('blue', '💡 钩子会在实际提交时自动修复大部分问题');
  }
}

/**
 * 主函数
 */
function main() {
  colorLog('cyan', '\n🚀 设置Pre-commit钩子...\n');
  
  // 检查Git仓库
  if (!checkGitRepository()) {
    colorLog('yellow', '⚠️ 当前目录不是Git仓库，跳过pre-commit钩子设置');
    return;
  }
  
  try {
    // 创建pre-commit钩子
    createPreCommitHook();
    
    // 更新package.json脚本
    updatePackageJsonScripts();
    
    // 检查依赖
    installDependencies();
    
    // 创建配置文件
    createESLintConfig();
    createPrettierConfig();
    
    // 测试钩子
    testPreCommitHook();
    
    // 显示使用说明
    colorLog('cyan', '\n📋 Pre-commit钩子设置完成!\n');
    colorLog('green', '✅ 功能说明:');
    console.log('  - 提交前自动运行ESLint检查和修复');
    console.log('  - 自动运行TypeScript类型检查');
    console.log('  - 自动运行Prettier代码格式化');
    console.log('  - 阻止有严重错误的代码提交');
    
    colorLog('blue', '\n💡 可用的npm脚本:');
    console.log('  - npm run pre-commit:fix    # 手动修复代码质量问题');
    console.log('  - npm run pre-commit:check  # 手动检查代码质量');
    console.log('  - npm run quality:optimize  # 运行代码质量优化');
    
    colorLog('yellow', '\n⚠️ 注意事项:');
    console.log('  - 首次提交可能需要较长时间');
    console.log('  - 钩子会自动修复大部分问题');
    console.log('  - 严重错误需要手动修复');
    
  } catch (error) {
    colorLog('red', `\n💥 设置失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
main();
