#!/usr/bin/env node

/**
 * TMH任务调度系统 - SSH配置脚本
 * 整合版本，支持密码自动配置和密钥认证
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '..', '.env.deploy');
const SSH_DIR = path.join(os.homedir(), '.ssh');
const KEY_PATH = path.join(SSH_DIR, 'id_rsa');
const PUB_KEY_PATH = `${KEY_PATH}.pub`;
const CONFIG_PATH = path.join(SSH_DIR, 'config');

// 默认配置
const DEFAULT_CONFIG = {
  SERVER_IP: '*************',
  SERVER_USER: 'administrator',
  SERVER_PASSWORD: '13834567299Goodbye!',
  SSH_PORT: '22',
  DEPLOY_PATH: 'C:\\inetpub\\tmh-task-dispatcher',
  SERVICE_NAME: 'tmh-task-dispatcher',
  SERVER_PORT: '9001'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 加载配置
 */
function loadConfig() {
  let config = { ...DEFAULT_CONFIG };
  
  if (fs.existsSync(CONFIG_FILE)) {
    try {
      const envContent = fs.readFileSync(CONFIG_FILE, 'utf8');
      const envLines = envContent.split('\n');
      
      envLines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          config[key.trim()] = value.trim().replace(/['"]/g, '');
        }
      });
      
      colorLog('green', '✅ 配置文件加载成功');
    } catch (error) {
      colorLog('yellow', '⚠️ 配置文件读取失败，使用默认配置');
    }
  } else {
    // 创建默认配置文件
    const envContent = Object.entries(DEFAULT_CONFIG)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(CONFIG_FILE, envContent);
    colorLog('cyan', `📝 已创建配置文件: ${CONFIG_FILE}`);
  }
  
  return config;
}

/**
 * 创建SSH目录
 */
function ensureSSHDir() {
  if (!fs.existsSync(SSH_DIR)) {
    fs.mkdirSync(SSH_DIR, { mode: 0o700 });
    colorLog('cyan', `📁 创建SSH目录: ${SSH_DIR}`);
  }
}

/**
 * 生成SSH密钥
 */
function generateSSHKey(config) {
  if (fs.existsSync(KEY_PATH)) {
    colorLog('green', '✅ SSH密钥已存在');
    return true;
  }
  
  try {
    colorLog('blue', '🔑 生成SSH密钥对...');
    execSync(`ssh-keygen -t rsa -b 4096 -f "${KEY_PATH}" -N "" -C "tmh-deploy@${os.hostname()}"`, {
      stdio: 'pipe'
    });
    colorLog('green', '✅ SSH密钥生成成功');
    return true;
  } catch (error) {
    colorLog('red', `❌ SSH密钥生成失败: ${error.message}`);
    return false;
  }
}

/**
 * 创建SSH配置
 */
function createSSHConfig(config) {
  const sshConfig = `
Host tmh-server
    HostName ${config.SERVER_IP}
    User ${config.SERVER_USER}
    Port ${config.SSH_PORT}
    IdentityFile ${KEY_PATH}
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
`;

  fs.writeFileSync(CONFIG_PATH, sshConfig.trim());
  colorLog('green', '✅ SSH配置文件已创建');
}

/**
 * 使用sshpass自动上传公钥
 */
function uploadPublicKey(config) {
  if (!fs.existsSync(PUB_KEY_PATH)) {
    colorLog('red', '❌ 公钥文件不存在');
    return false;
  }
  
  const publicKey = fs.readFileSync(PUB_KEY_PATH, 'utf8').trim();
  
  try {
    colorLog('blue', '🚀 上传公钥到服务器...');
    
    // 使用expect脚本自动输入密码
    const expectScript = `
spawn ssh -o StrictHostKeyChecking=no ${config.SERVER_USER}@${config.SERVER_IP} "mkdir -p ~/.ssh && echo '${publicKey}' >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"
expect "password:"
send "${config.SERVER_PASSWORD}\\r"
expect eof
`;
    
    const tempScript = path.join(os.tmpdir(), 'ssh_upload.exp');
    fs.writeFileSync(tempScript, expectScript);
    
    try {
      execSync(`expect "${tempScript}"`, { stdio: 'pipe' });
      fs.unlinkSync(tempScript);
      colorLog('green', '✅ 公钥上传成功');
      return true;
    } catch (expectError) {
      fs.unlinkSync(tempScript);
      
      // 如果expect不可用，尝试使用sshpass
      try {
        execSync(`sshpass -p "${config.SERVER_PASSWORD}" ssh-copy-id -o StrictHostKeyChecking=no -i "${PUB_KEY_PATH}" ${config.SERVER_USER}@${config.SERVER_IP}`, {
          stdio: 'pipe'
        });
        colorLog('green', '✅ 公钥上传成功 (sshpass)');
        return true;
      } catch (sshpassError) {
        colorLog('yellow', '⚠️ 自动上传失败，需要手动配置');
        return false;
      }
    }
  } catch (error) {
    colorLog('red', `❌ 公钥上传失败: ${error.message}`);
    return false;
  }
}

/**
 * 手动配置指导
 */
function showManualInstructions(config) {
  const publicKey = fs.readFileSync(PUB_KEY_PATH, 'utf8').trim();
  
  colorLog('yellow', '\n📋 请手动执行以下步骤:');
  colorLog('cyan', '\n1. 复制公钥内容:');
  console.log('----------------------------------------');
  console.log(publicKey);
  console.log('----------------------------------------');
  
  colorLog('cyan', '\n2. 登录服务器:');
  colorLog('white', `   ssh ${config.SERVER_USER}@${config.SERVER_IP}`);
  colorLog('white', `   密码: ${config.SERVER_PASSWORD}`);
  
  colorLog('cyan', '\n3. 在服务器上执行:');
  colorLog('white', '   mkdir -p ~/.ssh');
  colorLog('white', `   echo "${publicKey}" >> ~/.ssh/authorized_keys`);
  colorLog('white', '   chmod 700 ~/.ssh');
  colorLog('white', '   chmod 600 ~/.ssh/authorized_keys');
}

/**
 * 测试SSH连接
 */
function testSSHConnection() {
  try {
    colorLog('blue', '🔍 测试SSH连接...');
    execSync('ssh -o BatchMode=yes -o ConnectTimeout=10 tmh-server "echo SSH连接成功"', {
      stdio: 'pipe'
    });
    colorLog('green', '✅ SSH免密登录测试成功');
    return true;
  } catch (error) {
    colorLog('red', '❌ SSH连接测试失败');
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  colorLog('green', '🔐 TMH任务调度系统 - SSH配置');
  colorLog('cyan', '🎯 目标: 配置免密码SSH登录');
  
  // 加载配置
  const config = loadConfig();
  colorLog('cyan', `🖥️  目标服务器: ${config.SERVER_USER}@${config.SERVER_IP}`);
  
  // 创建SSH目录
  ensureSSHDir();
  
  // 生成SSH密钥
  if (!generateSSHKey(config)) {
    process.exit(1);
  }
  
  // 创建SSH配置
  createSSHConfig(config);
  
  // 上传公钥
  const uploadSuccess = uploadPublicKey(config);
  
  if (!uploadSuccess) {
    showManualInstructions(config);
    
    // 等待用户手动配置
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('\n手动配置完成后按回车继续...', () => {
      rl.close();
      
      // 测试连接
      if (testSSHConnection()) {
        colorLog('green', '\n🎉 SSH配置完成!');
        showUsageInstructions(config);
      } else {
        colorLog('red', '\n❌ SSH配置失败，请检查配置');
        process.exit(1);
      }
    });
  } else {
    // 测试连接
    if (testSSHConnection()) {
      colorLog('green', '\n🎉 SSH配置完成!');
      showUsageInstructions(config);
    } else {
      colorLog('red', '\n❌ SSH配置失败，请检查配置');
      process.exit(1);
    }
  }
}

/**
 * 显示使用说明
 */
function showUsageInstructions(config) {
  colorLog('cyan', '\n📋 现在可以使用:');
  colorLog('white', '1. 免密登录: ssh tmh-server');
  colorLog('white', '2. 自动部署: npm run deploy:intranet');
  colorLog('white', `3. 访问应用: http://${config.SERVER_IP}:${config.SERVER_PORT}`);
  colorLog('green', '\n✅ 享受自动化部署吧！');
}

if (require.main === module) {
  main();
}

module.exports = { loadConfig };
