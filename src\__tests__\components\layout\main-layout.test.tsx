/**
 * MainLayout组件单元测试
 */

import { MainLayout } from '@/shared/components/layout/main-layout';
import { QueryClient } from '@tanstack/react-query';
import { screen, waitFor } from '@testing-library/react';
import {
  createMockPlants,
  createMockTasks,
  createMockVehicles,
} from '../../factories/test-data-factory.factory';
import { cleanupMocks, mockLocalStorage, render } from '../../utils/test-utils.helper';

// Mock dataService
jest.mock('@/services/dataService', () => ({
  getStaticPlants: jest.fn(),
}));

// Mock store
jest.mock('@/store/appStore', () => ({
  useAppStore: jest.fn(),
}));

jest.mock('@/store/uiStore', () => ({
  useUiStore: jest.fn(),
  initializeUiStoreWithPlantData: jest.fn(),
}));

import { getStaticPlants } from '@/shared/services/dataService';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import {
  initializeUiStoreWithPlantData,
  useUiStore,
} from '@/infrastructure/storage/stores/uiStore';

const mockGetStaticPlants = getStaticPlants as jest.MockedFunction<typeof getStaticPlants>;
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>;
const mockUseUiStore = useUiStore as jest.MockedFunction<typeof useUiStore>;
const mockInitializeUiStoreWithPlantData = initializeUiStoreWithPlantData as jest.MockedFunction<
  typeof initializeUiStoreWithPlantData
>;

// Mock functions
const mockFetchInitialData = jest.fn();
const mockDispatchVehicleToTask = jest.fn();
const mockCancelVehicleDispatch = jest.fn();
const mockConfirmCrossPlantDispatch = jest.fn();

describe('MainLayout', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
        mutations: { retry: false },
      },
    });

    mockLocalStorage();

    // 设置默认的store mock返回值
    const mockPlants = createMockPlants(2);
    const mockTasks = createMockTasks(5);
    const mockVehicles = createMockVehicles(3);

    // 确保vehicles是数组格式
    mockUseAppStore.mockImplementation(selector => {
      const state = {
        plants: mockPlants,
        tasks: mockTasks,
        vehicles: mockVehicles,
        deliveryOrders: [],
        selectedPlantId: null,
        setPlants: jest.fn(),
        setTasks: jest.fn(),
        setVehicles: jest.fn(),
        setDeliveryOrders: jest.fn(),
        setSelectedPlantId: jest.fn(),
        setMockTasks: jest.fn(),
        updateTask: jest.fn(),
        addTask: jest.fn(),
        deleteTask: jest.fn(),
        updateVehicle: jest.fn(),
        addVehicle: jest.fn(),
        deleteVehicle: jest.fn(),
        fetchInitialData: mockFetchInitialData,
        dispatchVehicleToTask: mockDispatchVehicleToTask,
        cancelVehicleDispatch: mockCancelVehicleDispatch,
        confirmCrossPlantDispatch: mockConfirmCrossPlantDispatch,
        reorderVehiclesInList: jest.fn(),
        _workerApi: null,
        _globalTimerId: null,
        _visibilityChangeHandler: jest.fn(),
        _handleVisibilityChange: jest.fn(),
        _startGlobalTimer: jest.fn(),
        _stopGlobalTimer: jest.fn(),
        _cleanup: jest.fn(),
      };

      // 确保selector是函数
      if (typeof selector === 'function') {
        return selector(state as any);
      }
      return state;
    });

    mockUseUiStore.mockReturnValue({
      selectedPlantId: null,
      setSelectedPlantId: jest.fn(),
      leftPanelCollapsed: false,
      rightPanelCollapsed: false,
      setLeftPanelCollapsed: jest.fn(),
      setRightPanelCollapsed: jest.fn(),
      vehicleDisplayMode: 'normal',
      setVehicleDisplayMode: jest.fn(),
    } as any);
  });

  afterEach(() => {
    cleanupMocks();
    queryClient.clear();
  });

  describe('渲染测试', () => {
    it('应该正确渲染基本布局结构', async () => {
      const mockPlants = createMockPlants(2);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      // 等待数据加载完成
      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });

    it('应该在没有初始数据时显示加载状态', () => {
      mockGetStaticPlants.mockImplementation(() => new Promise(() => {})); // 永不resolve

      render(<MainLayout />);

      // 应该显示加载指示器
      expect(screen.getByText('加载厂区信息...')).toBeInTheDocument();
    });

    it('应该正确处理初始搅拌站数据', async () => {
      const mockPlants = createMockPlants(2);
      const initialPlants = mockPlants;

      render(<MainLayout initialPlants={initialPlants} />);

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });
  });

  describe('数据获取测试', () => {
    it('应该在组件挂载时获取搅拌站数据', async () => {
      const mockPlants = createMockPlants(2);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalledTimes(1);
      });
    });

    it('应该处理数据获取错误', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockGetStaticPlants.mockRejectedValue(new Error('API Error'));

      render(<MainLayout />);

      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      consoleErrorSpy.mockRestore();
    });

    it('应该在有初始数据时仍然获取最新数据', async () => {
      const mockPlants = createMockPlants(2);
      const initialPlants = createMockPlants(1);

      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout initialPlants={initialPlants} />);

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });
  });

  describe('响应式布局测试', () => {
    it('应该在移动设备上正确调整布局', async () => {
      // Mock移动设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      const mockPlants = createMockPlants(1);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      // 等待数据加载完成
      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });

    it('应该在桌面设备上显示完整布局', async () => {
      // Mock桌面设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      const mockPlants = createMockPlants(1);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      // 等待数据加载完成
      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });
  });

  describe('面板折叠功能测试', () => {
    it('应该支持左侧面板折叠', async () => {
      const setLeftPanelCollapsed = jest.fn();
      mockUseUiStore.mockReturnValue({
        selectedPlantId: null,
        setSelectedPlantId: jest.fn(),
        leftPanelCollapsed: false,
        rightPanelCollapsed: false,
        setLeftPanelCollapsed,
        setRightPanelCollapsed: jest.fn(),
        vehicleDisplayMode: 'normal',
        setVehicleDisplayMode: jest.fn(),
      } as any);

      const mockPlants = createMockPlants(1);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      // 等待数据加载完成
      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });

    it('应该支持右侧面板折叠', async () => {
      const setRightPanelCollapsed = jest.fn();
      mockUseUiStore.mockReturnValue({
        selectedPlantId: null,
        setSelectedPlantId: jest.fn(),
        leftPanelCollapsed: false,
        rightPanelCollapsed: false,
        setLeftPanelCollapsed: jest.fn(),
        setRightPanelCollapsed,
        vehicleDisplayMode: 'normal',
        setVehicleDisplayMode: jest.fn(),
      } as any);

      const mockPlants = createMockPlants(1);
      mockGetStaticPlants.mockResolvedValue(mockPlants);

      render(<MainLayout />);

      // 等待数据加载完成
      await waitFor(() => {
        expect(mockGetStaticPlants).toHaveBeenCalled();
      });

      // 检查布局容器是否存在
      expect(screen.getByTestId('test-providers')).toBeDefined();
    });
  });

  describe('错误边界测试', () => {
    it('应该优雅地处理子组件错误', () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // 这里需要测试错误边界的具体实现
      expect(() => {
        render(<MainLayout />);
      }).not.toThrow();

      consoleErrorSpy.mockRestore();
    });
  });
});
