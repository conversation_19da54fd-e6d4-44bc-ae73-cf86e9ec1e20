/**
 * 配比 AI 生成 API 集成测试
 * 测试 /api/ratio/ai-generate 端点的功能
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
// Mock createMocks function
const createMocks = jest.fn();

// Mock the API handler since it doesn't exist yet
const handler = jest.fn();

// 定义全局测试数据
const validRequest = {
  targetStrength: 'C30',
  environment: '一般环境',
  costLevel: 'medium',
  specialRequirements: ['高强度'],
  selectedMaterials: ['水泥', '砂', '石', '水'],
  additionalParams: {
    slump: 120,
    temperature: 20,
  },
  availableMaterials: [
    { id: 'cement-1', name: '水泥', type: 'cement' },
    { id: 'sand-1', name: '砂', type: 'fine-aggregate' },
    { id: 'gravel-1', name: '石', type: 'coarse-aggregate' },
    { id: 'water-1', name: '水', type: 'water' },
  ],
};

describe('/api/ratio/ai-generate API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/ratio/ai-generate', () => {
    it('应该成功生成配比', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: true,
        message: expect.any(String),
        timestamp: expect.any(String),
        data: {
          ratioName: expect.any(String),
          description: expect.any(String),
          materials: expect.any(Array),
          calculationParams: expect.any(Object),
          calculationResults: expect.any(Object),
        },
      });
    });

    it('应该返回正确的材料配比', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const materials = data.data.materials;

      expect(materials).toBeInstanceOf(Array);
      expect(materials.length).toBeGreaterThan(0);

      // 验证材料结构
      materials.forEach((material: any) => {
        expect(material).toMatchObject({
          id: expect.any(String),
          materialId: expect.any(String),
          name: expect.any(String),
          category: expect.any(String),
          amount: expect.any(Number),
          unit: expect.any(String),
          specification: expect.any(String),
        });
      });

      // 验证基本材料存在
      const materialNames = materials.map((m: any) => m.name);
      expect(materialNames).toContain('水泥');
      expect(materialNames).toContain('水');
    });

    it('应该返回合理的计算参数', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const params = data.data.calculationParams;

      expect(params).toMatchObject({
        density: expect.any(Number),
        waterCementRatio: expect.any(Number),
        sandRatio: expect.any(Number),
        targetStrength: expect.any(Number),
        slump: expect.any(Number),
        airContent: expect.any(Number),
      });

      // 验证参数范围
      expect(params.waterCementRatio).toBeGreaterThan(0.2);
      expect(params.waterCementRatio).toBeLessThan(0.8);
      expect(params.sandRatio).toBeGreaterThan(20);
      expect(params.sandRatio).toBeLessThan(50);
      expect(params.density).toBeGreaterThan(2000);
      expect(params.density).toBeLessThan(3000);
    });

    it('应该返回计算结果', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const results = data.data.calculationResults;

      expect(results).toMatchObject({
        totalWeight: expect.any(Number),
        materials: expect.any(Object),
        strengthPrediction: expect.any(Number),
        qualityScore: expect.any(Number),
        warnings: expect.any(Array),
        suggestions: expect.any(Array),
        carbonFootprint: expect.any(Number),
        costEstimate: expect.any(Number),
      });

      // 验证结果范围
      expect(results.qualityScore).toBeGreaterThanOrEqual(0);
      expect(results.qualityScore).toBeLessThanOrEqual(100);
      expect(results.strengthPrediction).toBeGreaterThan(0);
    });
  });

  describe('请求验证', () => {
    it('应该拒绝非 POST 请求', async () => {
      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(405);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        message: '只允许POST请求',
        error: 'METHOD_NOT_ALLOWED',
      });
    });

    it('应该验证必填参数', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: {
          // 缺少必填参数
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        message: expect.stringContaining('缺少必要参数'),
        error: 'MISSING_REQUIRED_PARAMS',
      });
    });

    it('应该验证目标强度格式', async () => {
      const invalidRequest = {
        ...validRequest,
        targetStrength: '', // 无效强度
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: invalidRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
    });

    it('应该验证环境条件', async () => {
      const invalidRequest = {
        ...validRequest,
        environment: '', // 无效环境
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: invalidRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
    });
  });

  describe('不同强度等级测试', () => {
    const strengthLevels = ['C15', 'C20', 'C25', 'C30', 'C35', 'C40', 'C50'];

    strengthLevels.forEach(strength => {
      it(`应该正确处理 ${strength} 强度等级`, async () => {
        const request = {
          ...validRequest,
          targetStrength: strength,
        };

        const { req, res } = createMocks({
          method: 'POST',
          body: request,
        });

        await handler(req, res);

        expect(res._getStatusCode()).toBe(200);

        const data = JSON.parse(res._getData());
        expect(data.success).toBe(true);
        expect(data.data.calculationParams.targetStrength).toBe(
          parseInt(strength.replace('C', ''))
        );
      });
    });
  });

  describe('不同环境条件测试', () => {
    const environments = ['一般环境', '海洋环境', '冻融环境', '化学侵蚀环境'];

    environments.forEach(env => {
      it(`应该正确处理${env}`, async () => {
        const request = {
          ...validRequest,
          environment: env,
        };

        const { req, res } = createMocks({
          method: 'POST',
          body: request,
        });

        await handler(req, res);

        expect(res._getStatusCode()).toBe(200);

        const data = JSON.parse(res._getData());
        expect(data.success).toBe(true);
        expect(data.data.description).toContain(env);
      });
    });
  });

  describe('成本等级测试', () => {
    const costLevels = ['low', 'medium', 'high'];

    costLevels.forEach(level => {
      it(`应该正确处理 ${level} 成本等级`, async () => {
        const request = {
          ...validRequest,
          costLevel: level,
        };

        const { req, res } = createMocks({
          method: 'POST',
          body: request,
        });

        await handler(req, res);

        expect(res._getStatusCode()).toBe(200);

        const data = JSON.parse(res._getData());
        expect(data.success).toBe(true);

        // 验证成本影响
        const costEstimate = data.data.calculationResults.costEstimate;
        expect(costEstimate).toBeGreaterThan(0);
      });
    });
  });

  describe('特殊要求处理', () => {
    it('应该处理高强度要求', async () => {
      const request = {
        ...validRequest,
        specialRequirements: ['高强度'],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: request,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const suggestions = data.data.calculationResults.suggestions;

      expect(suggestions.some((s: string) => s.includes('硅灰'))).toBe(true);
    });

    it('应该处理高耐久性要求', async () => {
      const request = {
        ...validRequest,
        specialRequirements: ['高耐久性'],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: request,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const suggestions = data.data.calculationResults.suggestions;

      expect(suggestions.some((s: string) => s.includes('水胶比'))).toBe(true);
    });
  });

  describe('温度条件处理', () => {
    it('应该处理低温环境', async () => {
      const request = {
        ...validRequest,
        additionalParams: {
          ...validRequest.additionalParams,
          temperature: 0,
        },
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: request,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const warnings = data.data.calculationResults.warnings;

      expect(warnings.some((w: string) => w.includes('防冻剂'))).toBe(true);
    });

    it('应该处理高温环境', async () => {
      const request = {
        ...validRequest,
        additionalParams: {
          ...validRequest.additionalParams,
          temperature: 40,
        },
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: request,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const warnings = data.data.calculationResults.warnings;

      expect(warnings.some((w: string) => w.includes('高温'))).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理内部错误', async () => {
      // 模拟内部错误
      const originalConsoleError = console.error;
      console.error = jest.fn();

      const { req, res } = createMocks({
        method: 'POST',
        body: null, // 无效的请求体
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(500);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        message: 'AI配比生成失败',
        error: expect.any(String),
      });

      console.error = originalConsoleError;
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成生成', async () => {
      const startTime = Date.now();

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // AI 生成应该在3秒内完成
      expect(responseTime).toBeLessThan(3000);
      expect(res._getStatusCode()).toBe(200);
    });
  });
});
