/**
 * 配比反向计算 API 集成测试
 * 测试 /api/ratio/reverse-calculate 端点的功能
 */

// Mock createMocks function
const createMocks = jest.fn();

// Mock the API handler since it doesn't exist yet
const handler = jest.fn();

describe('/api/ratio/reverse-calculate API Integration Tests', () => {
  const validRequest = {
    taskId: 'task-123',
    materials: [
      {
        id: 'cement-1',
        name: '水泥',
        category: 'cement',
        actualAmount: 350,
        unit: 'kg/m³',
      },
      {
        id: 'water-1',
        name: '水',
        category: 'water',
        actualAmount: 175,
        unit: 'kg/m³',
      },
      {
        id: 'sand-1',
        name: '砂',
        category: 'fine-aggregate',
        actualAmount: 650,
        unit: 'kg/m³',
      },
      {
        id: 'gravel-1',
        name: '石',
        category: 'coarse-aggregate',
        actualAmount: 1200,
        unit: 'kg/m³',
      },
    ],
    calculationParams: {
      targetStrength: 30,
      targetSlump: 120,
      targetAirContent: 4.5,
      temperature: 20,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/ratio/reverse-calculate', () => {
    it('应该成功执行反向计算', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: true,
        message: '反向计算完成',
        timestamp: expect.any(String),
        data: {
          calculationParams: expect.any(Object),
          qualityAnalysis: expect.any(Object),
          warnings: expect.any(Array),
          suggestions: expect.any(Array),
          adjustments: expect.any(Array),
          metadata: expect.any(Object),
        },
      });
    });

    it('应该返回正确的计算参数', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const params = data.data.calculationParams;

      expect(params).toMatchObject({
        density: expect.any(Number),
        waterCementRatio: expect.any(Number),
        sandRatio: expect.any(Number),
        strengthGrade: expect.any(Number),
        slump: expect.any(Number),
        airContent: expect.any(Number),
        temperature: expect.any(Number),
      });

      // 验证计算结果合理性
      expect(params.waterCementRatio).toBeCloseTo(0.5, 1); // 175/350 = 0.5
      expect(params.density).toBeGreaterThan(2000);
      expect(params.sandRatio).toBeGreaterThan(0);
      expect(params.sandRatio).toBeLessThan(100);
    });

    it('应该返回质量分析', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const analysis = data.data.qualityAnalysis;

      expect(analysis).toMatchObject({
        qualityScore: expect.any(Number),
        strengthPrediction: expect.any(Number),
        durabilityRating: expect.any(String),
        workabilityRating: expect.any(String),
      });

      // 验证评分范围
      expect(analysis.qualityScore).toBeGreaterThanOrEqual(0);
      expect(analysis.qualityScore).toBeLessThanOrEqual(100);
      expect(analysis.strengthPrediction).toBeGreaterThan(0);

      // 验证评级
      expect(['优秀', '良好', '一般', '较差']).toContain(analysis.durabilityRating);
      expect(['优秀', '良好', '一般', '较差']).toContain(analysis.workabilityRating);
    });

    it('应该提供警告和建议', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const { warnings, suggestions } = data.data;

      expect(warnings).toBeInstanceOf(Array);
      expect(suggestions).toBeInstanceOf(Array);

      // 应该至少有基本建议
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0]).toContain('试配验证');
    });

    it('应该返回调整建议', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const adjustments = data.data.adjustments;

      expect(adjustments).toBeInstanceOf(Array);

      // 如果有调整建议，验证结构
      adjustments.forEach((adj: any) => {
        expect(adj).toMatchObject({
          parameter: expect.any(String),
          action: expect.any(String),
          amount: expect.any(Number),
          reason: expect.any(String),
        });
        expect(['increase', 'decrease']).toContain(adj.action);
      });
    });

    it('应该包含元数据', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const metadata = data.data.metadata;

      expect(metadata).toMatchObject({
        calculationMethod: 'reverse',
        calculatedAt: expect.any(String),
        version: expect.any(String),
        confidence: expect.any(Number),
      });

      // 验证置信度范围
      expect(metadata.confidence).toBeGreaterThanOrEqual(0);
      expect(metadata.confidence).toBeLessThanOrEqual(1);
    });
  });

  describe('请求验证', () => {
    it('应该拒绝非 POST 请求', async () => {
      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(405);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: '只允许POST请求',
        },
      });
    });

    it('应该验证必填参数', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: {
          // 缺少必填参数
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: expect.stringContaining('缺少必要参数'),
        },
      });
    });

    it('应该验证材料数据', async () => {
      const invalidRequest = {
        taskId: 'task-123',
        materials: [], // 空材料列表
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: invalidRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
    });
  });

  describe('不同材料组合测试', () => {
    it('应该处理只有基本材料的情况', async () => {
      const basicRequest = {
        ...validRequest,
        materials: [
          {
            id: 'cement-1',
            name: '水泥',
            category: 'cement',
            actualAmount: 300,
            unit: 'kg/m³',
          },
          {
            id: 'water-1',
            name: '水',
            category: 'water',
            actualAmount: 150,
            unit: 'kg/m³',
          },
        ],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: basicRequest,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);

      const data = JSON.parse(res._getData());
      expect(data.success).toBe(true);
    });

    it('应该处理包含外加剂的情况', async () => {
      const requestWithAdmixture = {
        ...validRequest,
        materials: [
          ...validRequest.materials,
          {
            id: 'admixture-1',
            name: '外加剂',
            category: 'admixture',
            actualAmount: 3.5,
            unit: 'kg/m³',
          },
        ],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: requestWithAdmixture,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);

      const data = JSON.parse(res._getData());
      const params = data.data.calculationParams;

      // 有外加剂时坍落度应该更高
      expect(params.slump).toBeGreaterThan(120);
    });
  });

  describe('边界条件测试', () => {
    it('应该处理极低水胶比', async () => {
      const lowWCRequest = {
        ...validRequest,
        materials: [
          {
            id: 'cement-1',
            name: '水泥',
            category: 'cement',
            actualAmount: 500,
            unit: 'kg/m³',
          },
          {
            id: 'water-1',
            name: '水',
            category: 'water',
            actualAmount: 125, // 水胶比 0.25
            unit: 'kg/m³',
          },
          ...validRequest.materials.slice(2),
        ],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: lowWCRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const warnings = data.data.warnings;

      expect(warnings.some((w: string) => w.includes('水胶比过低'))).toBe(true);
    });

    it('应该处理极高水胶比', async () => {
      const highWCRequest = {
        ...validRequest,
        materials: [
          {
            id: 'cement-1',
            name: '水泥',
            category: 'cement',
            actualAmount: 250,
            unit: 'kg/m³',
          },
          {
            id: 'water-1',
            name: '水',
            category: 'water',
            actualAmount: 175, // 水胶比 0.7
            unit: 'kg/m³',
          },
          ...validRequest.materials.slice(2),
        ],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: highWCRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const warnings = data.data.warnings;

      expect(warnings.some((w: string) => w.includes('水胶比过高'))).toBe(true);
    });

    it('应该处理极端砂率', async () => {
      const extremeSandRequest = {
        ...validRequest,
        materials: [
          ...validRequest.materials.slice(0, 2),
          {
            id: 'sand-1',
            name: '砂',
            category: 'fine-aggregate',
            actualAmount: 200, // 很低的砂用量
            unit: 'kg/m³',
          },
          {
            id: 'gravel-1',
            name: '石',
            category: 'coarse-aggregate',
            actualAmount: 1500,
            unit: 'kg/m³',
          },
        ],
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: extremeSandRequest,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const warnings = data.data.warnings;

      expect(warnings.some((w: string) => w.includes('砂率'))).toBe(true);
    });
  });

  describe('约束条件测试', () => {
    it('应该根据目标强度提供调整建议', async () => {
      const requestWithConstraints = {
        ...validRequest,
        calculationParams: {
          ...validRequest.calculationParams,
          targetStrength: 40, // 高于当前配比强度
        },
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: requestWithConstraints,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const adjustments = data.data.adjustments;

      // 应该建议增加水泥用量
      expect(
        adjustments.some((adj: any) => adj.parameter === 'cement' && adj.action === 'increase')
      ).toBe(true);
    });

    it('应该根据目标坍落度提供调整建议', async () => {
      const requestWithSlumpTarget = {
        ...validRequest,
        calculationParams: {
          ...validRequest.calculationParams,
          targetSlump: 180, // 高坍落度要求
        },
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: requestWithSlumpTarget,
      });

      await handler(req, res);

      const data = JSON.parse(res._getData());
      const adjustments = data.data.adjustments;

      // 应该建议调整用水量
      expect(adjustments.some((adj: any) => adj.parameter === 'water')).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理计算错误', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: null, // 无效请求体
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(500);

      const data = JSON.parse(res._getData());
      expect(data).toMatchObject({
        success: false,
        error: {
          code: 'CALCULATION_ERROR',
          message: '反向计算失败',
        },
      });
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成计算', async () => {
      const startTime = Date.now();

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequest,
      });

      await handler(req, res);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // 反向计算应该在1秒内完成
      expect(responseTime).toBeLessThan(1000);
      expect(res._getStatusCode()).toBe(200);
    });
  });
});
