/**
 * 集成测试工具文件
 * 配置测试环境和全局模拟
 *
 * 注意：这个文件不包含测试用例，只是工具函数
 */

// 导出工具函数供测试使用
export const mockFetch = jest.fn();
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

import React from 'react';
import { cleanup } from '@testing-library/react';

// 每个测试后清理
afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

// 每个测试前设置
beforeEach(() => {
  // 重置所有模拟
  jest.resetAllMocks();

  // 模拟 localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'localStorage', { value: localStorageMock });

  // 模拟 fetch
  global.fetch = jest.fn();

  // 模拟 ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // 模拟 IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));
});

// 模拟环境变量
process.env['NEXT_PUBLIC_USE_MOCK_DATA'] = 'true';
// NODE_ENV is read-only in some environments, so we use Object.defineProperty
Object.defineProperty(process.env, 'NODE_ENV', {
  value: 'test',
  writable: true,
});

// 模拟 Next.js 路由
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useParams: () => ({
    taskId: 'test-task-id',
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/test-path',
}));

// 模拟 toast
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// 模拟 Lucide React 图标
jest.mock('lucide-react', () => ({
  Calculator: () => React.createElement('div', { 'data-testid': 'calculator-icon' }),
  TestTube: () => React.createElement('div', { 'data-testid': 'test-tube-icon' }),
  Printer: () => React.createElement('div', { 'data-testid': 'printer-icon' }),
  Container: () => React.createElement('div', { 'data-testid': 'container-icon' }),
  History: () => React.createElement('div', { 'data-testid': 'history-icon' }),
  Layers: () => React.createElement('div', { 'data-testid': 'layers-icon' }),
  Settings: () => React.createElement('div', { 'data-testid': 'settings-icon' }),
  RefreshCw: () => React.createElement('div', { 'data-testid': 'refresh-icon' }),
  BookCopy: () => React.createElement('div', { 'data-testid': 'book-copy-icon' }),
  PlusCircle: () => React.createElement('div', { 'data-testid': 'plus-circle-icon' }),
  X: () => React.createElement('div', { 'data-testid': 'x-icon' }),
  Save: () => React.createElement('div', { 'data-testid': 'save-icon' }),
  ChevronUp: () => React.createElement('div', { 'data-testid': 'chevron-up-icon' }),
  ChevronDown: () => React.createElement('div', { 'data-testid': 'chevron-down-icon' }),
}));
