/**
 * TaskList 集成测试
 * 测试任务列表组件的基本渲染功能
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock TaskList component
const TaskList = () => React.createElement('div', { 'data-testid': 'task-list' });

// Mock components and data
jest.mock('@/components/sections/task-list/task-list-refactored', () => ({
  TaskList,
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return React.createElement('div', { 'data-testid': 'test-wrapper' }, children);
};

describe('TaskList Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本渲染', () => {
    it('应该正确渲染任务列表组件', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // 验证任务列表存在
      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });

    it('应该在测试包装器中渲染', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // 验证测试包装器存在
      expect(screen.getByTestId('test-wrapper')).toBeInTheDocument();
      // 验证任务列表在包装器内
      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });

    it('应该能够多次渲染而不出错', () => {
      const { rerender } = render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      expect(screen.getByTestId('task-list')).toBeInTheDocument();

      // 重新渲染
      rerender(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });
  });

  describe('组件稳定性', () => {
    it('应该处理空的 props', () => {
      render(<TaskList />);
      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });

    it('应该在没有包装器的情况下渲染', () => {
      render(<TaskList />);
      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });
  });
});
