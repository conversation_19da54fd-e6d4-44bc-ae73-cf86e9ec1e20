/**
 * 全局Mock设置
 * 为测试环境提供必要的Mock实现
 *
 * 注意：这个文件不包含测试用例，只是Mock配置
 */

import React from 'react';

// 防止Jest将此文件识别为测试套件
if (typeof describe === 'undefined') {
  // 这不是测试环境，跳过Mock设置
}

// Mock window对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  usePathname: () => '/',
}));

jest.mock('next/dynamic', () => {
  return function dynamic(_importFunc: () => Promise<any>) {
    const Component = (_props: any) => {
      return null; // 简化的mock实现
    };

    Component.displayName = 'DynamicComponent';
    return Component;
  };
});

// Mock React DnD
jest.mock('react-dnd', () => ({
  DndProvider: ({ children }: { children: React.ReactNode }) => children,
  useDrag: () => [{ isDragging: false }, jest.fn(), jest.fn()],
  useDrop: () => [{ isOver: false, canDrop: false }, jest.fn()],
}));

// Mock react-dnd-html5-backend
jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}));

// Mock Zustand stores
jest.mock('@/store/appStore', () => ({
  useAppStore: jest.fn(() => ({
    tasks: [],
    vehicles: [],
    plants: [],
    setTasks: jest.fn(),
    setVehicles: jest.fn(),
    setPlants: jest.fn(),
    updateTask: jest.fn(),
    updateVehicle: jest.fn(),
    addTask: jest.fn(),
    removeTask: jest.fn(),
  })),
}));

jest.mock('@/store/uiStore', () => ({
  useUiStore: jest.fn(() => ({
    selectedPlantId: null,
    selectedTaskIds: [],
    vehicleDisplayMode: 'normal',
    taskDisplayMode: 'table',
    setSelectedPlantId: jest.fn(),
    setSelectedTaskIds: jest.fn(),
    setVehicleDisplayMode: jest.fn(),
    setTaskDisplayMode: jest.fn(),
    toggleTaskSelection: jest.fn(),
    clearTaskSelection: jest.fn(),
  })),
}));

// Mock Shadcn/ui components
// Note: Removed Button and Card mocks to allow proper CSS class testing

jest.mock('@/components/ui/table', () => ({
  Table: ({ children, ...props }: any) => React.createElement('table', props, children),
  TableHeader: ({ children, ...props }: any) => React.createElement('thead', props, children),
  TableBody: ({ children, ...props }: any) => React.createElement('tbody', props, children),
  TableRow: ({ children, ...props }: any) => React.createElement('tr', props, children),
  TableHead: ({ children, ...props }: any) => React.createElement('th', props, children),
  TableCell: ({ children, ...props }: any) => React.createElement('td', props, children),
}));

// Mock toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
    dismiss: jest.fn(),
  }),
}));

// Mock logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  memory: {
    usedJSHeapSize: 1024 * 1024,
    totalJSHeapSize: 2048 * 1024,
    jsHeapSizeLimit: 4096 * 1024,
  },
};

// 设置全局performance对象
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
  configurable: true,
});

Object.defineProperty(window, 'performance', {
  value: mockPerformance,
  writable: true,
  configurable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock,
});

// 简化console输出以减少测试噪音
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = jest.fn(message => {
  // 过滤掉一些已知的无害警告
  if (typeof message === 'string') {
    if (
      message.includes('Warning: ReactDOM.render is deprecated') ||
      message.includes('Warning: componentWillReceiveProps') ||
      message.includes('act(...)')
    ) {
      return;
    }
  }
  originalConsoleError(message);
});

console.warn = jest.fn(message => {
  // 过滤掉一些已知的无害警告
  if (typeof message === 'string') {
    if (message.includes('Warning: componentWillReceiveProps') || message.includes('deprecated')) {
      return;
    }
  }
  originalConsoleWarn(message);
});

// 导出mock工具函数
export { localStorageMock };
