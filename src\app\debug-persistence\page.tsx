'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';

/**
 * 调试持久化问题的专用页面
 */
export default function DebugPersistencePage() {
  const taskListSettings = useTaskListSettings();
  const [refreshCount, setRefreshCount] = useState(0);
  const [localStorageData, setLocalStorageData] = useState<string | null>(null);
  const [sessionStorageData, setSessionStorageData] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState<string>('');

  // 修复 Hydration 错误 - 在客户端设置时间
  useEffect(() => {
    setCurrentTime(new Date().toLocaleTimeString());
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // 检查存储状态
  const checkStorageStatus = () => {
    if (typeof window !== 'undefined') {
      const localData = localStorage.getItem('taskListSettings_v3.3');
      const sessionData = sessionStorage.getItem('taskListSettings_v3.3_backup');
      setLocalStorageData(localData);
      setSessionStorageData(sessionData);
    }
  };

  useEffect(() => {
    checkStorageStatus();
    const interval = setInterval(checkStorageStatus, 1000);
    return () => clearInterval(interval);
  }, []);

  // 测试设置列背景色
  const testSetColumnBackground = () => {
    console.log('🧪 测试设置列背景色');
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'pale-pink');
    setTimeout(checkStorageStatus, 100);
  };

  // 测试设置固定列背景色
  const testSetStickyBackground = () => {
    console.log('🧪 测试设置固定列背景色');
    taskListSettings.updateStickyColumnStyle({
      backgroundColor: 'bg-green-100',
    });
    setTimeout(checkStorageStatus, 100);
  };

  // 清除所有设置
  const clearAllSettings = () => {
    console.log('🧪 清除所有设置');
    if (typeof window !== 'undefined') {
      localStorage.removeItem('taskListSettings_v3.3');
      sessionStorage.removeItem('taskListSettings_v3.3_backup');
      checkStorageStatus();
      setTimeout(() => window.location.reload(), 500);
    }
  };

  // 强制刷新
  const forceRefresh = () => {
    setRefreshCount(prev => prev + 1);
    window.location.reload();
  };

  // 解析存储数据
  const parseStorageData = (data: string | null) => {
    if (!data) return null;
    try {
      const parsed = JSON.parse(data);
      return {
        columnBackgrounds: Object.keys(parsed.columnBackgrounds || {}),
        stickyColumnBg: parsed.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
        displayMode: parsed.displayMode,
        density: parsed.density,
      };
    } catch (error) {
      return { error: '解析失败' };
    }
  };

  const localParsed = parseStorageData(localStorageData);
  const sessionParsed = parseStorageData(sessionStorageData);

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <h1 className='text-3xl font-bold'>持久化问题调试页面</h1>

      {/* 当前设置状态 */}
      <Card>
        <CardHeader>
          <CardTitle>当前设置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h3 className='font-semibold mb-2'>Hook状态</h3>
              <div className='space-y-1 text-sm'>
                <div>设置已加载: {taskListSettings.isSettingsLoaded ? '是' : '否'}</div>
                <div>显示模式: {taskListSettings.settings.displayMode}</div>
                <div>密度: {taskListSettings.settings.density}</div>
                <div>
                  列背景数量:{' '}
                  {Object.keys(taskListSettings.settings.columnBackgrounds || {}).length}
                </div>
                <div>
                  任务编号列背景:{' '}
                  {taskListSettings.settings.columnBackgrounds?.['taskNumber'] || '无'}
                </div>
                <div>
                  固定列背景:{' '}
                  {taskListSettings.settings.tableStyleConfig?.stickyColumnStyle?.backgroundColor ||
                    '无'}
                </div>
              </div>
            </div>

            <div>
              <h3 className='font-semibold mb-2'>操作</h3>
              <div className='space-y-2'>
                <Button onClick={testSetColumnBackground} variant='outline' size='sm'>
                  设置任务编号列红色背景
                </Button>
                <Button onClick={testSetStickyBackground} variant='outline' size='sm'>
                  设置固定列绿色背景
                </Button>
                <Button onClick={clearAllSettings} variant='destructive' size='sm'>
                  清除所有设置
                </Button>
                <Button onClick={forceRefresh} variant='outline' size='sm'>
                  强制刷新页面
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 存储状态 */}
      <Card>
        <CardHeader>
          <CardTitle>存储状态检查</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h3 className='font-semibold mb-2'>localStorage</h3>
              <div className='space-y-1 text-sm'>
                <div>数据存在: {localStorageData ? '是' : '否'}</div>
                <div>数据大小: {localStorageData?.length || 0} 字符</div>
                {localParsed && (
                  <div className='mt-2'>
                    <div>解析结果:</div>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(localParsed, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className='font-semibold mb-2'>sessionStorage (备份)</h3>
              <div className='space-y-1 text-sm'>
                <div>备份存在: {sessionStorageData ? '是' : '否'}</div>
                <div>备份大小: {sessionStorageData?.length || 0} 字符</div>
                {sessionParsed && (
                  <div className='mt-2'>
                    <div>解析结果:</div>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(sessionParsed, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 调试信息 */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2 text-sm'>
            <div>页面刷新次数: {refreshCount}</div>
            <div>当前时间: {currentTime}</div>
            <div className='mt-4'>
              <strong>调试步骤:</strong>
              <ol className='list-decimal list-inside mt-2 space-y-1'>
                <li>打开浏览器开发者工具的控制台</li>
                <li>点击"设置任务编号列红色背景"</li>
                <li>观察控制台日志和存储状态变化</li>
                <li>点击"强制刷新页面"</li>
                <li>检查设置是否保持</li>
                <li>重复步骤2-5多次</li>
              </ol>
            </div>
            <div className='mt-4'>
              <strong>预期行为:</strong>
              <ul className='list-disc list-inside mt-2 space-y-1'>
                <li>设置后立即在localStorage中看到数据</li>
                <li>刷新后设置保持不变</li>
                <li>控制台显示详细的保存和加载日志</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
