'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useTaskListStyles } from '@/features/task-management/hooks/use-task-list-styles';
import { allBackgroundColorOptionsMap } from '@/models/column-specific-style-modal';

/**
 * 调试表格样式问题的专用页面
 */
export default function DebugTableStylesPage() {
  const taskListSettings = useTaskListSettings();
  const taskListStyles = useTaskListStyles(taskListSettings.settings);
  const [testResults, setTestResults] = useState<any>({});
  const [currentTime, setCurrentTime] = useState<string>('');

  // 修复 Hydration 错误 - 在客户端设置时间
  useEffect(() => {
    setCurrentTime(new Date().toLocaleTimeString());
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // 测试列背景色设置
  const testColumnBackground = () => {
    console.log('🧪 测试列背景色设置');

    // 设置任务编号列背景色 - 使用预定义的值
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'pale-yellow');

    // 检查设置是否生效
    setTimeout(() => {
      const bgProps = taskListStyles.getColumnBackgroundProps('taskNumber', false, false);
      console.log('🧪 列背景色测试结果:', bgProps);

      setTestResults((prev: any) => ({
        ...prev,
        columnBackground: {
          setting: taskListSettings.settings.columnBackgrounds?.['taskNumber'],
          props: bgProps,
          timestamp: currentTime,
        },
      }));
    }, 100);
  };

  // 测试字体大小设置
  const testFontSize = () => {
    console.log('🧪 测试字体大小设置');

    // 设置任务编号列字体大小 - 使用模态框中定义的值
    taskListSettings.handleColumnTextStyleChange('taskNumber', 'fontSize', 'text-[14px]');

    // 检查设置是否生效
    setTimeout(() => {
      const textStyles = taskListSettings.settings.columnTextStyles?.['taskNumber'];
      console.log('🧪 字体大小测试结果:', textStyles);

      setTestResults((prev: any) => ({
        ...prev,
        fontSize: {
          setting: textStyles,
          timestamp: currentTime,
        },
      }));
    }, 100);
  };

  // 测试固定列背景色
  const testStickyBackground = () => {
    console.log('🧪 测试固定列背景色设置');

    // 使用Tailwind类名设置固定列背景色
    taskListSettings.updateStickyColumnStyle({
      backgroundColor: 'bg-green-100',
    });

    setTimeout(() => {
      const bgProps = taskListStyles.getColumnBackgroundProps('taskNumber', false, true);
      console.log('🧪 固定列背景色测试结果:', bgProps);

      setTestResults((prev: any) => ({
        ...prev,
        stickyBackground: {
          setting: taskListSettings.settings.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
          props: bgProps,
          timestamp: currentTime,
        },
      }));
    }, 100);
  };

  // 检查背景色选项映射
  const checkBackgroundOptions = () => {
    console.log('🧪 检查背景色选项映射');

    // 测试预定义的背景色值
    const testValues = [
      'pale-yellow',
      'pale-blue',
      'pale-green',
      'pale-pink',
      'white',
      'light-gray',
    ];
    const results = testValues.map(value => ({
      value,
      option: allBackgroundColorOptionsMap.get(value),
      exists: allBackgroundColorOptionsMap.has(value),
    }));

    console.log('🧪 背景色选项检查结果:', results);

    // 也检查所有可用的选项
    console.log('🧪 所有可用的背景色选项:', Array.from(allBackgroundColorOptionsMap.keys()));

    setTestResults((prev: any) => ({
      ...prev,
      backgroundOptions: {
        testResults: results,
        allOptions: Array.from(allBackgroundColorOptionsMap.keys()),
      },
    }));
  };

  // 测试双击表头功能
  const testDoubleClickHeader = () => {
    console.log('🧪 测试双击表头功能');

    // 模拟双击表头打开列样式设置模态框
    const mockColumnDef = {
      id: 'taskNumber',
      label: '任务编号',
      isStyleable: true,
      fixed: false,
      defaultVisible: true,
      densityStyles: {}, // 添加必需的属性
    } as any; // 临时使用 any 类型

    console.log('🧪 模拟打开列样式设置模态框:', mockColumnDef);
    taskListSettings.openColumnSpecificStyleModal(mockColumnDef);

    setTestResults((prev: any) => ({
      ...prev,
      doubleClickTest: {
        columnDef: mockColumnDef,
        modalOpened: true,
        timestamp: currentTime,
      },
    }));
  };

  // 测试浅底背景色
  const testSemiTransparentBackground = () => {
    console.log('🧪 测试浅底背景色设置');

    // 测试多个浅底背景色选项
    const testOptions = [
      { value: 'muted-light-15', label: '柔和灰浅底 (15%)' },
      { value: 'primary-light-15', label: '主题色浅底 (15%)' },
      { value: 'accent-light-15', label: '强调色浅底 (15%)' },
      { value: 'destructive-light-15', label: '警示色浅底 (15%)' },
    ];

    testOptions.forEach((option, index) => {
      setTimeout(() => {
        console.log(`🧪 测试浅底背景色选项: ${option.label} (${option.value})`);

        // 设置任务编号列浅底背景色
        taskListSettings.handleColumnBackgroundChange('taskNumber', option.value);

        // 检查设置是否生效
        setTimeout(() => {
          const bgProps = taskListStyles.getColumnBackgroundProps('taskNumber', false, false);
          console.log(`🧪 ${option.label} 测试结果:`, bgProps);

          // 检查背景色选项映射
          const bgOption = allBackgroundColorOptionsMap.get(option.value);
          console.log(`🧪 ${option.label} 选项映射:`, bgOption);

          if (index === testOptions.length - 1) {
            // 最后一个测试完成后更新结果
            setTestResults((prev: any) => ({
              ...prev,
              semiTransparentBackground: {
                setting: taskListSettings.settings.columnBackgrounds?.['taskNumber'],
                props: bgProps,
                allTestOptions: testOptions,
                timestamp: currentTime,
              },
            }));
          }
        }, 50);
      }, index * 200);
    });
  };

  // 测试文字样式应用
  const testTextStyleApplication = () => {
    console.log('🧪 测试文字样式应用');

    // 设置任务编号列文字样式
    taskListSettings.handleColumnTextStyleChange('taskNumber', 'fontSize', 'text-[16px]');
    taskListSettings.handleColumnTextStyleChange('taskNumber', 'color', 'primary');
    taskListSettings.handleColumnTextStyleChange('taskNumber', 'fontWeight', 'font-bold');

    // 检查设置是否生效
    setTimeout(() => {
      const textStyles = taskListSettings.settings.columnTextStyles?.['taskNumber'];
      console.log('🧪 文字样式设置结果:', textStyles);

      setTestResults((prev: any) => ({
        ...prev,
        textStyleApplication: {
          setting: textStyles,
          timestamp: currentTime,
        },
      }));
    }, 100);
  };

  // 重置所有设置
  const resetAllSettings = () => {
    console.log('🧪 重置所有设置');

    // 重置列背景色
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'default-solid');

    // 重置字体大小
    taskListSettings.handleColumnTextStyleChange('taskNumber', 'fontSize', 'default');

    // 重置固定列背景色
    taskListSettings.updateStickyColumnStyle({
      backgroundColor: 'bg-white',
    });

    setTestResults({});
  };

  // 实时监控设置变化
  useEffect(() => {
    console.log('🔍 当前设置状态:', {
      columnBackgrounds: taskListSettings.settings.columnBackgrounds,
      columnTextStyles: taskListSettings.settings.columnTextStyles,
      stickyColumnStyle: taskListSettings.settings.tableStyleConfig?.stickyColumnStyle,
    });
  }, [taskListSettings.settings]);

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <h1 className='text-3xl font-bold'>表格样式调试页面</h1>

      {/* 测试操作 */}
      <Card>
        <CardHeader>
          <CardTitle>测试操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Button onClick={testColumnBackground} variant='outline' size='sm'>
                测试列背景色设置
              </Button>
              <Button onClick={testFontSize} variant='outline' size='sm'>
                测试字体大小设置
              </Button>
              <Button onClick={testStickyBackground} variant='outline' size='sm'>
                测试固定列背景色
              </Button>
            </div>

            <div className='space-y-2'>
              <Button onClick={checkBackgroundOptions} variant='outline' size='sm'>
                检查背景色选项映射
              </Button>
              <Button onClick={testDoubleClickHeader} variant='outline' size='sm'>
                测试双击表头功能
              </Button>
              <Button onClick={testSemiTransparentBackground} variant='outline' size='sm'>
                测试浅底背景色
              </Button>
              <Button onClick={testTextStyleApplication} variant='outline' size='sm'>
                测试文字样式应用
              </Button>
              <Button onClick={resetAllSettings} variant='destructive' size='sm'>
                重置所有设置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 当前设置状态 */}
      <Card>
        <CardHeader>
          <CardTitle>当前设置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h3 className='font-semibold mb-2'>列背景色设置</h3>
              <pre className='text-xs bg-gray-100 p-2 rounded'>
                {JSON.stringify(taskListSettings.settings.columnBackgrounds, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className='font-semibold mb-2'>列文本样式设置</h3>
              <pre className='text-xs bg-gray-100 p-2 rounded'>
                {JSON.stringify(taskListSettings.settings.columnTextStyles, null, 2)}
              </pre>
            </div>
          </div>

          <div className='mt-4'>
            <h3 className='font-semibold mb-2'>固定列样式设置</h3>
            <pre className='text-xs bg-gray-100 p-2 rounded'>
              {JSON.stringify(
                taskListSettings.settings.tableStyleConfig?.stickyColumnStyle,
                null,
                2
              )}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {testResults.columnBackground && (
              <div>
                <h3 className='font-semibold mb-2'>
                  列背景色测试结果 ({testResults.columnBackground.timestamp})
                </h3>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <h4 className='text-sm font-medium'>设置值:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.columnBackground.setting, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium'>生成的属性:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.columnBackground.props, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}

            {testResults.fontSize && (
              <div>
                <h3 className='font-semibold mb-2'>
                  字体大小测试结果 ({testResults.fontSize.timestamp})
                </h3>
                <pre className='text-xs bg-gray-100 p-2 rounded'>
                  {JSON.stringify(testResults.fontSize.setting, null, 2)}
                </pre>
              </div>
            )}

            {testResults.stickyBackground && (
              <div>
                <h3 className='font-semibold mb-2'>
                  固定列背景色测试结果 ({testResults.stickyBackground.timestamp})
                </h3>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <h4 className='text-sm font-medium'>设置值:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.stickyBackground.setting, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium'>生成的属性:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.stickyBackground.props, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}

            {testResults.semiTransparentBackground && (
              <div>
                <h3 className='font-semibold mb-2'>
                  浅底背景色测试结果 ({testResults.semiTransparentBackground.timestamp})
                </h3>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <h4 className='text-sm font-medium'>设置值:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.semiTransparentBackground.setting, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium'>生成的属性:</h4>
                    <pre className='text-xs bg-gray-100 p-2 rounded'>
                      {JSON.stringify(testResults.semiTransparentBackground.props, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}

            {testResults.textStyleApplication && (
              <div>
                <h3 className='font-semibold mb-2'>
                  文字样式应用测试结果 ({testResults.textStyleApplication.timestamp})
                </h3>
                <pre className='text-xs bg-gray-100 p-2 rounded'>
                  {JSON.stringify(testResults.textStyleApplication.setting, null, 2)}
                </pre>
              </div>
            )}

            {testResults.backgroundOptions && (
              <div>
                <h3 className='font-semibold mb-2'>背景色选项检查结果</h3>
                <pre className='text-xs bg-gray-100 p-2 rounded'>
                  {JSON.stringify(testResults.backgroundOptions, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 调试信息 */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2 text-sm'>
            <div>设置已加载: {taskListSettings.isSettingsLoaded ? '是' : '否'}</div>
            <div>当前时间: {currentTime}</div>
            <div className='mt-4'>
              <strong>调试步骤:</strong>
              <ol className='list-decimal list-inside mt-2 space-y-1'>
                <li>打开浏览器开发者工具的控制台</li>
                <li>点击各个测试按钮</li>
                <li>观察控制台日志和测试结果</li>
                <li>检查设置是否正确保存和应用</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
