import React from 'react';
import localFont from 'next/font/local';
import type { Metadata } from 'next';

import { QueryProvider } from '@/core/providers/query-provider';
import { DndContextProvider } from '@/core/providers/dnd-context-provider';
import { Toaster } from '@/shared/components/toaster';
import { DragDropProvider } from '@/core/contexts/DragDropContext';
import { ThemeProvider } from '@/core/contexts/theme-provider';
import DevToolsContainer from '@/shared/components/dev/ConditionalDevTools';
import { LazyModalManager } from '@/models/LazyModalManager';
import LazyLoadingProvider from '@/core/providers/LazyLoadingProvider';
import PerformanceMonitorWidget from '@/shared/components/performance/PerformanceMonitorWidget';

import './globals.css';

// 初始化全局错误处理器
if (typeof window !== 'undefined') {
  import('@/infrastructure/error-handling/error-handlers').then(({ setupGlobalErrorHandlers }) => {
    setupGlobalErrorHandlers();
  });
}

// 使用系统字体替代 Geist，避免网络连接问题
const geistSans = localFont({
  src: [
    {
      path: '../assets/fonts/ttf/PingFangSC-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
  ],
  variable: '--font-geist-sans',
  display: 'swap',
  fallback: ['system-ui', 'arial', 'sans-serif'],
});

const pingFangMono = localFont({
  src: [
    {
      path: '../assets/fonts/ttf/PingFangSC-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Semibold.ttf',
      weight: '600',
      style: 'normal',
    },
  ],
  variable: '--font-pingfang-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'FEILING',
  description: 'FEILING商砼搅拌站调度管理系统',
  icons: {
    icon: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzUwMzgxMzQ5MDExIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ2NTkiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ3NS4yIDEyOEM1MjkuNiAxMjggNTc2IDE2Ni40IDU3NiAyMjRWMjU2aDIyNGM0OS42IDAgOTYgNDAgOTYgOTZ2MjU3LjZjMCAxNy42LTggMzUuMi0yMC44IDQ2LjRsLTMuMiAzLjItNDAgMjcuMlY4MzJoOTZjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDk2YTMyLjA2NCAzMi4wNjQgMCAwIDEtMzItMzJjMC0xNy42IDEyLjgtMzAuNCAzMC40LTMySDE5MnYtMTg3LjJsLTQxLjYtMzMuNmMtMTIuOC0xMS4yLTIyLjQtMjcuMi0yMi40LTQ0LjhWMjI0QzEyOCAxNzIuOCAxNjkuNiAxMjggMjI0IDEyOGgyNTEuMnpNNTE4LjQgNjM4LjRsLTYuNCA2LjRWODMyaDY0di0xNDUuNmwtNDEuNi0yOC44YTY0IDY0IDAgMCAxLTE2LTE5LjJ6TTc2OCA3MjkuNmwtNDQuOCAzMC40YTMxLjI5NiAzMS4yOTYgMCAwIDEtMzguNCAxLjZsLTEuNi0xLjYtNDMuMi0yOC44VjgzMmgxMjh2LTEwMi40eiBtLTMyMC0zMmwtNzYuOCA2NGEzMS4yOTYgMzEuMjk2IDAgMCAxLTM4LjQgMS42bC0xLjYtMS42TDI1NiA2OTcuNlY4MzJoMTkydi0xMzQuNHpNNDgwIDE5MmgtMjU2Yy0xNy42IDAtMzIgMTQuNC0zMiAzMnYzMzcuNmwxNjAgMTMyLjhMNTEyIDU2MS42VjIyNGMwLTE3LjYtMTQuNC0zMi0zMi0zMnogbTMyMCAxMjhINTc2djI4OS42bDEyOCA4NC44IDEyOC04NC44VjM1MmMwLTE3LjYtMTQuNC0zMi0zMi0zMnogbS00OCAxOTJjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDY1NmEzMi4wNjQgMzIuMDY0IDAgMCAxLTMyLTMyYzAtMTcuNiAxMi44LTMwLjQgMzAuNC0zMmg5Ny42eiBtMC0xMjhjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDY1NmEzMi4wNjQgMzIuMDY0IDAgMCAxLTMyLTMyYzAtMTcuNiAxMi44LTMwLjQgMzAuNC0zMmg5Ny42eiIgZmlsbD0iIzBEQzZGMSIgcC1pZD0iNDY2MCI+PC9wYXRoPjwvc3ZnPg==',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // 懒加载系统通过 LazyLoadingProvider 初始化

  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${pingFangMono.variable} text-xs font-normal antialiased`}
      >
        <ThemeProvider defaultTheme='oceanic-deep' storageKey='app-theme'>
          <QueryProvider>
            <DragDropProvider>
              <DndContextProvider>
                <LazyLoadingProvider>
                  <LazyModalManager>
                    {children}
                    <DevToolsContainer />
                    <Toaster />
                    <PerformanceMonitorWidget compact />

                    {/* <audio id='dispatch-alert-sound' preload='none' style={{ display: 'none' }}>
                      <source src='/sounds/alert.wav' type='audio/wav' />
                      <source src='/sounds/alert.mp3' type='audio/mpeg' />
                    </audio> */}
                  </LazyModalManager>
                </LazyLoadingProvider>
              </DndContextProvider>
            </DragDropProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
