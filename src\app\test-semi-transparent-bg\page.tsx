'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useTaskListStyles } from '@/features/task-management/hooks/use-task-list-styles';
import { allBackgroundColorOptionsMap } from '@/models/column-specific-style-modal';

/**
 * 专门测试浅底背景色的页面
 */
export default function TestSemiTransparentBgPage() {
  const taskListSettings = useTaskListSettings();
  const taskListStyles = useTaskListStyles(taskListSettings.settings);
  const [currentTime, setCurrentTime] = useState<string>('');
  const [testResults, setTestResults] = useState<any>({});

  // 修复 Hydration 错误 - 在客户端设置时间
  useEffect(() => {
    setCurrentTime(new Date().toLocaleTimeString());
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // 浅底背景色选项列表
  const semiTransparentOptions = [
    { value: 'default-semi', label: '默认 (透明)' },
    { value: 'muted-light-15', label: '柔和灰浅底 (15%)' },
    { value: 'primary-light-15', label: '主题色浅底 (15%)' },
    { value: 'accent-light-15', label: '强调色浅底 (15%)' },
    { value: 'destructive-light-15', label: '警示色浅底 (15%)' },
    { value: 'info-light-15', label: '信息蓝浅底 (15%)' },
    { value: 'success-light-15', label: '成功绿浅底 (15%)' },
    { value: 'warning-light-15', label: '警告黄浅底 (15%)' },
    { value: 'cyan-light-15', label: '青色浅底 (15%)' },
    { value: 'slate-light-15', label: '蓝灰浅底 (15%)' },
    { value: 'teal-light-15', label: '水鸭浅底 (15%)' },
  ];

  // 测试单个浅底背景色选项
  const testSingleOption = (option: { value: string; label: string }) => {
    console.log(`🧪 测试浅底背景色选项: ${option.label} (${option.value})`);

    // 设置背景色
    taskListSettings.handleColumnBackgroundChange('taskNumber', option.value);

    // 检查设置结果
    setTimeout(() => {
      const bgProps = taskListStyles.getColumnBackgroundProps('taskNumber', false, false);
      const bgOption = allBackgroundColorOptionsMap.get(option.value);

      console.log(`🧪 ${option.label} 测试结果:`, {
        setting: taskListSettings.settings.columnBackgrounds?.['taskNumber'],
        bgOption,
        bgProps,
      });

      setTestResults((prev: any) => ({
        ...prev,
        [option.value]: {
          label: option.label,
          value: option.value,
          setting: taskListSettings.settings.columnBackgrounds?.['taskNumber'],
          bgOption,
          bgProps,
          timestamp: currentTime,
        },
      }));
    }, 100);
  };

  // 测试所有浅底背景色选项
  const testAllOptions = () => {
    console.log('🧪 开始测试所有浅底背景色选项');
    setTestResults({});

    semiTransparentOptions.forEach((option, index) => {
      setTimeout(() => {
        testSingleOption(option);
      }, index * 300);
    });
  };

  // 检查背景色选项映射
  const checkOptionMapping = () => {
    console.log('🧪 检查浅底背景色选项映射');

    const mappingResults = semiTransparentOptions.map(option => {
      const bgOption = allBackgroundColorOptionsMap.get(option.value);
      return {
        value: option.value,
        label: option.label,
        exists: !!bgOption,
        bgOption,
      };
    });

    console.log('🧪 浅底背景色选项映射结果:', mappingResults);

    setTestResults((prev: any) => ({
      ...prev,
      mappingCheck: {
        results: mappingResults,
        timestamp: currentTime,
      },
    }));
  };

  // 重置设置
  const resetSettings = () => {
    console.log('🧪 重置背景色设置');
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'default-solid');
    setTestResults({});
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <h1 className='text-3xl font-bold'>浅底背景色测试页面</h1>

      {/* 测试操作 */}
      <Card>
        <CardHeader>
          <CardTitle>测试操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-3 gap-4'>
            <Button onClick={testAllOptions} variant='default' size='sm'>
              测试所有浅底背景色
            </Button>
            <Button onClick={checkOptionMapping} variant='outline' size='sm'>
              检查选项映射
            </Button>
            <Button onClick={resetSettings} variant='destructive' size='sm'>
              重置设置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 单个选项测试 */}
      <Card>
        <CardHeader>
          <CardTitle>单个选项测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-4 gap-2'>
            {semiTransparentOptions.map(option => (
              <Button
                key={option.value}
                onClick={() => testSingleOption(option)}
                variant='outline'
                size='sm'
                className='text-xs'
              >
                {option.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 当前设置状态 */}
      <Card>
        <CardHeader>
          <CardTitle>当前设置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2 text-sm'>
            <div>当前时间: {currentTime}</div>
            <div>
              任务编号列背景色设置:{' '}
              {taskListSettings.settings.columnBackgrounds?.['taskNumber'] || '未设置'}
            </div>
            <div>设置已加载: {taskListSettings.isSettingsLoaded ? '是' : '否'}</div>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {testResults.mappingCheck && (
              <div>
                <h3 className='font-semibold mb-2'>
                  选项映射检查结果 ({testResults.mappingCheck.timestamp})
                </h3>
                <div className='grid grid-cols-2 gap-4'>
                  {testResults.mappingCheck.results.map((result: any) => (
                    <div key={result.value} className='p-2 border rounded'>
                      <div className='font-medium'>{result.label}</div>
                      <div className='text-sm text-gray-600'>值: {result.value}</div>
                      <div className='text-sm'>映射: {result.exists ? '✅ 存在' : '❌ 缺失'}</div>
                      {result.bgOption && (
                        <div className='text-xs mt-1'>
                          <div>themeClassName: {result.bgOption.themeClassName || '无'}</div>
                          <div>
                            specificSolidColor: {result.bgOption.specificSolidColor || '无'}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {Object.entries(testResults)
              .filter(([key]) => key !== 'mappingCheck')
              .map(([key, result]: [string, any]) => (
                <div key={key}>
                  <h3 className='font-semibold mb-2'>
                    {result.label} 测试结果 ({result.timestamp})
                  </h3>
                  <div className='grid grid-cols-3 gap-4'>
                    <div>
                      <h4 className='text-sm font-medium'>设置值:</h4>
                      <pre className='text-xs bg-gray-100 p-2 rounded'>
                        {JSON.stringify(result.setting, null, 2)}
                      </pre>
                    </div>
                    <div>
                      <h4 className='text-sm font-medium'>背景选项:</h4>
                      <pre className='text-xs bg-gray-100 p-2 rounded'>
                        {JSON.stringify(result.bgOption, null, 2)}
                      </pre>
                    </div>
                    <div>
                      <h4 className='text-sm font-medium'>生成的属性:</h4>
                      <pre className='text-xs bg-gray-100 p-2 rounded'>
                        {JSON.stringify(result.bgProps, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* CSS类名直接测试 */}
      <Card>
        <CardHeader>
          <CardTitle>CSS类名直接测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='text-sm text-gray-600 mb-4'>
              直接测试各种浅底背景色CSS类名的显示效果：
            </div>

            <div className='grid grid-cols-3 gap-4'>
              {semiTransparentOptions.slice(1).map(option => {
                const bgOption = allBackgroundColorOptionsMap.get(option.value);
                const className = bgOption?.themeClassName || '';

                return (
                  <div key={option.value} className='space-y-2'>
                    <div className='text-xs font-medium'>{option.label}</div>
                    <div className='text-xs text-gray-500'>类名: {className}</div>
                    <div
                      className={`p-4 border rounded ${className}`}
                      style={{ minHeight: '60px' }}
                    >
                      <div className='text-xs'>这是 {option.label} 的效果</div>
                    </div>
                    <div
                      className='p-4 border rounded virtualized-table'
                      style={{ minHeight: '60px' }}
                    >
                      <div className={`p-2 ${className}`}>
                        <div className='text-xs'>表格单元格中的 {option.label}</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 调试信息 */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2 text-sm'>
            <div className='mt-4'>
              <strong>测试步骤:</strong>
              <ol className='list-decimal list-inside mt-2 space-y-1'>
                <li>点击"检查选项映射"验证所有浅底背景色选项是否正确定义</li>
                <li>点击"测试所有浅底背景色"或单个选项按钮进行测试</li>
                <li>观察控制台日志和测试结果</li>
                <li>检查生成的 themeClassName 是否正确</li>
                <li>查看上方"CSS类名直接测试"区域的视觉效果</li>
                <li>访问任务列表页面验证实际效果</li>
              </ol>
            </div>

            <div className='mt-4'>
              <strong>CSS修复说明:</strong>
              <ul className='list-disc list-inside mt-2 space-y-1 text-xs'>
                <li>添加了强制的CSS规则确保浅底背景色显示</li>
                <li>使用 !important 覆盖可能冲突的样式</li>
                <li>修复了 background-clip 属性的影响</li>
                <li>保护浅底背景色不被高亮和悬停状态覆盖</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
