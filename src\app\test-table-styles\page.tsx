'use client';

import { useState } from 'react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useTaskListStyles } from '@/features/task-management/hooks/use-task-list-styles';

/**
 * 测试页面：验证表格样式修复
 * 1. 固定列背景色不透明问题
 * 2. 样式持久化问题
 */
export default function TestTableStylesPage() {
  const taskListSettings = useTaskListSettings();
  const taskListStyles = useTaskListStyles(taskListSettings.settings);
  const [testColumnId] = useState('taskNumber');

  // 测试固定列背景色
  const testFixedColumnBackground = () => {
    const props = taskListStyles.getColumnBackgroundProps(testColumnId, false, true);
    return props;
  };

  // 测试普通列背景色
  const testNormalColumnBackground = () => {
    const props = taskListStyles.getColumnBackgroundProps(testColumnId, false, false);
    return props;
  };

  // 测试样式持久化
  const testStylePersistence = () => {
    // 修改一个样式设置
    taskListSettings.updateSetting('enableZebraStriping', !taskListSettings.settings.enableZebraStriping);
  };

  // 测试固定列背景色设置
  const testStickyColumnBackground = () => {
    // 设置固定列背景色为蓝色
    taskListSettings.updateStickyColumnStyle({
      backgroundColor: 'bg-blue-100'
    });
  };

  // 重置固定列背景色
  const resetStickyColumnBackground = () => {
    taskListSettings.updateStickyColumnStyle({
      backgroundColor: 'bg-white'
    });
  };

  // 测试跨标签页同步
  const testCrossTabSync = () => {
    // 触发一个设置变更
    taskListSettings.updateSetting('density',
      taskListSettings.settings.density === 'compact' ? 'normal' : 'compact'
    );
  };

  // 测试列背景色设置
  const testColumnBackground = () => {
    // 设置任务编号列的背景色
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'pale-yellow');
  };

  // 重置列背景色
  const resetColumnBackground = () => {
    taskListSettings.handleColumnBackgroundChange('taskNumber', 'default-solid');
  };

  const fixedColumnProps = testFixedColumnBackground();
  const normalColumnProps = testNormalColumnBackground();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">表格样式修复测试</h1>

      {/* 问题1: 固定列背景色测试 */}
      <Card>
        <CardHeader>
          <CardTitle>问题1: 固定列背景色测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">固定列背景色</h3>
              <div
                className={`p-4 border rounded ${fixedColumnProps.className}`}
                style={fixedColumnProps.style}
              >
                固定列示例
                <div className="text-sm text-muted-foreground mt-2">
                  className: {fixedColumnProps.className || '无'}
                </div>
                <div className="text-sm text-muted-foreground">
                  style: {JSON.stringify(fixedColumnProps.style)}
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">普通列背景色</h3>
              <div
                className={`p-4 border rounded ${normalColumnProps.className}`}
                style={normalColumnProps.style}
              >
                普通列示例
                <div className="text-sm text-muted-foreground mt-2">
                  className: {normalColumnProps.className || '无'}
                </div>
                <div className="text-sm text-muted-foreground">
                  style: {JSON.stringify(normalColumnProps.style)}
                </div>
              </div>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            <strong>预期结果:</strong> 固定列应该有不透明的背景色，普通列可以是透明的
          </div>
        </CardContent>
      </Card>

      {/* 问题2: 样式持久化测试 */}
      <Card>
        <CardHeader>
          <CardTitle>问题2: 样式持久化测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">当前设置</h3>
              <div className="space-y-2 text-sm">
                <div>显示模式: {taskListSettings.settings.displayMode}</div>
                <div>密度: {taskListSettings.settings.density}</div>
                <div>斑马纹: {taskListSettings.settings.enableZebraStriping ? '启用' : '禁用'}</div>
                <div>设置已加载: {taskListSettings.isSettingsLoaded ? '是' : '否'}</div>
                <div>固定列背景: {taskListSettings.settings.tableStyleConfig?.stickyColumnStyle?.backgroundColor || '默认'}</div>
                <div>任务编号列背景: {taskListSettings.settings.columnBackgrounds?.['taskNumber'] || '默认'}</div>
                <div>列背景设置数量: {Object.keys(taskListSettings.settings.columnBackgrounds || {}).length}</div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">测试操作</h3>
              <div className="space-y-2">
                <Button onClick={testStylePersistence} variant="outline">
                  切换斑马纹设置
                </Button>
                <Button onClick={testCrossTabSync} variant="outline">
                  切换密度设置
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                >
                  刷新页面测试持久化
                </Button>
                <Button onClick={testStickyColumnBackground} variant="outline">
                  设置固定列蓝色背景
                </Button>
                <Button onClick={resetStickyColumnBackground} variant="outline">
                  重置固定列背景
                </Button>
                <Button onClick={testColumnBackground} variant="outline">
                  设置任务编号列背景
                </Button>
                <Button onClick={resetColumnBackground} variant="outline">
                  重置任务编号列背景
                </Button>
              </div>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            <strong>测试步骤:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>点击"切换斑马纹设置"或"切换密度设置"</li>
              <li>观察设置是否立即生效</li>
              <li>点击"刷新页面测试持久化"</li>
              <li>检查设置是否在刷新后保持</li>
              <li>在新标签页打开此页面，检查设置是否同步</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* 配置同步状态 */}
      <Card>
        <CardHeader>
          <CardTitle>配置同步状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div>localStorage 键名: taskListSettings_v3.3</div>
            <div>
              localStorage 存在: {
                typeof window !== 'undefined' && localStorage.getItem('taskListSettings_v3.3')
                  ? '是' : '否'
              }
            </div>
            <div>
              sessionStorage 备份存在: {
                typeof window !== 'undefined' && sessionStorage.getItem('taskListSettings_v3.3_backup')
                  ? '是' : '否'
              }
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 修复说明 */}
      <Card>
        <CardHeader>
          <CardTitle>修复说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <strong>问题1修复:</strong> 固定列背景色不透明
              <ul className="list-disc list-inside mt-2 space-y-1 ml-4">
                <li>修改了 use-task-list-styles.ts 中的 getColumnBackgroundProps 函数</li>
                <li>为固定列设置了不透明的背景色: bg-background 和 hsl(var(--background))</li>
                <li>移除了透明度设置，确保固定列有实心背景</li>
              </ul>
            </div>

            <div>
              <strong>问题2修复:</strong> 样式持久化问题
              <ul className="list-disc list-inside mt-2 space-y-1 ml-4">
                <li>创建了增强的配置同步服务 (configSyncService)</li>
                <li>支持多重备份: localStorage + sessionStorage + IndexedDB</li>
                <li>实现跨标签页同步机制</li>
                <li>添加重试机制和错误恢复</li>
                <li>强制触发 storage 事件确保同步</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
