'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

/**
 * 配比页面拖拽项目类型
 */
export const RatioDragItemTypes = {
  SILO_MATERIAL: 'silo_material',
} as const;

export type RatioDragItemType = (typeof RatioDragItemTypes)[keyof typeof RatioDragItemTypes];

/**
 * 拖拽的料仓材料数据结构
 */
export interface DraggedSiloMaterial {
  id: string;
  name: string;
  type: string;
  siloId: string;
  capacity: number;
  currentAmount: number;
  specification: string;
  materialType?: string;
  density?: number;
  unit?: string;
}

/**
 * 拖拽状态接口
 */
interface RatioDragDropState {
  isDragging: boolean;
  draggedMaterial: DraggedSiloMaterial | null;
  dragType: RatioDragItemType | null;
  isOverDropZone: boolean;
}

/**
 * 拖拽上下文接口
 */
interface RatioDragDropContextType {
  // 状态
  state: RatioDragDropState;

  // 拖拽控制方法
  setDraggedMaterial: (material: DraggedSiloMaterial | null) => void;
  setIsDragging: (isDragging: boolean) => void;
  setIsOverDropZone: (isOver: boolean) => void;
  setDragType: (type: RatioDragItemType | null) => void;

  // 拖拽事件处理
  onMaterialDragStart: (material: DraggedSiloMaterial) => void;
  onMaterialDragEnd: () => void;
  onMaterialDrop: (material: DraggedSiloMaterial) => void;

  // 工具方法
  clearDragState: () => void;
}

const RatioDragDropContext = createContext<RatioDragDropContextType | undefined>(undefined);

/**
 * 配比页面拖拽上下文提供者属性
 */
interface RatioDragDropProviderProps {
  children: ReactNode;
  onMaterialDrop?: (material: DraggedSiloMaterial) => void;
}

/**
 * 配比页面拖拽上下文提供者
 */
export function RatioDragDropProvider({ children, onMaterialDrop }: RatioDragDropProviderProps) {
  // 拖拽状态
  const [state, setState] = useState<RatioDragDropState>({
    isDragging: false,
    draggedMaterial: null,
    dragType: null,
    isOverDropZone: false,
  });

  // 设置拖拽材料
  const setDraggedMaterial = useCallback((material: DraggedSiloMaterial | null) => {
    setState(prev => ({ ...prev, draggedMaterial: material }));
  }, []);

  // 设置拖拽状态
  const setIsDragging = useCallback((isDragging: boolean) => {
    setState(prev => ({ ...prev, isDragging }));
  }, []);

  // 设置悬停状态
  const setIsOverDropZone = useCallback((isOver: boolean) => {
    setState(prev => ({ ...prev, isOverDropZone: isOver }));
  }, []);

  // 设置拖拽类型
  const setDragType = useCallback((type: RatioDragItemType | null) => {
    setState(prev => ({ ...prev, dragType: type }));
  }, []);

  // 材料拖拽开始
  const onMaterialDragStart = useCallback((material: DraggedSiloMaterial) => {
    setState(prev => ({
      ...prev,
      isDragging: true,
      draggedMaterial: material,
      dragType: RatioDragItemTypes.SILO_MATERIAL,
    }));
  }, []);

  // 材料拖拽结束
  const onMaterialDragEnd = useCallback(() => {
    setState(prev => ({
      ...prev,
      isDragging: false,
      draggedMaterial: null,
      dragType: null,
      isOverDropZone: false,
    }));
  }, []);

  // 材料放置
  const onMaterialDropHandler = useCallback(
    (material: DraggedSiloMaterial) => {
      // 调用外部处理函数
      if (onMaterialDrop) {
        onMaterialDrop(material);
      }

      // 清理拖拽状态
      onMaterialDragEnd();
    },
    [onMaterialDrop, onMaterialDragEnd]
  );

  // 清理拖拽状态
  const clearDragState = useCallback(() => {
    setState({
      isDragging: false,
      draggedMaterial: null,
      dragType: null,
      isOverDropZone: false,
    });
  }, []);

  const contextValue: RatioDragDropContextType = {
    state,
    setDraggedMaterial,
    setIsDragging,
    setIsOverDropZone,
    setDragType,
    onMaterialDragStart,
    onMaterialDragEnd,
    onMaterialDrop: onMaterialDropHandler,
    clearDragState,
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <RatioDragDropContext.Provider value={contextValue}>{children}</RatioDragDropContext.Provider>
    </DndProvider>
  );
}

/**
 * 使用配比拖拽上下文的 Hook
 */
export function useRatioDragDrop(): RatioDragDropContextType {
  const context = useContext(RatioDragDropContext);
  if (context === undefined) {
    throw new Error('useRatioDragDrop must be used within a RatioDragDropProvider');
  }
  return context;
}

/**
 * 拖拽预览组件属性
 */
interface DragPreviewProps {
  material: DraggedSiloMaterial;
}

/**
 * 拖拽预览组件
 */
export function MaterialDragPreview({ material }: DragPreviewProps) {
  return (
    <div className='bg-white border border-gray-300 rounded-lg p-3 shadow-lg opacity-90 transform rotate-2'>
      <div className='font-medium text-sm'>{material.name}</div>
      <div className='text-xs text-gray-500'>{material.specification}</div>
      <div className='text-xs text-blue-600 mt-1'>
        {material.currentAmount.toFixed(1)}t / {material.capacity.toFixed(1)}t
      </div>
    </div>
  );
}

/**
 * 拖拽状态指示器组件
 */
interface DragIndicatorProps {
  isOver: boolean;
  canDrop: boolean;
  className?: string;
}

export function DragIndicator({ isOver, canDrop, className = '' }: DragIndicatorProps) {
  if (!canDrop) return null;

  const indicatorClass = isOver
    ? 'border-blue-500 bg-blue-50 border-solid'
    : 'border-gray-300 bg-gray-50 border-dashed';

  return (
    <div
      className={`absolute inset-0 border-2 rounded-lg transition-all duration-200 ${indicatorClass} ${className}`}
    >
      {isOver && (
        <div className='absolute inset-0 flex items-center justify-center'>
          <div className='bg-blue-500 text-white px-3 py-1 rounded-md text-sm font-medium'>
            释放以添加材料
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * 拖拽覆盖层组件
 */
export function RatioDragOverlay() {
  const { state } = useRatioDragDrop();

  if (!state.isDragging || !state.draggedMaterial) {
    return null;
  }

  return (
    <div className='fixed inset-0 pointer-events-none z-50'>
      <MaterialDragPreview material={state.draggedMaterial} />
    </div>
  );
}
