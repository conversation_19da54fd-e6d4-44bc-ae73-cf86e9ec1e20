// src/contexts/TaskSelectionContext.tsx
'use client';

import React, { createContext, useCallback, useContext, useState } from 'react';

import type { Task } from '@/core/types';

// src/contexts/TaskSelectionContext.tsx

interface TaskSelectionContextType {
  // 单选模式
  selectedTask: Task | null;
  selectedTaskId: string | null;
  setSelectedTask: (task: Task | null) => void;
  setSelectedTaskId: (taskId: string | null) => void;
  isTaskSelected: (taskId: string) => boolean;
  clearSelection: () => void;

  // 多选模式
  isMultiSelectMode: boolean;
  setMultiSelectMode: (enabled: boolean) => void;
  selectedTaskIds: string[];
  selectedTasks: Task[];
  toggleTaskSelection: (task: Task) => void;
  selectAllTasks: (tasks: Task[]) => void;
  clearAllSelections: () => void;
  isTaskMultiSelected: (taskId: string) => boolean;
}

const TaskSelectionContext = createContext<TaskSelectionContextType | undefined>(undefined);

interface TaskSelectionProviderProps {
  children: React.ReactNode;
}

export const TaskSelectionProvider: React.FC<TaskSelectionProviderProps> = ({ children }) => {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  // Multi-select state
  const [isMultiSelectMode, setMultiSelectMode] = useState(false);
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([]);
  const [selectedTasks, setSelectedTasks] = useState<Task[]>([]);

  const handleSetSelectedTask = useCallback(
    (task: Task | null) => {
      // 移除 console.log 以提升性能
      setSelectedTask(task);
      setSelectedTaskId(task?.id || null);
    },
    [] // 移除依赖，避免不必要的重新创建
  );

  const handleSetSelectedTaskId = useCallback(
    (taskId: string | null) => {
      setSelectedTaskId(taskId);
      // 如果只设置ID，清除task对象，让组件自己查找
      if (taskId !== selectedTask?.id) {
        setSelectedTask(null);
      }
    },
    [selectedTask?.id]
  );

  const isTaskSelected = useCallback(
    (taskId: string) => {
      return selectedTaskId === taskId;
    },
    [selectedTaskId]
  );

  const clearSelection = useCallback(() => {
    setSelectedTask(null);
    setSelectedTaskId(null);
  }, []);

  // Multi-select functions
  const toggleTaskSelection = useCallback((task: Task) => {
    setSelectedTaskIds(prev => {
      const isSelected = prev.includes(task.id);
      if (isSelected) {
        const newIds = prev.filter(id => id !== task.id);
        setSelectedTasks(current => current.filter(t => t.id !== task.id));
        return newIds;
      } else {
        setSelectedTasks(current => [...current, task]);
        return [...prev, task.id];
      }
    });
  }, []);

  const selectAllTasks = useCallback((tasks: Task[]) => {
    setSelectedTaskIds(tasks.map(t => t.id));
    setSelectedTasks(tasks);
  }, []);

  const clearAllSelections = useCallback(() => {
    setSelectedTaskIds([]);
    setSelectedTasks([]);
  }, []);

  const isTaskMultiSelected = useCallback(
    (taskId: string) => {
      return selectedTaskIds.includes(taskId);
    },
    [selectedTaskIds]
  );

  const contextValue: TaskSelectionContextType = {
    selectedTask,
    selectedTaskId,
    setSelectedTask: handleSetSelectedTask,
    setSelectedTaskId: handleSetSelectedTaskId,
    isTaskSelected,
    clearSelection,
    // Multi-select properties
    isMultiSelectMode,
    setMultiSelectMode,
    selectedTaskIds,
    selectedTasks,
    toggleTaskSelection,
    selectAllTasks,
    clearAllSelections,
    isTaskMultiSelected,
  };

  return (
    <TaskSelectionContext.Provider value={contextValue}>{children}</TaskSelectionContext.Provider>
  );
};

export const useTaskSelection = (): TaskSelectionContextType => {
  const context = useContext(TaskSelectionContext);
  if (!context) {
    throw new Error('useTaskSelection must be used within a TaskSelectionProvider');
  }
  return context;
};

// 简化的hook，只用于检查选中状态
export const useTaskSelectionState = () => {
  const context = useTaskSelection();

  return {
    selectedTaskId: context.selectedTaskId,
    selectedTask: context.selectedTask,
    isTaskSelected: context.isTaskSelected,
  };
};

// 简化的hook，用于设置选中状态
export const useTaskSelectionActions = () => {
  const context = useTaskSelection();

  return {
    setSelectedTask: context.setSelectedTask,
    setSelectedTaskId: context.setSelectedTaskId,
    clearSelection: context.clearSelection,
  };
};
