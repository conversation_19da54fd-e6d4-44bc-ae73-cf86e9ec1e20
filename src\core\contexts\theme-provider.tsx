'use client';

import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

export type Theme = 'oceanic-deep' | 'dark';

export type DensityMode = 'comfortable' | 'compact' | 'cozy';

export interface DensityStyleValues {
  spacing: string;
  padding: string;
  fontSize: string;
  lineHeight: string;
  borderRadius: string;
}

export const THEME_OPTIONS: { value: Theme; label: string; className?: string }[] = [
  { value: 'oceanic-deep', label: '深海蔚蓝 (默认)' },
  { value: 'dark', label: '深色模式', className: 'dark' },
];

export const DENSITY_OPTIONS: { value: DensityMode; label: string }[] = [
  { value: 'comfortable', label: '舒适' },
  { value: 'cozy', label: '标准' },
  { value: 'compact', label: '紧凑' },
];

export const DENSITY_STYLES: Record<DensityMode, DensityStyleValues> = {
  comfortable: {
    spacing: '1.5rem',
    padding: '1rem',
    fontSize: '1rem',
    lineHeight: '1.75',
    borderRadius: '0.5rem',
  },
  cozy: {
    spacing: '1rem',
    padding: '0.75rem',
    fontSize: '0.9375rem',
    lineHeight: '1.5',
    borderRadius: '0.375rem',
  },
  compact: {
    spacing: '0.75rem',
    padding: '0.5rem',
    fontSize: '0.875rem',
    lineHeight: '1.25',
    borderRadius: '0.25rem',
  },
};

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

interface ThemeProviderState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  density: DensityMode;
  setDensity: (density: DensityMode) => void;
  densityStyles: DensityStyleValues;
}

const initialState: ThemeProviderState = {
  theme: 'oceanic-deep',
  setTheme: () => null,
  density: 'cozy',
  setDensity: () => null,
  densityStyles: DENSITY_STYLES.cozy,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = 'oceanic-deep',
  storageKey = 'app-theme',
}: ThemeProviderProps) {
  const [theme, _setThemeInternal] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      try {
        const storedTheme = localStorage.getItem(storageKey) as Theme;
        if (THEME_OPTIONS.some(opt => opt.value === storedTheme)) {
          return storedTheme;
        }
      } catch (e) {
        console.error('Failed to access localStorage for theme:', e);
      }
    }
    return defaultTheme;
  });

  const [density, _setDensityInternal] = useState<DensityMode>(() => {
    if (typeof window !== 'undefined') {
      try {
        const storedDensity = localStorage.getItem('app-density') as DensityMode;
        if (DENSITY_OPTIONS.some(opt => opt.value === storedDensity)) {
          return storedDensity;
        }
      } catch (e) {
        console.error('Failed to access localStorage for density:', e);
      }
    }
    return 'cozy';
  });

  const densityStyles = DENSITY_STYLES[density];

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const root = window.document.documentElement;

    root.classList.remove('dark');

    if (theme === 'dark') {
      root.classList.add('dark');
    }
    // For "oceanic-deep", no class is needed as its styles are in :root
  }, [theme]);

  const setTheme = useCallback(
    (newTheme: Theme) => {
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(storageKey, newTheme);

          // 同时使用新的持久化管理器保存
          try {
            const {
              PersistenceManager,
            } = require('@/infrastructure/storage/persistence/persistenceManager');
            PersistenceManager.save('APP_THEME', newTheme);
          } catch (backupError) {
            console.warn('主题备份保存失败:', backupError);
          }
        } catch (e) {
          console.error('Failed to access localStorage for theme:', e);
        }
      }
      _setThemeInternal(newTheme);
    },
    [storageKey]
  );

  const setDensity = useCallback((newDensity: DensityMode) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('app-density', newDensity);

        // 同时使用新的持久化管理器保存
        try {
          const {
            PersistenceManager,
          } = require('@/infrastructure/storage/persistence/persistenceManager');
          PersistenceManager.save('APP_DENSITY', newDensity);
        } catch (backupError) {
          console.warn('密度设置备份保存失败:', backupError);
        }
      } catch (e) {
        console.error('Failed to access localStorage for density:', e);
      }
    }
    _setDensityInternal(newDensity);
  }, []);

  const value = {
    theme,
    setTheme,
    density,
    setDensity,
    densityStyles,
  };

  return <ThemeProviderContext.Provider value={value}>{children}</ThemeProviderContext.Provider>;
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
