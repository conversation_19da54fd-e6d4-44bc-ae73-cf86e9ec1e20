/**
 * 任务卡片配置类型定义
 */

// 字段样式配置
export interface FieldStyle {
  visible: boolean;
  fontSize: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
  fontWeight: 'normal' | 'medium' | 'semibold' | 'bold';
  color: 'default' | 'muted' | 'primary' | 'secondary' | 'destructive' | 'warning' | 'success';
  textAlign: 'left' | 'center' | 'right';
}

// 卡片样式配置
export interface TaskCardStyleConfig {
  // 卡片整体样式
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  spacing: 'tight' | 'normal' | 'loose';
}

// 区域配置
export interface CardAreaConfig {
  visible: boolean;
  height?: 'auto' | 'compact' | 'normal' | 'expanded';
}

// 顶部区域配置
export interface TopAreaConfig extends CardAreaConfig {
  fields: {
    messageIcon: FieldStyle; // 消息图标
    projectName: FieldStyle;
    constructionSite: FieldStyle;
    dispatchReminder: FieldStyle;
    strength: FieldStyle;
    progressRing: FieldStyle; // 不可隐藏，但可配置样式
  };
}

// 车辆区域配置
export interface VehicleAreaConfig extends CardAreaConfig {
  // 调度车辆区域不可隐藏，但可配置样式
  fields: {
    vehicleCount: FieldStyle;
    vehicleCards: FieldStyle; // 不可隐藏
  };
}

// 内容区域配置 - 可配置字段
export interface ContentAreaConfig extends CardAreaConfig {
  layout: 'single' | 'double'; // 单列或双列
  fields: {
    // 左列字段
    requiredVolume: FieldStyle;
    completedVolume: FieldStyle;
    scheduledTime: FieldStyle;
    contactPhone: FieldStyle;
    // 右列字段
    completedProgress: FieldStyle;
    estimatedDuration: FieldStyle;
    constructionLocation: FieldStyle;
    taskStatus: FieldStyle;
  };
}

// 底部区域配置
export interface BottomAreaConfig extends CardAreaConfig {
  layout: 'single' | 'double'; // 单列或双列
  fields: {
    // 左列字段
    customerName: FieldStyle;
    createdAt: FieldStyle;
    // 右列字段
    taskNumber: FieldStyle;
    updatedAt: FieldStyle;
  };
}

// 完整的任务卡片配置
export interface TaskCardConfig {
  style: TaskCardStyleConfig;
  areas: {
    top: TopAreaConfig;
    vehicle: VehicleAreaConfig;
    content: ContentAreaConfig;
    bottom: BottomAreaConfig;
  };
}

// 默认配置 - 优化为更美观紧凑的样式
export const defaultTaskCardConfig: TaskCardConfig = {
  style: {
    theme: 'default',
    borderRadius: 'lg', // 增强圆角
    shadow: 'md', // 增强阴影
    animation: 'smooth', // 更流畅的动画
    spacing: 'tight', // 紧凑间距
  },
  areas: {
    top: {
      visible: true,
      height: 'normal',
      fields: {
        messageIcon: {
          visible: true,
          fontSize: 'sm',
          fontWeight: 'normal',
          color: 'primary',
          textAlign: 'left',
        },
        projectName: {
          visible: true,
          fontSize: 'sm',
          fontWeight: 'semibold',
          color: 'default',
          textAlign: 'left',
        },
        constructionSite: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'normal',
          color: 'muted',
          textAlign: 'left',
        },
        dispatchReminder: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'bold',
          color: 'destructive',
          textAlign: 'right',
        },
        strength: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'warning',
          textAlign: 'right',
        },
        progressRing: {
          visible: true, // 不可隐藏
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'primary',
          textAlign: 'center',
        },
      },
    },
    vehicle: {
      visible: true, // 不可隐藏
      height: 'normal',
      fields: {
        vehicleCount: {
          visible: true,
          fontSize: 'sm',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        vehicleCards: {
          visible: true, // 不可隐藏
          fontSize: 'xs',
          fontWeight: 'normal',
          color: 'default',
          textAlign: 'left',
        },
      },
    },
    content: {
      visible: true,
      height: 'auto',
      layout: 'double',
      fields: {
        requiredVolume: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        completedVolume: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        scheduledTime: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        contactPhone: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        completedProgress: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        estimatedDuration: {
          visible: false,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        constructionLocation: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        taskStatus: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
      },
    },
    bottom: {
      visible: true,
      height: 'compact',
      layout: 'double',
      fields: {
        customerName: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        createdAt: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        taskNumber: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
        updatedAt: {
          visible: true,
          fontSize: 'xs',
          fontWeight: 'medium',
          color: 'default',
          textAlign: 'left',
        },
      },
    },
  },
};
