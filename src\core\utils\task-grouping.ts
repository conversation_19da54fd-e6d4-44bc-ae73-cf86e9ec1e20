import type { Task, TaskColumnId, TaskGroup, TaskGroupConfig, TaskGroupStats } from '@/core/types';

/**
 * 任务分组工具函数
 * 提供高效的任务分组和统计功能
 */

/**
 * 获取任务的分组键值 - 支持动态列分组
 */
export function getTaskGroupKey(task: Task, groupBy: TaskGroupConfig['groupBy']): string {
  if (groupBy === 'none') {
    return 'all';
  }

  // 动态获取任务对象中对应字段的值
  const value = (task as any)[groupBy];

  // 处理特殊字段的默认值
  const defaultValues: Record<string, string> = {
    dispatchStatus: '未分派',
    projectName: '未知项目',
    constructionCompany: '未知施工单位',
    constructionUnit: '未知施工单位',
    constructionLocation: '未知施工位置',
    constructionSite: '未知施工部位',
    strength: '未知强度',
    pouringMethod: '未知浇筑方式',
    deliveryStatus: '未知配送状态',
    plantId: '未知工厂',
    supplyDate: '未知日期',
    supplyTime: '未知时间',
    publishDate: '未知发布日期',
    pumpTruck: '无泵车',
    vehicleCount: '0车',
    taskNumber: '无编号',
    projectAbbreviation: '无缩写',
    contactPhone: '无联系方式',
    otherRequirements: '无特殊要求',
    timing: '无计时',
    requiredVolume: '未知需求量',
    completedVolume: '未知完成量',
    completedProgress: '未知进度',
  };

  // 如果值为空或undefined，使用默认值
  if (value === null || value === undefined || value === '') {
    return defaultValues[groupBy] || '未知';
  }

  // 对于数字类型，转换为字符串
  if (typeof value === 'number') {
    return value.toString();
  }

  // 对于布尔类型，转换为中文
  if (typeof value === 'boolean') {
    return value ? '是' : '否';
  }

  // 对于日期类型，格式化显示
  if (value instanceof Date) {
    return value.toLocaleDateString('zh-CN');
  }

  // 其他情况直接转换为字符串
  return String(value);
}

/**
 * 获取分组的显示标签 - 支持动态列分组
 */
export function getGroupLabel(groupKey: string, groupBy: TaskGroupConfig['groupBy']): string {
  if (groupBy === 'none') return '所有任务';

  // 通用的状态标签映射
  const statusLabelMap: Record<string, string> = {
    // 分派状态
    未分派: '🔄 未分派',
    已分派: '✅ 已分派',
    配送中: '🚛 配送中',
    已完成: '✨ 已完成',

    // 配送状态
    待配送: '⏳ 待配送',
    配送中状态: '🚚 配送中',
    已送达: '✅ 已送达',

    // 通用未知状态
    未知项目: '❓ 未知项目',
    未知施工单位: '❓ 未知施工单位',
    未知施工部位: '📍 未知部位',
    未知强度: '❓ 未知强度',
    未知浇筑方式: '❓ 未知方式',
    未知配送状态: '❓ 未知状态',
    未知工厂: '🏭 未知工厂',
    未知日期: '📅 未知日期',
    未知时间: '⏰ 未知时间',
    未知发布日期: '📆 未知发布日期',
    无泵车: '🚫 无泵车',
    无编号: '🔢 无编号',
    无缩写: '📝 无缩写',
    无联系方式: '📞 无联系方式',
    无特殊要求: '📋 无特殊要求',
    无计时: '⏱️ 无计时',
    未知需求量: '📊 未知需求量',
    未知完成量: '✅ 未知完成量',
    未知进度: '📈 未知进度',
    未知: '❓ 未知',

    // 布尔值
    是: '✅ 是',
    否: '❌ 否',

    // 数字相关
    '0车': '🚫 无车辆',
  };

  // 根据分组类型添加特定的图标前缀
  const getIconForGroupType = (groupBy: string): string => {
    const iconMap: Record<string, string> = {
      dispatchStatus: '🔄',
      projectName: '🏗️',
      constructionUnit: '🏢',
      constructionSite: '📍',
      strength: '💪',
      pouringMethod: '🔧',
      supplyDate: '📅',
      supplyTime: '⏰',
      publishDate: '📆',
      pumpTruck: '🚛',
      vehicleCount: '🚗',
      taskNumber: '🔢',
      projectAbbreviation: '📝',
      contactPhone: '📞',
      otherRequirements: '📋',
      timing: '⏱️',
      requiredVolume: '📊',
      completedVolume: '✅',
      completedProgress: '📈',
      deliveryStatus: '🚚',
      plantId: '🏭',
    };
    return iconMap[groupBy] || '📋';
  };

  // 如果在状态映射中找到，直接返回
  if (statusLabelMap[groupKey]) {
    return statusLabelMap[groupKey];
  }

  // 否则添加对应的图标前缀
  const icon = getIconForGroupType(groupBy);
  return `${icon} ${groupKey}`;
}

/**
 * 计算分组统计信息
 */
export function calculateGroupStats(tasks: Task[]): TaskGroupStats {
  const total = tasks.length;
  const dispatched = tasks.filter(task => task.dispatchStatus === 'InProgress').length;
  const inDelivery = tasks.filter(task => task.deliveryStatus === 'InDelivery').length;
  const completed = tasks.filter(task => task.deliveryStatus === 'Completed').length;

  return {
    total,
    dispatched,
    inDelivery,
    completed,
    pending: total - dispatched - inDelivery - completed,
  };
}

/**
 * 对任务进行分组
 */
export function groupTasks(tasks: Task[], config: TaskGroupConfig): TaskGroup[] {
  if (!config.enabled || config.groupBy === 'none') {
    return [
      {
        key: 'all',
        label: '所有任务',
        tasks,
        collapsed: false,
      },
    ];
  }

  // 按分组字段分组
  const groupMap = new Map<string, Task[]>();

  tasks.forEach(task => {
    const groupKey = getTaskGroupKey(task, config.groupBy);

    if (!groupMap.has(groupKey)) {
      groupMap.set(groupKey, []);
    }
    groupMap.get(groupKey)!.push(task);
  });

  // 转换为TaskGroup数组
  const groups: TaskGroup[] = Array.from(groupMap.entries()).map(([key, groupTasks]) => ({
    key,
    label: getGroupLabel(key, config.groupBy),
    tasks: groupTasks,
    collapsed: config.defaultCollapsed?.includes(key) || false,
  }));

  // 排序
  groups.sort((a, b) => {
    const aValue = a.key;
    const bValue = b.key;

    if (config.sortOrder === 'desc') {
      return bValue.localeCompare(aValue, 'zh-CN');
    }
    return aValue.localeCompare(bValue, 'zh-CN');
  });

  return groups;
}

/**
 * 获取可用的分组选项 - 支持动态列分组
 */
export function getGroupingOptions(config?: TaskGroupConfig): Array<{
  value: TaskGroupConfig['groupBy'];
  label: string;
  icon: string;
}> {
  // 完整的分组选项列表，包含所有可能的列
  const allOptions = [
    { value: 'none', label: '不分组', icon: '📋' },
    { value: 'dispatchStatus', label: '按分派状态', icon: '🔄' },
    { value: 'projectName', label: '按工程名称', icon: '🏗️' },
    { value: 'constructionUnit', label: '按施工单位', icon: '🏢' },
    { value: 'constructionSite', label: '按施工部位', icon: '📍' },
    { value: 'strength', label: '按强度', icon: '💪' },
    { value: 'pouringMethod', label: '按浇筑方式', icon: '🔧' },
    { value: 'supplyDate', label: '按供砼日期', icon: '📅' },
    { value: 'supplyTime', label: '按供砼时间', icon: '⏰' },
    { value: 'publishDate', label: '按发布日期', icon: '📆' },
    { value: 'pumpTruck', label: '按泵车', icon: '🚛' },
    { value: 'vehicleCount', label: '按车数', icon: '🚗' },
    { value: 'taskNumber', label: '按任务编号', icon: '🔢' },
    { value: 'projectAbbreviation', label: '按任务缩写', icon: '📝' },
    { value: 'contactPhone', label: '按联系电话', icon: '📞' },
    { value: 'otherRequirements', label: '按其他要求', icon: '📋' },
    { value: 'timing', label: '按计时', icon: '⏱️' },
    { value: 'requiredVolume', label: '按需求量', icon: '📊' },
    { value: 'completedVolume', label: '按已供量', icon: '✅' },
    { value: 'completedProgress', label: '按完成进度', icon: '📈' },
    { value: 'deliveryStatus', label: '按配送状态', icon: '🚚' },
    { value: 'plantId', label: '按工厂', icon: '🏭' },
  ] as const;

  // 如果没有提供配置，返回所有选项
  if (!config) {
    return allOptions.map(option => ({
      value: option.value as 'none' | TaskColumnId,
      label: option.label,
      icon: option.icon,
    }));
  }

  // 根据配置过滤选项
  return allOptions.filter((option): option is (typeof allOptions)[number] => {
    if (option.value === 'none') return true; // 总是允许"不分组"

    const columnId = option.value as TaskColumnId;
    const isDisallowed = config.disallowedGroupColumns?.includes(columnId) || false;
    const isAllowed = config.allowedGroupColumns?.includes(columnId) || false;

    // 如果在禁止列表中，不显示
    if (isDisallowed) return false;

    // 如果在允许列表中，显示
    if (isAllowed) return true;

    // 如果既不在允许列表也不在禁止列表中，默认显示（改为更宽松的策略）
    return true;
  });
}
