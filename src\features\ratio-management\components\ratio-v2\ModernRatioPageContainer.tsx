/**
 * 重构后的配比页面容器组件
 * 采用分层架构：UI组件只负责展示，业务逻辑通过Hook封装
 */

'use client';

import React from 'react';
import { TestTube2 } from 'lucide-react';

// 业务逻辑Hook导入
import { useRatioPageLogic } from '@/features/ratio-management/hooks/ratio/useRatioPageLogic';
import { useBackupRatioLogic } from '@/features/ratio-management/hooks/ratio/useBackupRatioLogic';
import { useNotificationLogic } from '@/features/ratio-management/hooks/ratio/useNotificationLogic';
import { useAIRatioLogic } from '@/features/ratio-management/hooks/ratio/useAIRatioLogic';

// UI组件导入
import { ModernRatioHeader } from '@/features/ratio-management/components/ratio-v2/modern-ratio-header';
import { RatioDesignPanel } from '@/features/ratio-management/components/ratio-v2/ratio-design-panel';
import { QualityInspectionPanel } from '@/features/ratio-management/components/ratio-v2/quality-inspection-panel';
import { SiloVisualizationPanel } from '@/features/ratio-management/components/ratio-v2/silo-visualization-panel';
import { RatioModalManager } from './RatioModalManager';
import { Card, CardContent } from '@/shared/components/card';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { ErrorAlert } from '@/shared/components/ErrorAlert';
import { NoSSR } from '@/shared/components/common/no-ssr';

// 上下文和工具导入
import { RatioDragDropProvider, RatioDragOverlay } from '@/core/contexts/RatioDragDropContext';
import { SiloManagementProvider } from '@/features/ratio-management/hooks/useSiloManagement';
import { convertToCalculationParams } from '@/core/utils/ratio-type-adapters';

/**
 * 重构后的配比页面容器组件
 */
export const ModernRatioPageContainer: React.FC = () => {
  // 主要业务逻辑
  const pageLogic = useRatioPageLogic();

  // 备选配比逻辑
  const backupRatioLogic = useBackupRatioLogic({
    taskId: pageLogic.taskId,
    selectedMaterials: pageLogic.ratioManager.selectedMaterials,
    calculationParams: pageLogic.ratioManager.calculationParams,
    calculationResult: pageLogic.ratioManager.calculationResult,
    removeMaterial: pageLogic.ratioManager.removeMaterial,
    addMaterial: pageLogic.ratioManager.addMaterial,
    updateCalculationParams: pageLogic.ratioManager.updateCalculationParams,
  });

  // 通知单逻辑
  const notificationLogic = useNotificationLogic({
    selectedMaterials: pageLogic.ratioManager.selectedMaterials,
    removeMaterial: pageLogic.ratioManager.removeMaterial,
    addMaterial: pageLogic.ratioManager.addMaterial,
    updateCalculationParams: pageLogic.ratioManager.updateCalculationParams,
    calculateRatio: pageLogic.ratioManager.calculateRatio,
  });

  // AI配比逻辑
  const aiRatioLogic = useAIRatioLogic({
    selectedMaterials: pageLogic.ratioManager.selectedMaterials,
    removeMaterial: pageLogic.ratioManager.removeMaterial,
    addMaterial: pageLogic.ratioManager.addMaterial,
    updateCalculationParams: pageLogic.ratioManager.updateCalculationParams,
    calculateRatio: pageLogic.ratioManager.calculateRatio,
  });

  // 处理备选配比相关操作
  const handleSaveBackupRatio = () => {
    if (backupRatioLogic.canSaveBackupRatio()) {
      pageLogic.modalManager.openModal('saveBackupRatio');
    }
  };

  const handleSelectBackupRatio = () => {
    pageLogic.modalManager.openModal('backupRatioSelection');
  };

  // 错误处理
  if (pageLogic.ratioManager.error) {
    return (
      <div className='container mx-auto p-4'>
        <ErrorAlert
          title='加载失败'
          message={pageLogic.ratioManager.error}
          onRetry={() => pageLogic.ratioManager.loadRatio(pageLogic.taskId)}
        />
      </div>
    );
  }

  // 加载状态
  if (pageLogic.ratioManager.isLoading) {
    return (
      <div className='container mx-auto p-4'>
        <LoadingSpinner size='large' message='正在加载配比数据...' />
      </div>
    );
  }

  return (
    <SiloManagementProvider>
      <div className='min-h-screen bg-gray-50'>
        {/* 页面头部 */}
        <ModernRatioHeader
          task={pageLogic.task}
          onSave={pageLogic.handleSave}
          canSave={pageLogic.ratioManager.canSave}
          isSaving={pageLogic.ratioManager.isSaving}
          isDirty={pageLogic.ratioManager.isDirty}
          onSwitchToOldVersionAction={pageLogic.handleSwitchToOldVersion}
          onAIGenerateAction={() => pageLogic.modalManager.openModal('aiGenerate')}
          onAIRecommendationAction={() => pageLogic.modalManager.openModal('ratioRecommendation')}
          onPerformanceDashboardAction={() => pageLogic.modalManager.openModal('performance')}
          onMortarRatioAction={() => pageLogic.modalManager.openModal('mortarRatio')}
          onSiloManagementAction={() => pageLogic.modalManager.openModal('siloManagement')}
          onHistoryViewAction={() => pageLogic.modalManager.openModal('history')}
          onTemplateViewAction={() => pageLogic.modalManager.openModal('template')}
          onSettingsViewAction={() => pageLogic.modalManager.openModal('settings')}
          onCheckStandardAction={() => pageLogic.modalManager.openModal('checkStandard')}
        />

        {/* 主要内容区域 */}
        <NoSSR
          fallback={
            <div className='w-full p-4 space-y-4'>
              <div className='flex gap-4 min-h-screen'>
                <div className='w-80 flex-shrink-0'>
                  <Card>
                    <CardContent className='p-4'>
                      <h3 className='text-lg font-semibold mb-4 flex items-center gap-2'>
                        <TestTube2 className='h-5 w-5' />
                        料仓管理
                      </h3>
                      <div className='text-center py-8 text-gray-500'>正在加载料仓管理面板...</div>
                    </CardContent>
                  </Card>
                </div>
                <div className='flex-1 min-w-0'>
                  <Card>
                    <CardContent className='p-4'>
                      <div className='text-center py-8 text-gray-500'>正在加载配比设计面板...</div>
                    </CardContent>
                  </Card>
                </div>
                <div className='w-80 flex-shrink-0'>
                  <QualityInspectionPanel
                    ratioMaterials={pageLogic.ratioManager.selectedMaterials.map(material => ({
                      id: material.id,
                      name: material.name,
                      amount: material.actualAmount || 0,
                      category: material.category,
                      moistureContent: material.waterContent,
                      stoneContent: material.stoneContent,
                    }))}
                    calculationParams={convertToCalculationParams(
                      pageLogic.ratioManager.calculationParams
                    )}
                    task={pageLogic.task}
                    onOptimize={() => {}}
                    onPreviewRatio={() => pageLogic.modalManager.openModal('previewRatio')}
                  />
                </div>
              </div>
            </div>
          }
        >
          <RatioDragDropProvider onMaterialDrop={pageLogic.handleMaterialDrop}>
            <div className='w-full p-4 space-y-4'>
              <div className='flex gap-4 min-h-screen'>
                {/* 左侧：料仓可视化面板 */}
                <div className='w-80 flex-shrink-0'>
                  <Card>
                    <CardContent className='p-4'>
                      <h3 className='text-lg font-semibold mb-4 flex items-center gap-2'>
                        <TestTube2 className='h-5 w-5' />
                        料仓管理
                      </h3>
                      <SiloVisualizationPanel />
                    </CardContent>
                  </Card>
                </div>

                {/* 中间：配比设计面板 */}
                <div className='flex-1 min-w-0'>
                  <RatioDesignPanel
                    ratioMaterials={pageLogic.ratioManager.selectedMaterials}
                    onMaterialsChangeAction={pageLogic.handleMaterialsChange}
                    calculationParams={convertToCalculationParams(
                      pageLogic.ratioManager.calculationParams
                    )}
                    onParamsChangeAction={pageLogic.ratioManager.updateCalculationParams}
                    onCalculate={pageLogic.handleCalculate}
                    onReverseCalculate={pageLogic.handleReverseCalculate}
                    canCalculate={pageLogic.ratioManager.canCalculate}
                    canReverseCalculate={pageLogic.ratioManager.canReverseCalculate}
                    isCalculating={pageLogic.ratioManager.isCalculating}
                    hasParamsChanged={pageLogic.ratioManager.hasParamsChanged}
                    onSelectRatio={handleSelectBackupRatio}
                    onCheckStandard={() => pageLogic.modalManager.openModal('checkStandard')}
                    onApplyNotification={() =>
                      pageLogic.modalManager.openModal('ratioNotification')
                    }
                    onOpenRecommendation={() =>
                      pageLogic.modalManager.openModal('ratioRecommendation')
                    }
                    onSaveBackupRatio={handleSaveBackupRatio}
                  />
                </div>

                {/* 右侧：质量检测与优化建议面板 */}
                <div className='w-80 flex-shrink-0'>
                  <QualityInspectionPanel
                    ratioMaterials={pageLogic.ratioManager.selectedMaterials.map(material => ({
                      id: material.id,
                      name: material.name,
                      amount: material.actualAmount || 0,
                      category: material.category,
                      moistureContent: material.waterContent,
                      stoneContent: material.stoneContent,
                    }))}
                    calculationParams={convertToCalculationParams(
                      pageLogic.ratioManager.calculationParams
                    )}
                    task={pageLogic.task}
                    onOptimize={() => {}}
                    onPreviewRatio={() => pageLogic.modalManager.openModal('previewRatio')}
                  />
                </div>
              </div>
            </div>

            {/* React DND 拖拽覆盖层 */}
            <RatioDragOverlay />

            {/* 拖拽动画效果 */}
            {pageLogic.isDropAnimating && pageLogic.droppedMaterial && (
              <div className='fixed inset-0 pointer-events-none z-50'>
                <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'>
                  <div className='bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg animate-pulse'>
                    正在添加 {pageLogic.droppedMaterial.name}...
                  </div>
                </div>
              </div>
            )}
          </RatioDragDropProvider>
        </NoSSR>

        {/* 模态框管理器 */}
        <RatioModalManager
          modals={pageLogic.modalManager.modals}
          onModalChange={(modalName, open) => {
            if (open) {
              pageLogic.modalManager.openModal(modalName);
            } else {
              pageLogic.modalManager.closeModal(modalName);
            }
          }}
          taskId={pageLogic.taskId}
          task={pageLogic.task}
          selectedMaterials={pageLogic.ratioManager.selectedMaterials}
          calculationResult={pageLogic.ratioManager.calculationResult}
          onAIGenerate={aiRatioLogic.handleAIGenerate}
          onAIRecommendation={aiRatioLogic.handleAIRecommendation}
          onApplyNotification={notificationLogic.handleApplyNotification}
          onSaveBackupRatio={backupRatioLogic.handleSaveBackupRatio}
          onApplyBackupRatio={backupRatioLogic.handleApplyBackupRatio}
          onGenerateDefaultBackupName={backupRatioLogic.generateDefaultBackupName}
          onGenerateRatioSummary={backupRatioLogic.generateRatioSummary}
        />
      </div>
    </SiloManagementProvider>
  );
};
