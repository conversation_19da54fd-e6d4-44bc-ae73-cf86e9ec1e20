/**
 * 备选配比业务逻辑Hook
 * 处理备选配比的保存和应用逻辑
 */

import { useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import { ratioBackupService } from '@/features/ratio-management/services/ratio-backup';
import type { CreateBackupRatioRequest } from '@/core/types/ratio-backup';
import type {
  UnifiedRatioMaterial,
  RatioCalculationParams,
  CalculationResults,
} from '@/core/types/ratio';

interface UseBackupRatioLogicProps {
  taskId: string;
  selectedMaterials: UnifiedRatioMaterial[];
  calculationParams: RatioCalculationParams | null;
  calculationResult: CalculationResults | null;
  removeMaterial: (id: string) => void;
  addMaterial: (material: UnifiedRatioMaterial) => void;
  updateCalculationParams: (params: RatioCalculationParams) => void;
}

/**
 * 备选配比业务逻辑Hook
 */
export function useBackupRatioLogic({
  taskId,
  selectedMaterials,
  calculationParams,
  calculationResult,
  removeMaterial,
  addMaterial,
  updateCalculationParams,
}: UseBackupRatioLogicProps) {
  const { toast } = useToast();

  /**
   * 检查是否可以保存备选配比
   */
  const canSaveBackupRatio = useCallback(() => {
    if (selectedMaterials.length === 0) {
      toast({
        title: '无法保存',
        description: '请先添加配比材料',
        variant: 'destructive',
      });
      return false;
    }
    return true;
  }, [selectedMaterials.length, toast]);

  /**
   * 保存备选配比
   */
  const handleSaveBackupRatio = useCallback(
    async (
      request: Omit<
        CreateBackupRatioRequest,
        'taskId' | 'materials' | 'calculationParams' | 'calculationResults'
      >
    ) => {
      try {
        if (!calculationParams) {
          throw new Error('计算参数不能为空');
        }

        const fullRequest: CreateBackupRatioRequest = {
          ...request,
          taskId,
          materials: selectedMaterials,
          calculationParams,
          calculationResults: calculationResult || undefined,
        };

        const savedRatio = await ratioBackupService.createBackupRatio(fullRequest);

        toast({
          title: '保存成功',
          description: `备选配比"${savedRatio.name}"已保存`,
        });

        return savedRatio;
      } catch (error) {
        console.error('保存备选配比失败:', error);
        throw error;
      }
    },
    [taskId, selectedMaterials, calculationParams, calculationResult, toast]
  );

  /**
   * 应用备选配比
   */
  const handleApplyBackupRatio = useCallback(
    async (ratioId: string) => {
      try {
        const result = await ratioBackupService.applyBackupRatio(ratioId);

        if (!result.success) {
          toast({
            title: '应用失败',
            description: result.errors?.join(', ') || '应用备选配比时发生错误',
            variant: 'destructive',
          });
          return false;
        }

        const { appliedRatio } = result;
        if (!appliedRatio) {
          throw new Error('应用的配比数据为空');
        }

        // 清空当前材料
        selectedMaterials.forEach(material => {
          removeMaterial(material.id);
        });

        // 应用备选配比的计算参数
        updateCalculationParams(appliedRatio.calculationParams);

        // 添加备选配比的材料
        appliedRatio.materials.forEach(material => {
          addMaterial(material);
        });

        toast({
          title: '应用成功',
          description: `已应用备选配比"${appliedRatio.name}"`,
        });

        if (result.warnings && result.warnings.length > 0) {
          toast({
            title: '注意',
            description: result.warnings.join(', '),
            variant: 'default',
          });
        }

        return true;
      } catch (error) {
        console.error('应用备选配比失败:', error);
        toast({
          title: '应用失败',
          description: error instanceof Error ? error.message : '应用备选配比时发生错误',
          variant: 'destructive',
        });
        return false;
      }
    },
    [selectedMaterials, removeMaterial, addMaterial, updateCalculationParams, toast]
  );

  /**
   * 生成备选配比摘要信息
   */
  const generateRatioSummary = useCallback(() => {
    if (!calculationParams) return undefined;

    return {
      targetStrength: `C${calculationParams.targetStrength}`,
      slump: calculationParams.slump,
      waterCementRatio: calculationParams.waterCementRatio,
      materialCount: selectedMaterials.length,
      qualityScore: calculationResult?.qualityScore,
    };
  }, [calculationParams, selectedMaterials.length, calculationResult]);

  /**
   * 生成默认备选配比名称
   */
  const generateDefaultBackupName = useCallback(() => {
    const strength = calculationParams?.targetStrength || 30;
    const date = new Date().toLocaleDateString();
    return `C${strength}配比-${date}`;
  }, [calculationParams]);

  return {
    canSaveBackupRatio,
    handleSaveBackupRatio,
    handleApplyBackupRatio,
    generateRatioSummary,
    generateDefaultBackupName,
  };
}
