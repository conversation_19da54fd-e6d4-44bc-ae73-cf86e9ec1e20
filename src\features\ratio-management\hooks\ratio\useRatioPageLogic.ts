/**
 * 配比页面业务逻辑Hook
 * 将页面级别的业务逻辑从组件中分离
 */

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useToast } from '@/shared/hooks/use-toast';
import { useUnifiedRatioManagement } from '@/features/ratio-management/hooks/ratio/useUnifiedRatioManagement';
import { useRatioModals } from './useRatioModals';
import { generateMockTasks } from '@/infrastructure/api/mock/mock-data';
import {
  convertSiloMaterialToUnified,
  convertToUnifiedMaterial,
} from '@/core/utils/ratio-type-adapters';
import type { Task } from '@/core/types';
import type { UnifiedRatioMaterial } from '@/core/types/ratio';
import type { DraggedSiloMaterial } from '@/core/contexts/RatioDragDropContext';
import type { NotificationApplyResult } from '@/core/types/ratio-notification';
import type { CreateBackupRatioRequest } from '@/core/types/ratio-backup';

/**
 * 配比页面业务逻辑Hook
 */
export function useRatioPageLogic() {
  const params = useParams();
  const router = useRouter();
  const taskId = params?.['taskId'] as string;
  const { toast } = useToast();

  // 模态框状态管理
  const modalManager = useRatioModals();

  // 页面状态
  const [task, setTask] = useState<Task | null>(null);
  const [isDropAnimating, setIsDropAnimating] = useState(false);
  const [droppedMaterial, setDroppedMaterial] = useState<DraggedSiloMaterial | null>(null);

  // 统一配比管理
  const ratioManager = useUnifiedRatioManagement({
    taskId,
    autoLoad: true,
    autoSave: true,
    autoSaveDelay: 3000,
    compatibilityMode: 'unified',
  });

  // 初始化任务数据
  useEffect(() => {
    const tasks = generateMockTasks();
    const foundTask = tasks.find(t => t.id === taskId) || tasks[0];
    if (foundTask) {
      setTask(foundTask);
    }
  }, [taskId]);

  // 页面卸载前保存
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (ratioManager.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [ratioManager.hasUnsavedChanges]);

  // 页面导航处理
  const handleSwitchToOldVersion = useCallback(() => {
    router.push(`/ratio/${taskId}`);
  }, [router, taskId]);

  // 材料拖拽处理
  const handleMaterialDrop = useCallback(
    (material: DraggedSiloMaterial) => {
      setIsDropAnimating(true);
      setDroppedMaterial(material);

      setTimeout(() => {
        const newRatioMaterial = convertSiloMaterialToUnified(material);
        ratioManager.addMaterial(newRatioMaterial);

        setTimeout(() => {
          setIsDropAnimating(false);
          setDroppedMaterial(null);

          toast({
            title: '材料已添加',
            description: `${material.name} 已添加到配比设计中`,
          });
        }, 300);
      }, 200);
    },
    [toast, ratioManager.addMaterial]
  );

  // 配比计算处理
  const handleCalculate = useCallback(async () => {
    try {
      await ratioManager.calculateRatio();
      toast({
        title: '计算完成',
        description: '配比计算已完成，请查看结果',
      });
    } catch (error) {
      console.error('计算失败:', error);
      toast({
        title: '计算失败',
        description: error instanceof Error ? error.message : '配比计算过程中出现错误',
        variant: 'destructive',
      });
    }
  }, [ratioManager.calculateRatio, toast]);

  // 反算处理
  const handleReverseCalculate = useCallback(async () => {
    try {
      if (ratioManager.selectedMaterials.length === 0) {
        toast({
          title: '无法反算',
          description: '请先添加配比材料',
          variant: 'destructive',
        });
        return;
      }

      await ratioManager.reverseCalculate();

      toast({
        title: '反算完成',
        description: '已根据配比材料反算出参数',
      });
    } catch (error) {
      console.error('反算失败:', error);
      toast({
        title: '反算失败',
        description: error instanceof Error ? error.message : '反算过程中发生错误',
        variant: 'destructive',
      });
    }
  }, [ratioManager.selectedMaterials, ratioManager.reverseCalculate, toast]);

  // 保存配比处理
  const handleSave = useCallback(async () => {
    try {
      await ratioManager.saveRatio();
      toast({
        title: '保存成功',
        description: '配比数据已保存',
      });
    } catch (error) {
      console.error('保存失败:', error);
      toast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '保存过程中发生错误',
        variant: 'destructive',
      });
    }
  }, [ratioManager.saveRatio, toast]);

  // 材料变更处理
  const handleMaterialsChange = useCallback(
    (materials: any[]) => {
      const currentMaterialIds = ratioManager.selectedMaterials.map(m => m.id);
      const newMaterialIds = materials.map(m => m.id);

      // 删除不在新列表中的材料
      currentMaterialIds.forEach(id => {
        if (!newMaterialIds.includes(id)) {
          ratioManager.removeMaterial(id);
        }
      });

      // 更新或添加材料
      materials.forEach(material => {
        const unifiedMaterial = convertToUnifiedMaterial(material);
        if (currentMaterialIds.includes(material.id)) {
          ratioManager.updateMaterial(material.id, unifiedMaterial);
        } else {
          ratioManager.addMaterial(unifiedMaterial);
        }
      });
    },
    [ratioManager]
  );

  return {
    // 基础数据
    taskId,
    task,

    // 动画状态
    isDropAnimating,
    droppedMaterial,

    // 管理器
    modalManager,
    ratioManager,

    // 事件处理器
    handleSwitchToOldVersion,
    handleMaterialDrop,
    handleCalculate,
    handleReverseCalculate,
    handleSave,
    handleMaterialsChange,
  };
}
