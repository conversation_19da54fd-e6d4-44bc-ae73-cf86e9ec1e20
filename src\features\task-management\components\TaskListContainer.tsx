/**
 * 重构后的任务列表容器组件
 * 采用分层架构：UI组件只负责展示，业务逻辑通过Hook封装
 */

'use client';

import { useState, useCallback } from 'react';
import { useDrop } from 'react-dnd';

import { ItemTypes } from '@/core/constants/dndItemTypes';
import { useCurrentPlantInfo } from '@/features/task-management/hooks/useCurrentPlantInfo';
import { useDraggableFab } from '@/shared/hooks/useDraggableFab';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useGroupingStore } from '@/features/task-management/store/groupingStore';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';

// 业务逻辑Hook导入
import { useTaskListModals } from '@/features/task-management/hooks/useTaskListModals';
import { useTaskListHeaderMode } from '@/features/task-management/hooks/useTaskListHeaderMode';
import { useTaskListBusinessLogic } from '../hooks/use-task-list-business-logic';
import { useTaskListColumns } from '../hooks/use-task-list-columns';
import { useTaskListData } from '../hooks/use-task-list-data';
import { useTaskListStyles } from '../hooks/use-task-list-styles';
import { convertColumnTextStyles } from './task-list-type-guards';

// UI组件导入
import { FloatingTaskListHeader } from './components/floating-task-list-header';
import { useTaskCardConfigManager } from './components/task-card-config-manager';
import { TaskListBody } from './task-list-body';
import { useTaskListDragHandlers } from './task-list-event-handlers';
import { TaskListFooter } from './task-list-footer';
import { TaskListHeader } from './task-list-header';
import { TaskListModalManager } from './task-list-modal-manager';

// 类型和配置导入
import type { CustomColumnDefinition, Task, Vehicle } from '@/core/types';
import { ALL_TASK_COLUMNS_CONFIG } from './task-list.config';

export interface TaskListContainerProps {
  productionLineCount: number;
  /** 是否使用悬浮头部 */
  useFloatingHeader?: boolean;
  /** 悬浮头部初始位置 */
  floatingHeaderPosition?: { x: number; y: number };
  /** 悬浮头部位置变化回调 */
  onFloatingHeaderPositionChange?: (position: { x: number; y: number }) => void;
}

const FAB_SIZE = 40;
const FAB_MARGIN = 16;

/**
 * 重构后的任务列表容器组件
 */
export function TaskListContainer({
  productionLineCount = 4,
  useFloatingHeader = false,
  floatingHeaderPosition,
  onFloatingHeaderPositionChange,
}: TaskListContainerProps) {
  // 头部模式管理
  const headerMode = useTaskListHeaderMode({
    useFloatingHeader,
    floatingHeaderPosition,
    onFloatingHeaderPositionChange,
  });

  // 数据和业务逻辑
  const taskListData = useTaskListData();
  const taskListBusinessLogic = useTaskListBusinessLogic();

  // 模态框管理
  const modals = useTaskListModals({
    onVehicleDispatch: taskListBusinessLogic.handleDropOnProductionLine,
    getTaskById: taskListData.getTaskById,
    allVehicles: taskListData.allVehicles,
  });

  // UI状态和设置
  const { taskStatusFilter, vehicleDisplayMode, setVehicleDisplayMode } = useUiStore();
  const { isLoadingPlants: isLoadingPlantInfo } = useCurrentPlantInfo();
  const taskListSettings = useTaskListSettings();
  const groupingStore = useGroupingStore();
  const taskListStyles = useTaskListStyles(taskListSettings.settings);

  // 适配器函数，处理分组列选择
  const handleGroupByColumnSelect = useCallback(
    (columnId: string) => {
      console.log('🔍 handleGroupByColumnSelect:', columnId);
      groupingStore.setGroupBy(columnId as any);
      taskListSettings.closeGroupByColumnSelect();
    },
    [groupingStore, taskListSettings]
  );

  // 表格列配置
  const taskListColumns = useTaskListColumns({
    densityStyles: taskListStyles.currentDensityStyles,
    vehicleDisplayMode: 'licensePlate',
    inTaskVehicleCardStyles: taskListStyles.inTaskVehicleCardStyles,
    productionLineCount,
    onDropOnProductionLine: modals.handleDropOnProductionLineWithModal,
    onCancelVehicleDispatch: () => { },
    onOpenStyleEditor: () => { },
    onOpenDeliveryOrderDetails: () => { },
    onOpenVehicleCardContextMenu: () => { },
    getColumnBackgroundProps: taskListStyles.getColumnBackgroundProps,
    settings: taskListSettings.settings,
  });

  // 任务卡片配置管理
  const taskCardConfigManager = useTaskCardConfigManager();

  // 卡片配置模态框状态
  const [isCardConfigModalOpen, setIsCardConfigModalOpen] = useState(false);

  // 打开卡片配置模态框的函数
  const openCardConfigModal = useCallback(() => {
    setIsCardConfigModalOpen(true);
  }, []);

  // 拖拽处理
  const dragHandlers = useTaskListDragHandlers();

  // 悬浮按钮
  const {
    position: fabPosition,
    isDragging: isFabDragging,
    ...fabProps
  } = useDraggableFab({
    initialPosition: {
      x: (typeof window !== 'undefined' ? window.innerWidth : 1280) - FAB_SIZE - FAB_MARGIN,
      y: (typeof window !== 'undefined' ? window.innerHeight : 720) / 2,
    },
    containerRef: { current: null },
    fabSize: FAB_SIZE,
    margin: FAB_MARGIN,
  });

  // 拖拽区域设置
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.VEHICLE_CARD_DISPATCH, ItemTypes.VEHICLE],
    drop: (item: any, monitor) => {
      if (!monitor.didDrop()) {
        dragHandlers.handleTaskListDrop(item, monitor);
      }
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
    }),
  });

  const { displayMode, density } = taskListSettings.settings;

  // 智能分组切换方法
  const handleSmartToggleGrouping = useCallback(() => {
    console.log('🔍 handleSmartToggleGrouping 触发:', {
      displayMode,
      currentEnabled: groupingStore.groupConfig.enabled,
      currentGroupBy: groupingStore.groupConfig.groupBy,
      timestamp: new Date().toLocaleTimeString(),
    });

    if (displayMode === 'card') {
      // 卡片模式：如果已分组则取消分组，否则打开列选择模态框
      if (groupingStore.groupConfig.enabled) {
        console.log('🔍 取消分组');
        groupingStore.updateGroupConfig({ enabled: false, groupBy: 'none' });
      } else {
        console.log('🔍 打开分组选择模态框');
        taskListSettings.openGroupByColumnSelect();
      }
    } else if (displayMode === 'table') {
      // 表格模式：如果已分组则取消分组，否则打开列选择模态框
      if (groupingStore.groupConfig.enabled) {
        console.log('🔍 表格模式取消分组');
        groupingStore.updateGroupConfig({ enabled: false, groupBy: 'none' });
      } else {
        console.log('🔍 表格模式打开分组选择模态框');
        taskListSettings.openGroupByColumnSelect();
      }
    } else {
      // 其他模式：使用切换逻辑
      console.log('🔍 其他模式分组切换');
      groupingStore.toggleGrouping();
    }
  }, [displayMode, groupingStore, taskListSettings]);

  return (
    <div
      ref={drop as any}
      className={`task-list-container h-full flex flex-col min-h-0 ${isOver ? 'drag-over' : ''}`}
    >
      {/* 头部区域 */}
      {headerMode.isFloatingHeader ? (
        <FloatingTaskListHeader
          displayMode={displayMode}
          onDisplayModeChange={taskListSettings.updateDisplayMode}
          densityMode={density}
          onDensityModeChange={taskListSettings.updateDensity}
          vehicleDisplayMode={taskListSettings.settings.vehicleDisplayMode}
          onVehicleDisplayModeChange={taskListSettings.updateVehicleDisplayMode}
          groupConfig={groupingStore.groupConfig}
          onToggleGrouping={handleSmartToggleGrouping}
          onCancelGrouping={() => {
            groupingStore.updateGroupConfig({ enabled: false, groupBy: 'none' });
          }}
          onSetGroupBy={groupBy => {
            console.log('🔍 onSetGroupBy 调用:', groupBy);
            groupingStore.setGroupBy(groupBy);
          }}
          columnVisibility={taskListSettings.settings.columnVisibility}
          onColumnVisibilityChange={taskListSettings.updateColumnVisibility}
          onOpenStyleEditor={taskListSettings.openStyleEditorModal}
          onOpenCardConfig={openCardConfigModal}
          onExportData={taskListBusinessLogic.handleExportData}
          onImportData={taskListBusinessLogic.handleImportData}
          onResetSettings={taskListSettings.resetSettings}
          isLoadingPlantInfo={isLoadingPlantInfo}
          taskCount={taskListData.allTasks.length}
          filteredTaskCount={taskListData.filteredTasks.length}
          initialPosition={headerMode.floatingPosition}
          onPositionChange={headerMode.handleFloatingPositionChange}
          onSwitchToFixedHeader={headerMode.handleSwitchToFixedHeader}
        />
      ) : (
        <TaskListHeader
          displayMode={displayMode}
          onDisplayModeChange={taskListSettings.updateDisplayMode}
          densityMode={density}
          onDensityModeChange={taskListSettings.updateDensity}
          vehicleDisplayMode={taskListSettings.settings.vehicleDisplayMode}
          onVehicleDisplayModeChange={taskListSettings.updateVehicleDisplayMode}
          groupConfig={groupingStore.groupConfig}
          onToggleGrouping={handleSmartToggleGrouping}
          onCancelGrouping={() => {
            groupingStore.updateGroupConfig({ enabled: false, groupBy: 'none' });
          }}
          onSetGroupBy={groupBy => {
            console.log('🔍 onSetGroupBy 调用 (固定头部):', groupBy);
            groupingStore.setGroupBy(groupBy);
          }}
          columnVisibility={taskListSettings.settings.columnVisibility}
          onColumnVisibilityChange={taskListSettings.updateColumnVisibility}
          onOpenStyleEditor={taskListSettings.openStyleEditorModal}
          onOpenCardConfig={openCardConfigModal}
          onExportData={taskListBusinessLogic.handleExportData}
          onImportData={taskListBusinessLogic.handleImportData}
          onResetSettings={taskListSettings.resetSettings}
          isLoadingPlantInfo={isLoadingPlantInfo}
          taskCount={taskListData.allTasks.length}
          filteredTaskCount={taskListData.filteredTasks.length}
          onSwitchToFloatingHeader={headerMode.handleSwitchToFloatingHeader}
        />
      )}

      {/* 主体内容 */}
      <TaskListBody
        displayMode={displayMode}
        densityMode={density}
        vehicleDisplayMode={taskListSettings.settings.vehicleDisplayMode}
        taskStatusFilter={taskStatusFilter}
        filteredTasks={taskListData.filteredTasks}
        tasksWithVehicles={taskListData.tasksWithVehicles}
        taskGroups={taskListData.taskGroups}
        allVehicles={taskListData.allVehicles}
        tableColumns={taskListColumns.tableColumns}
        settings={{
          columnWidths: taskListSettings.settings.columnWidths,
          columnOrder: taskListSettings.settings.columnOrder,
          columnVisibility: taskListSettings.settings.columnVisibility,
          enableZebraStriping: taskListSettings.settings.enableZebraStriping,
          groupConfig: taskListSettings.settings.groupConfig,
        }}
        groupConfig={groupingStore.groupConfig}
        densityStyles={taskListStyles.currentDensityStyles}
        taskCardConfig={taskCardConfigManager.taskCardConfig}
        tableTotalWidth={taskListStyles.tableTotalWidth}
        estimateRowHeight={() => taskListStyles.estimateRowHeight()}
        cardGridContainerClasses={taskListStyles.cardGridContainerClasses}
        getColumnBackgroundProps={(columnId: string) =>
          taskListStyles.getColumnBackgroundProps(columnId, false, false)
        }
        getCellTextClasses={(columnId: string) =>
          taskListColumns.getCellTextClasses(columnId as any)
        }
        getStatusLabelProps={taskListStyles.getStatusLabelProps}
        dragOverTaskId={null}
        setDragOverTaskId={() => { }}
        dragOverProductionLineId={null}
        setDragOverProductionLineId={() => { }}
        onColumnSizingChange={updater => {
          if (typeof updater === 'function') {
            // Handle function updater - get current state and apply the function
            const currentSizing = taskListSettings.settings.columnWidths;
            const newSizing = updater(currentSizing);
            // Apply each changed column width
            Object.entries(newSizing).forEach(([columnId, width]) => {
              if (currentSizing[columnId] !== width) {
                taskListSettings.handleSingleColumnWidthChange(columnId, width);
              }
            });
          } else {
            // Handle direct state updater
            Object.entries(updater).forEach(([columnId, width]) => {
              taskListSettings.handleSingleColumnWidthChange(columnId, width);
            });
          }
        }}
        onColumnOrderChange={updater => {
          if (typeof updater === 'function') {
            const currentOrder = taskListSettings.settings.columnOrder;
            const newOrder = updater(currentOrder);
            taskListSettings.handleColumnOrderChange(newOrder);
          } else {
            taskListSettings.handleColumnOrderChange(updater);
          }
        }}
        onColumnVisibilityChange={updater => {
          if (typeof updater === 'function') {
            const currentVisibility = taskListSettings.settings.columnVisibility;
            const newVisibility = updater(currentVisibility);
            // Apply each changed column visibility
            Object.entries(newVisibility).forEach(([columnId, visible]) => {
              if (currentVisibility[columnId] !== visible) {
                taskListSettings.handleColumnVisibilityChange(columnId, visible);
              }
            });
          } else {
            // Handle direct state updater
            Object.entries(updater).forEach(([columnId, visible]) => {
              taskListSettings.handleColumnVisibilityChange(columnId, visible);
            });
          }
        }}
        onHeaderContextMenu={(event, columnDef: CustomColumnDefinition) => {
          event.preventDefault();
          event.stopPropagation();
          // 检查是否可以按此列分组
          const allowedGroupColumns =
            taskListSettings.settings.groupConfig.allowedGroupColumns || [];
          if (allowedGroupColumns.includes(columnDef.id)) {
            taskListSettings.openGroupByColumnConfirm(columnDef);
          }
        }}
        onHeaderDoubleClick={(event, columnDef) => {
          event.preventDefault();
          event.stopPropagation();
          taskListSettings.openColumnSpecificStyleModal(columnDef);
        }}
        onRowContextMenu={(e, row) =>
          modals.taskContextMenu.openTaskContextMenu(e, row.original.id)
        }
        onRowDoubleClick={() => { }}
        onDropOnProductionLine={modals.handleDropOnProductionLineWithModal}
        onToggleGroupCollapse={groupingStore.toggleCollapse}
        onCancelGrouping={() => {
          groupingStore.updateGroupConfig({ enabled: false, groupBy: 'none' });
        }}
        onTaskContextMenu={(e, task) => modals.taskContextMenu.openTaskContextMenu(e, task.id)}
        onTaskDoubleClick={() => { }}
        onVehicleDrop={() => { }}
        onOpenVehicleCardContextMenu={modals.vehicleCardContextMenu.openContextMenu}
        onOpenDeliveryOrderDetailsForVehicle={(vehicleId: string, taskId: string) => {
          const vehicle = taskListData.allVehicles.find(v => v.id === vehicleId);
          const task = taskListData.getTaskById(taskId);
          if (vehicle) {
            modals.deliveryOrderDetailsModal.openDeliveryOrderDetailsModal(vehicle, task);
          }
        }}
        onOpenStyleEditor={taskListSettings.openStyleEditorModal}
        onCancelVehicleDispatch={taskListBusinessLogic.handleCancelVehicleDispatch}
        onVehicleDispatchedToLine={modals.handleDropOnProductionLineWithModal}
        onDropVehicleFromPanelOnTaskCard={
          taskListBusinessLogic.handleDropVehicleFromPanelOnTaskCard
        }
        onTaskCardConfigChange={taskCardConfigManager.handleTaskCardConfigChange}
        onOpenCardConfigModal={openCardConfigModal}
      />

      {/* 底部区域 */}
      <TaskListFooter
        allTasks={taskListData.allTasks}
        filteredTasks={taskListData.filteredTasks}
        allVehicles={taskListData.allVehicles}
        taskStatusFilter='all'
        selectedTaskIds={[]}
        onClearSelection={() => { }}
        onBulkAction={() => { }}
      />

      {/* 模态框管理器 */}
      <TaskListModalManager
        // Tanker Note Modal
        isTankerNoteModalOpen={false}
        closeTankerNoteModal={() => { }}
        selectedTaskForTankerNote={null}
        // Column Visibility Modal
        isColumnVisibilityModalOpen={taskListSettings.isColumnVisibilityModalOpen}
        closeColumnVisibilityModal={taskListSettings.closeColumnVisibilityModal}
        openColumnVisibilityModal={taskListSettings.openColumnVisibilityModal}
        allColumns={ALL_TASK_COLUMNS_CONFIG}
        columnVisibility={taskListSettings.settings.columnVisibility}
        handleColumnVisibilityChange={taskListSettings.updateColumnVisibility}
        currentOrder={taskListSettings.settings.columnOrder}
        handleColumnOrderChange={taskListSettings.handleColumnOrderChange}
        // Column Specific Style Modal
        isColumnSpecificStyleModalOpen={taskListSettings.isColumnSpecificStyleModalOpen}
        closeColumnSpecificStyleModal={taskListSettings.closeColumnSpecificStyleModal}
        editingColumnDef={taskListSettings.editingColumnDef}
        columnTextStyles={convertColumnTextStyles(taskListSettings.settings.columnTextStyles)}
        columnBackgrounds={taskListSettings.settings.columnBackgrounds}
        handleColumnTextStyleChange={taskListSettings.handleColumnTextStyleChange}
        handleColumnBackgroundChange={taskListSettings.handleColumnBackgroundChange}
        // Vehicle Card Styler Modal
        isStyleEditorModalOpen={taskListSettings.isStyleEditorModalOpen}
        closeStyleEditorModal={taskListSettings.closeStyleEditorModal}
        inTaskVehicleCardStyles={taskListSettings.settings.inTaskVehicleCardStyles}
        updateSetting={taskListSettings.updateSetting}
        // Delivery Order Details Modal
        isDeliveryOrderDetailsModalOpen={
          modals.deliveryOrderDetailsModal.isDeliveryOrderDetailsModalOpen
        }
        closeDeliveryOrderDetailsModal={
          modals.deliveryOrderDetailsModal.closeDeliveryOrderDetailsModal
        }
        selectedVehicleForDeliveryOrder={
          modals.deliveryOrderDetailsModal.selectedVehicleForDeliveryOrder
        }
        selectedTaskForDeliveryOrder={modals.deliveryOrderDetailsModal.selectedTaskForDeliveryOrder}
        // Tank Truck Dispatch Modal
        tankTruckDispatchModal={modals.tankTruckDispatchModal}
        // Task Reminder Config Modal
        isReminderConfigModalOpen={false}
        closeReminderConfigModal={() => { }}
        selectedTaskForReminderConfig={null}
        // QR Code Modal
        isQRCodeModalOpen={false}
        closeQRCodeModal={() => { }}
        selectedTaskForQRCode={null}
        // Task Abbreviation Modal
        isTaskAbbreviationModalOpen={false}
        closeTaskAbbreviationModal={() => { }}
        selectedTaskForAbbreviation={null}
        handleSaveAbbreviation={async () => { }}
        // Task Card Config Modal
        taskCardConfigModalOpen={isCardConfigModalOpen}
        setTaskCardConfigModalOpen={setIsCardConfigModalOpen}
        taskCardConfig={taskCardConfigManager.taskCardConfig}
        handleTaskCardConfigChange={taskCardConfigManager.handleTaskCardConfigChange}
        // Group Config Modal
        isGroupConfigModalOpen={false}
        setIsGroupConfigModalOpen={() => { }}
        groupConfig={taskListSettings.settings.groupConfig}
        handleOpenGroupConfig={() => { }}
        // Context Menus
        isTaskContextMenuOpen={modals.taskContextMenu.isTaskContextMenuOpen}
        taskContextMenuPosition={modals.taskContextMenu.taskContextMenuPosition}
        contextMenuTaskData={modals.taskContextMenu.contextMenuTaskData}
        closeTaskContextMenu={modals.taskContextMenu.closeTaskContextMenu}
        openTankerNoteModal={modals.taskContextMenu.openTankerNoteModal}
        openReminderConfigModal={modals.taskContextMenu.openReminderConfigModal}
        openQRCodeModal={modals.qrCodeModal.openQRCodeModal}
        openTaskAbbreviationModal={modals.taskAbbreviationModal.openTaskAbbreviationModal}
        filteredTasks={taskListData.filteredTasks}
        // Display mode
        currentDisplayMode={displayMode}
        onSetDisplayMode={taskListSettings.updateDisplayMode}
        isVehicleCardContextMenuOpen={modals.vehicleCardContextMenu.isContextMenuOpen}
        vehicleCardContextMenuPosition={modals.vehicleCardContextMenu.contextMenuPosition}
        vehicleCardContextMenuContext={modals.vehicleCardContextMenu.contextMenuData}
        closeVehicleCardContextMenu={modals.vehicleCardContextMenu.closeContextMenu}
        // Vehicle Dispatch Modal
        isVehicleDispatchModalOpen={modals.isVehicleDispatchModalOpen}
        closeVehicleDispatchModal={modals.closeVehicleDispatchModal}
        selectedTaskForVehicleDispatch={modals.selectedTaskForVehicleDispatch}
        openVehicleDispatchModal={modals.openVehicleDispatchModal}
        // Task Progress Modal
        isTaskProgressModalOpen={modals.isTaskProgressModalOpen}
        closeTaskProgressModal={modals.closeTaskProgressModal}
        selectedTaskForProgress={modals.selectedTaskForProgress}
        openTaskProgressModal={modals.openTaskProgressModal}
        // Ratio History Modal
        isRatioHistoryModalOpen={modals.isRatioHistoryModalOpen}
        closeRatioHistoryModal={modals.closeRatioHistoryModal}
        selectedTaskForRatioHistory={modals.selectedTaskForRatioHistory}
        openRatioHistoryModal={modals.openRatioHistoryModal}
        // Group By Column Confirm Dialog
        isGroupByColumnConfirmOpen={taskListSettings.isGroupByColumnConfirmOpen}
        groupingColumnDef={taskListSettings.groupingColumnDef}
        closeGroupByColumnConfirm={taskListSettings.closeGroupByColumnConfirm}
        confirmGroupByColumn={taskListSettings.confirmGroupByColumn}
        // Group By Column Select Modal
        isGroupByColumnSelectOpen={taskListSettings.isGroupByColumnSelectOpen}
        closeGroupByColumnSelect={taskListSettings.closeGroupByColumnSelect}
        confirmGroupByColumnSelect={handleGroupByColumnSelect}
        currentGroupConfig={groupingStore.groupConfig}
      />
    </div>
  );
}
