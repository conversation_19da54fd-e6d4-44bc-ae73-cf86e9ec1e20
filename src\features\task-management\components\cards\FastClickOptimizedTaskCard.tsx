'use client';

/**
 * 快速点击优化的任务卡片组件
 * 专门解决快速连续点击时的延时和卡顿问题
 */

import React, { memo, useCallback, useRef, useMemo } from 'react';
import { cn } from '@/core/lib/utils';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';
import {
  useTaskSelectionState,
  useTaskSelectionActions,
} from '@/core/contexts/TaskSelectionContext';
import { ConfigurableTaskCard } from './ConfigurableTaskCard';
import { safeTemporaryClass } from '@/core/utils/dom-safe-operations';

interface FastClickOptimizedTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small';
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onTaskClick?: (task: Task, event: React.MouseEvent) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 快速点击防抖管理器
 */
class FastClickManager {
  private static instance: FastClickManager;
  private clickTimeouts = new Map<string, NodeJS.Timeout>();
  private lastClickTime = new Map<string, number>();
  private isProcessing = new Map<string, boolean>();

  static getInstance(): FastClickManager {
    if (!FastClickManager.instance) {
      FastClickManager.instance = new FastClickManager();
    }
    return FastClickManager.instance;
  }

  /**
   * 处理快速点击，使用防抖和节流结合
   */
  handleFastClick(
    taskId: string,
    callback: () => void,
    options: {
      debounceMs?: number;
      throttleMs?: number;
    } = {}
  ): boolean {
    const { debounceMs = 50, throttleMs = 16 } = options; // 16ms ≈ 60fps
    const now = performance.now();
    const lastClick = this.lastClickTime.get(taskId) || 0;

    // 如果正在处理中，直接忽略
    if (this.isProcessing.get(taskId)) {
      return false;
    }

    // 节流：如果距离上次点击时间太短，忽略
    if (now - lastClick < throttleMs) {
      return false;
    }

    // 清除之前的防抖定时器
    const existingTimeout = this.clickTimeouts.get(taskId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // 设置新的防抖定时器
    const timeout = setTimeout(() => {
      this.isProcessing.set(taskId, true);

      // 使用 requestAnimationFrame 确保在下一帧执行
      requestAnimationFrame(() => {
        try {
          callback();
        } finally {
          this.isProcessing.set(taskId, false);
          this.clickTimeouts.delete(taskId);
        }
      });
    }, debounceMs);

    this.clickTimeouts.set(taskId, timeout);
    this.lastClickTime.set(taskId, now);

    return true;
  }

  /**
   * 清理指定任务的点击状态
   */
  cleanup(taskId: string) {
    const timeout = this.clickTimeouts.get(taskId);
    if (timeout) {
      clearTimeout(timeout);
      this.clickTimeouts.delete(taskId);
    }
    this.lastClickTime.delete(taskId);
    this.isProcessing.delete(taskId);
  }

  /**
   * 清理所有状态
   */
  cleanupAll() {
    this.clickTimeouts.forEach(timeout => clearTimeout(timeout));
    this.clickTimeouts.clear();
    this.lastClickTime.clear();
    this.isProcessing.clear();
  }
}

/**
 * 快速点击优化钩子
 */
const useFastClickOptimization = (taskId: string) => {
  const { isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask } = useTaskSelectionActions();
  const clickManagerRef = useRef(FastClickManager.getInstance());
  const isSelected = isTaskSelected(taskId);

  // 优化的点击处理器 - 使用防抖和节流
  const handleOptimizedClick = useCallback(
    (task: Task, event: React.MouseEvent) => {
      // 阻止事件冒泡
      event.stopPropagation();
      event.preventDefault();

      // 检查是否点击了交互元素
      const target = event.target as HTMLElement;
      if (target.closest('button, a, input, select, textarea, [role="button"]')) {
        return;
      }

      // 使用快速点击管理器处理
      const handled = clickManagerRef.current.handleFastClick(
        taskId,
        () => {
          // 实际的选中逻辑
          const currentlySelected = isTaskSelected(taskId);
          if (currentlySelected) {
            setSelectedTask(null);
          } else {
            setSelectedTask(task);
          }
        },
        {
          debounceMs: 30, // 30ms 防抖
          throttleMs: 16, // 16ms 节流 (60fps)
        }
      );

      // 如果点击被处理，立即提供视觉反馈
      if (handled) {
        const cardElement = event.currentTarget as HTMLElement;
        safeTemporaryClass(cardElement, 'fast-click-feedback', 100);
      }
    },
    [taskId, isTaskSelected, setSelectedTask]
  );

  // 清理函数
  const cleanup = useCallback(() => {
    clickManagerRef.current.cleanup(taskId);
  }, [taskId]);

  return {
    isSelected,
    handleOptimizedClick,
    cleanup,
  };
};

/**
 * 快速点击优化的任务卡片组件
 */
export const FastClickOptimizedTaskCard = memo<FastClickOptimizedTaskCardProps>(
  props => {
    const { task, onTaskClick, className, ...restProps } = props;
    const { isSelected, handleOptimizedClick, cleanup } = useFastClickOptimization(task.id);

    // 组件卸载时清理
    React.useEffect(() => {
      return cleanup;
    }, [cleanup]);

    // 最终的点击处理器
    const finalClickHandler = useCallback(
      (task: Task, event: React.MouseEvent) => {
        // 如果有外部点击处理函数，优先使用
        if (onTaskClick) {
          onTaskClick(task, event);
          return;
        }

        // 使用优化的点击处理
        handleOptimizedClick(task, event);
      },
      [onTaskClick, handleOptimizedClick]
    );

    // 优化的样式计算
    const optimizedClassName = useMemo(() => {
      return cn(
        'fast-click-optimized-card',
        // 使用 CSS 变量控制选中状态
        isSelected && 'task-row-selected',
        className
      );
    }, [isSelected, className]);

    return (
      <div
        className={optimizedClassName}
        style={{
          // 启用 GPU 加速
          transform: 'translateZ(0)',
          // 优化渲染性能
          contain: 'layout style paint',
          // 快速点击反馈样式
          transition: 'all 0.1s ease-out',
        }}
      >
        <ConfigurableTaskCard
          {...restProps}
          task={task}
          onTaskClick={finalClickHandler}
          className={cn(
            // 快速点击优化样式
            'transition-all duration-100 ease-out',
            // 选中状态样式
            isSelected && [
              'bg-accent/10',
              'border-accent/40',
              'border-l-accent border-l-4',
              'shadow-md',
              'transform translate-y-[-1px]',
              'relative z-10',
            ]
          )}
        />
      </div>
    );
  },
  // 优化的比较函数 - 只比较关键属性
  (prevProps, nextProps) => {
    return (
      prevProps.task.id === nextProps.task.id &&
      prevProps.task.taskNumber === nextProps.task.taskNumber &&
      prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
      prevProps.task.completedVolume === nextProps.task.completedVolume &&
      prevProps.task.requiredVolume === nextProps.task.requiredVolume &&
      prevProps.vehicles.length === nextProps.vehicles.length &&
      // 简化的车辆比较
      prevProps.vehicles.every(
        (vehicle, index) =>
          vehicle.id === nextProps.vehicles[index]?.id &&
          vehicle.status === nextProps.vehicles[index]?.status
      )
    );
  }
);

FastClickOptimizedTaskCard.displayName = 'FastClickOptimizedTaskCard';

export default FastClickOptimizedTaskCard;
