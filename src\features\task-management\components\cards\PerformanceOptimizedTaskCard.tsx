'use client';

/**
 * 性能优化的任务卡片组件
 * 专门解决选中状态导致的性能问题
 */

import React, { memo, useCallback, useMemo, useRef, useEffect } from 'react';
import { cn } from '@/core/lib/utils';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';
import {
  useTaskSelectionState,
  useTaskSelectionActions,
} from '@/core/contexts/TaskSelectionContext';
import { ConfigurableTaskCard } from './ConfigurableTaskCard';

interface PerformanceOptimizedTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small';
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onTaskClick?: (task: Task, event: React.MouseEvent) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 选中状态管理器 - 优化选中状态的性能
 */
const useOptimizedSelection = (taskId: string) => {
  const { isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask } = useTaskSelectionActions();
  const cardRef = useRef<HTMLDivElement>(null);
  const isSelected = isTaskSelected(taskId);
  const previousSelectedRef = useRef(isSelected);

  // 优化选中状态变化的处理
  useEffect(() => {
    if (isSelected !== previousSelectedRef.current) {
      previousSelectedRef.current = isSelected;

      // 使用 requestAnimationFrame 优化 DOM 操作
      if (cardRef.current) {
        requestAnimationFrame(() => {
          if (cardRef.current) {
            if (isSelected) {
              // 添加选中状态类
              cardRef.current.classList.add('task-row-selected');
              // 使用 CSS 变量避免强制重绘
              cardRef.current.style.setProperty('--selection-opacity', '1');
            } else {
              // 移除选中状态类
              cardRef.current.classList.remove('task-row-selected');
              cardRef.current.style.setProperty('--selection-opacity', '0');
            }
          }
        });
      }
    }
  }, [isSelected]);

  const handleSelection = useCallback(
    (task: Task, event: React.MouseEvent) => {
      // 防止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();

      // 避免在点击交互元素时触发选中
      const target = event.target as HTMLElement;
      if (target.closest('button, a, input, select, textarea, [role="button"]')) {
        return;
      }

      // 使用 requestIdleCallback 优化状态更新
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          if (isSelected) {
            setSelectedTask(null);
          } else {
            setSelectedTask(task);
          }
        });
      } else {
        // 降级到 setTimeout
        setTimeout(() => {
          if (isSelected) {
            setSelectedTask(null);
          } else {
            setSelectedTask(task);
          }
        }, 0);
      }
    },
    [isSelected, setSelectedTask]
  );

  return {
    isSelected,
    cardRef,
    handleSelection,
  };
};

/**
 * 性能优化的任务卡片组件
 */
export const PerformanceOptimizedTaskCard = memo<PerformanceOptimizedTaskCardProps>(
  props => {
    const { task, onTaskClick, className, ...restProps } = props;
    const { isSelected, cardRef, handleSelection } = useOptimizedSelection(task.id);

    // 优化的点击处理器
    const handleTaskClick = useCallback(
      (task: Task, event: React.MouseEvent) => {
        // 如果有外部点击处理函数，优先使用
        if (onTaskClick) {
          onTaskClick(task, event);
          return;
        }

        // 使用优化的选中处理
        handleSelection(task, event);
      },
      [onTaskClick, handleSelection]
    );

    // 优化的样式计算 - 使用 CSS 变量减少重绘
    const optimizedClassName = useMemo(() => {
      return cn(
        'performance-optimized-card',
        // 移除动态的选中状态类，改用 CSS 变量
        className
      );
    }, [className]);

    return (
      <div
        ref={cardRef}
        className={optimizedClassName}
        style={
          {
            // 使用 CSS 变量控制选中状态，避免类切换导致的重绘
            ['--selection-opacity' as any]: isSelected ? '1' : '0',
            // 启用 GPU 加速
            transform: 'translateZ(0)',
            // 优化渲染性能
            contain: 'layout style paint',
          } as React.CSSProperties
        }
      >
        <ConfigurableTaskCard
          {...restProps}
          task={task}
          onTaskClick={handleTaskClick}
          className={cn(
            // 使用 CSS 变量控制选中状态样式
            'transition-all duration-200 ease-out',
            '[&.task-row-selected]:bg-[hsl(var(--accent)/calc(0.12*var(--selection-opacity)))]',
            '[&.task-row-selected]:border-[hsl(var(--accent)/calc(0.4*var(--selection-opacity)))]',
            '[&.task-row-selected]:shadow-[0_4px_12px_hsl(var(--accent)/calc(0.2*var(--selection-opacity)))]'
          )}
        />
      </div>
    );
  },
  // 优化的比较函数 - 减少不必要的重新渲染
  (prevProps, nextProps) => {
    // 只在关键属性变化时重新渲染
    return (
      prevProps.task.id === nextProps.task.id &&
      prevProps.task.taskNumber === nextProps.task.taskNumber &&
      prevProps.task.projectName === nextProps.task.projectName &&
      prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
      prevProps.task.completedVolume === nextProps.task.completedVolume &&
      prevProps.task.requiredVolume === nextProps.task.requiredVolume &&
      prevProps.vehicles.length === nextProps.vehicles.length &&
      // 浅比较车辆状态
      prevProps.vehicles.every(
        (vehicle, index) =>
          vehicle.id === nextProps.vehicles[index]?.id &&
          vehicle.status === nextProps.vehicles[index]?.status
      ) &&
      // 使用 JSON 比较配置（可以进一步优化为深度比较关键字段）
      JSON.stringify(prevProps.config) === JSON.stringify(nextProps.config) &&
      prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
      prevProps.density === nextProps.density
    );
  }
);

PerformanceOptimizedTaskCard.displayName = 'PerformanceOptimizedTaskCard';

/**
 * 虚拟化性能优化行组件
 */
interface PerformanceOptimizedRowProps {
  index: number;
  tasks: Task[];
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small';
  columnsPerRow: number;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onTaskClick?: (task: Task, event: React.MouseEvent) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  gridColumns: string;
  spacingClass: string;
}

export const PerformanceOptimizedRow = memo<PerformanceOptimizedRowProps>(
  props => {
    const { index, tasks, vehicles, columnsPerRow, gridColumns, spacingClass, ...cardProps } =
      props;

    // 计算当前行的任务 - 使用 useMemo 优化
    const rowTasks = useMemo(() => {
      const startIndex = index * columnsPerRow;
      const endIndex = Math.min(startIndex + columnsPerRow, tasks.length);
      return tasks.slice(startIndex, endIndex);
    }, [index, columnsPerRow, tasks]);

    // 缓存车辆分组 - 使用 useMemo 优化
    const vehiclesByTask = useMemo(() => {
      const map = new Map<string, Vehicle[]>();
      const validVehicles = Array.isArray(vehicles) ? vehicles : [];

      validVehicles.forEach(vehicle => {
        if (vehicle && vehicle.assignedTaskId) {
          if (!map.has(vehicle.assignedTaskId)) {
            map.set(vehicle.assignedTaskId, []);
          }
          map.get(vehicle.assignedTaskId)!.push(vehicle);
        }
      });
      return map;
    }, [vehicles]);

    return (
      <div
        className={cn('grid mt-2 w-full performance-optimized-row', gridColumns, spacingClass)}
        style={{
          // 启用 GPU 加速和优化
          transform: 'translateZ(0)',
          contain: 'layout style paint',
          // 使用 will-change 提示浏览器优化
          willChange: 'transform',
        }}
      >
        {rowTasks.map(task => {
          const taskVehicles =
            task && task.id && vehiclesByTask instanceof Map
              ? vehiclesByTask.get(task.id) || []
              : [];

          return (
            <PerformanceOptimizedTaskCard
              key={task.id}
              task={task}
              vehicles={taskVehicles}
              {...cardProps}
            />
          );
        })}
      </div>
    );
  },
  // 优化的比较函数
  (prevProps, nextProps) => {
    return (
      prevProps.index === nextProps.index &&
      prevProps.tasks.length === nextProps.tasks.length &&
      prevProps.vehicles.length === nextProps.vehicles.length &&
      prevProps.columnsPerRow === nextProps.columnsPerRow &&
      prevProps.gridColumns === nextProps.gridColumns &&
      prevProps.spacingClass === nextProps.spacingClass &&
      // 深度比较关键配置
      JSON.stringify(prevProps.config) === JSON.stringify(nextProps.config)
    );
  }
);

PerformanceOptimizedRow.displayName = 'PerformanceOptimizedRow';

export default PerformanceOptimizedTaskCard;
