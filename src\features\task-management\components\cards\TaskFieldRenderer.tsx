'use client';

import React, { useMemo } from 'react';

import {
  AlertTriangle,
  Building2,
  Calendar,
  CheckCircle,
  CheckCircle2,
  Clock,
  Droplets,
  FileText,
  Hash,
  MapPin,
  Pause,
  Phone,
  Settings,
  Target,
  Timer,
  TruckIcon,
  User,
  XCircle,
} from 'lucide-react';

import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

interface FieldConfig {
  id: string;
  label: string;
  visible: boolean;
  order: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  weight?: 'normal' | 'medium' | 'bold';
}

interface TaskFieldRendererProps {
  field: FieldConfig;
  task: Task;
  size: 'small' | 'medium' | 'large';
}

// 字段图标映射
const FIELD_ICONS: Record<string, React.ReactNode> = {
  taskNumber: <Hash className='w-4 h-4' />,
  constructionSite: <Building2 className='w-4 h-4' />,
  customerName: <User className='w-4 h-4' />,
  status: <AlertTriangle className='w-4 h-4' />,
  requiredVolume: <Target className='w-4 h-4' />,
  completedVolume: <Droplets className='w-4 h-4' />,
  scheduledTime: <Calendar className='w-4 h-4' />,
  estimatedDuration: <Timer className='w-4 h-4' />,
  contactPhone: <Phone className='w-4 h-4' />,
  notes: <FileText className='w-4 h-4' />,
  address: <MapPin className='w-4 h-4' />,
  createdAt: <Clock className='w-4 h-4' />,
};

// 状态颜色映射
const STATUS_COLORS: Record<string, string> = {
  New: 'text-gray-600 bg-gray-50 border-gray-200',
  ReadyToProduce: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  RatioSet: 'text-orange-600 bg-orange-50 border-orange-200',
  InProgress: 'text-blue-600 bg-blue-50 border-blue-200',
  Paused: 'text-purple-600 bg-purple-50 border-purple-200',
  Completed: 'text-green-600 bg-green-50 border-green-200',
  Cancelled: 'text-red-600 bg-red-50 border-red-200',
};

export const TaskFieldRenderer: React.FC<TaskFieldRendererProps> = React.memo(
  ({ field, task, size }) => {
    // 获取字段值
    const fieldValue = useMemo(() => {
      switch (field.id) {
        case 'taskNumber':
          return task.taskNumber;
        case 'constructionSite':
          return task.constructionSite;
        case 'customerName':
          return task.customerName;
        case 'status':
          return getStatusLabel(task.status);
        case 'requiredVolume':
          return `${task.requiredVolume}m³`;
        case 'completedVolume':
          return `${task.completedVolume}m³`;
        case 'scheduledTime':
          return task.scheduledTime ? formatTime(task.scheduledTime) : '未设置';
        case 'estimatedDuration':
          return task.estimatedDuration ? `${task.estimatedDuration}分钟` : '未设置';
        case 'contactPhone':
          return task.contactPhone || '未设置';
        case 'notes':
          return task.notes || '无备注';
        case 'address':
          return task.address || '未设置';
        case 'createdAt':
          return task.createdAt ? formatDate(task.createdAt) : '未设置';
        case 'progress':
          return `${Math.round((task.completedVolume / task.requiredVolume) * 100)}%`;
        default:
          return '未知字段';
      }
    }, [field.id, task]);

    // 获取字段样式
    const fieldStyles = useMemo(() => {
      const baseStyles = 'flex items-center gap-2 p-2 rounded-md transition-all duration-200';

      // 尺寸样式
      const sizeStyles = {
        small: 'text-xs',
        medium: 'text-sm',
        large: 'text-base',
      };

      // 字重样式
      const weightStyles = {
        normal: 'font-normal',
        medium: 'font-medium',
        bold: 'font-bold',
      };

      // 特殊字段样式
      let specialStyles = '';
      if (field.id === 'status') {
        specialStyles = STATUS_COLORS[task.status] || 'text-gray-600 bg-gray-50 border-gray-200';
      } else if (field.id === 'taskNumber') {
        specialStyles = 'bg-primary/10 text-primary border border-primary/20 font-semibold';
      } else if (field.id === 'constructionSite') {
        specialStyles = 'bg-blue/10 text-blue-700 border border-blue/20';
      } else {
        specialStyles = 'bg-muted/50 text-foreground border border-border/50 hover:bg-muted/70';
      }

      return cn(
        baseStyles,
        sizeStyles[size],
        weightStyles[field.weight || 'normal'],
        specialStyles,
        field.color && `text-${field.color}`
      );
    }, [field, size, task.status]);

    // 获取图标
    const icon = FIELD_ICONS[field.id] || <FileText className='w-4 h-4' />;

    // 特殊渲染：进度条
    if (field.id === 'progress') {
      const percentage = Math.round((task.completedVolume / task.requiredVolume) * 100);
      return (
        <div className={cn('space-y-1', fieldStyles)}>
          <div className='flex items-center justify-between text-xs'>
            <span className='flex items-center gap-1'>
              {icon}
              {field.label}
            </span>
            <span className='font-medium'>{percentage}%</span>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-primary h-2 rounded-full transition-all duration-300'
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>
      );
    }

    // 特殊渲染：状态徽章
    if (field.id === 'status') {
      return (
        <div className={cn(fieldStyles, 'border')}>
          {getStatusIcon(task.status)}
          <span className='font-medium'>{fieldValue}</span>
        </div>
      );
    }

    // 标准字段渲染
    return (
      <div className={fieldStyles}>
        <div className='flex-shrink-0 text-muted-foreground'>{icon}</div>
        <div className='flex-1 min-w-0'>
          <div className='text-xs text-muted-foreground truncate'>{field.label}</div>
          <div className='font-medium truncate' title={String(fieldValue)}>
            {fieldValue}
          </div>
        </div>
      </div>
    );
  }
);

// 辅助函数
function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    New: '新建',
    ReadyToProduce: '待生产',
    RatioSet: '配比设定',
    InProgress: '进行中',
    Paused: '暂停',
    Completed: '已完成',
    Cancelled: '已取消',
  };
  return labels[status] || status;
}

function getStatusIcon(status: string): React.ReactNode {
  switch (status) {
    case 'New':
      return <FileText className='w-4 h-4' />;
    case 'ReadyToProduce':
      return <Pause className='w-4 h-4' />;
    case 'RatioSet':
      return <Settings className='w-4 h-4' />;
    case 'InProgress':
      return <Clock className='w-4 h-4' />;
    case 'Paused':
      return <Pause className='w-4 h-4' />;
    case 'Completed':
      return <CheckCircle2 className='w-4 h-4' />;
    case 'Cancelled':
      return <XCircle className='w-4 h-4' />;
    default:
      return <AlertTriangle className='w-4 h-4' />;
  }
}

function formatTime(time: string): string {
  try {
    return new Date(time).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return time;
  }
}

function formatDate(date: string): string {
  try {
    return new Date(date).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return date;
  }
}

TaskFieldRenderer.displayName = 'TaskFieldRenderer';
