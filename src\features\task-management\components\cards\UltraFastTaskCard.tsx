'use client';

/**
 * 超快速任务卡片组件
 * 专门解决卡片选中状态切换时的严重延迟和性能问题
 * 针对"从卡片A切换到卡片B需要1秒"的问题进行极致优化
 */

import React, { memo, useCallback, useRef, useMemo, useState, useEffect } from 'react';
import { cn } from '@/core/lib/utils';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';
import {
  useTaskSelectionState,
  useTaskSelectionActions,
} from '@/core/contexts/TaskSelectionContext';
import { ConfigurableTaskCard } from './ConfigurableTaskCard';
import { safeTemporaryClassAnimated } from '@/core/utils/dom-safe-operations';

interface UltraFastTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small';
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onTaskClick?: (task: Task, event: React.MouseEvent) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 超快速选中状态管理器
 * 使用本地状态 + 延迟同步策略，避免全局状态更新的性能开销
 */
class UltraFastSelectionManager {
  private static instance: UltraFastSelectionManager;
  private localSelectedId: string | null = null;
  private globalSyncTimeout: NodeJS.Timeout | null = null;
  private subscribers = new Set<(selectedId: string | null) => void>();
  private globalSetSelectedTask: ((task: Task | null) => void) | null = null;
  private tasksCache = new Map<string, Task>();

  static getInstance(): UltraFastSelectionManager {
    if (!UltraFastSelectionManager.instance) {
      UltraFastSelectionManager.instance = new UltraFastSelectionManager();
    }
    return UltraFastSelectionManager.instance;
  }

  /**
   * 设置全局状态更新函数
   */
  setGlobalUpdater(setSelectedTask: (task: Task | null) => void) {
    this.globalSetSelectedTask = setSelectedTask;
  }

  /**
   * 缓存任务对象
   */
  cacheTask(task: Task) {
    this.tasksCache.set(task.id, task);
  }

  /**
   * 订阅选中状态变化
   */
  subscribe(callback: (selectedId: string | null) => void): () => void {
    this.subscribers.add(callback);
    // 立即通知当前状态
    callback(this.localSelectedId);

    return () => {
      this.subscribers.delete(callback);
    };
  }

  /**
   * 立即更新本地选中状态，延迟同步到全局状态
   */
  setSelectedId(taskId: string | null) {
    // 立即更新本地状态
    this.localSelectedId = taskId;

    // 立即通知所有订阅者
    this.subscribers.forEach(callback => callback(taskId));

    // 清除之前的同步定时器
    if (this.globalSyncTimeout) {
      clearTimeout(this.globalSyncTimeout);
    }

    // 延迟同步到全局状态（减少全局状态更新频率）
    this.globalSyncTimeout = setTimeout(() => {
      try {
        if (this.globalSetSelectedTask) {
          const task = taskId ? this.tasksCache.get(taskId) || null : null;
          this.globalSetSelectedTask(task);
        }
      } catch (error) {
        console.warn('Global sync error:', error);
      } finally {
        this.globalSyncTimeout = null;
      }
    }, 100); // 100ms延迟同步
  }

  /**
   * 获取当前选中ID
   */
  getSelectedId(): string | null {
    return this.localSelectedId;
  }

  /**
   * 同步全局状态到本地
   */
  syncFromGlobal(globalSelectedId: string | null) {
    if (this.localSelectedId !== globalSelectedId) {
      this.localSelectedId = globalSelectedId;
      this.subscribers.forEach(callback => callback(globalSelectedId));
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      if (this.globalSyncTimeout) {
        clearTimeout(this.globalSyncTimeout);
        this.globalSyncTimeout = null;
      }
      this.subscribers.clear();
      this.tasksCache.clear();
    } catch (error) {
      // 静默处理清理错误，避免影响用户体验
      console.warn('UltraFastSelectionManager cleanup error:', error);
    }
  }
}

/**
 * 超快速选中状态钩子
 */
/**
 * 自定义Hook：用于实现超快速任务卡片选择功能
 *
 * 该Hook封装了任务选择的状态管理和点击处理逻辑，提供以下功能：
 * 1. 管理本地选中状态与全局状态的同步
 * 2. 实现防快速点击机制（10ms间隔）
 * 3. 自动跳过交互元素的点击事件
 * 4. 提供即时视觉反馈
 * 5. 组件卸载时自动清理状态
 *
 * @param task - 当前卡片对应的任务对象
 * @returns {Object} 包含以下属性：
 *   - isSelected: 当前任务是否被选中
 *   - handleUltraFastClick: 点击事件处理函数
 */
const useUltraFastSelection = (task: Task) => {
  const { selectedTaskId } = useTaskSelectionState();
  const { setSelectedTask } = useTaskSelectionActions();
  const [localIsSelected, setLocalIsSelected] = useState(false);
  const managerRef = useRef(UltraFastSelectionManager.getInstance());
  const lastClickTimeRef = useRef(0);

  // 缓存任务对象
  useEffect(() => {
    managerRef.current.cacheTask(task);
  }, [task]);

  // 设置全局更新函数
  useEffect(() => {
    managerRef.current.setGlobalUpdater(setSelectedTask);
  }, [setSelectedTask]);

  // 订阅本地选中状态变化
  useEffect(() => {
    const unsubscribe = managerRef.current.subscribe(selectedId => {
      setLocalIsSelected(selectedId === task.id);
    });

    return unsubscribe;
  }, [task.id]);

  // 同步全局状态到本地（仅在必要时）
  useEffect(() => {
    managerRef.current.syncFromGlobal(selectedTaskId);
  }, [selectedTaskId]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 组件卸载时清理相关状态
      try {
        const manager = managerRef.current;
        if (manager.getSelectedId() === task.id) {
          manager.setSelectedId(null);
        }
      } catch (error) {
        // 静默处理清理错误
        console.warn('Component cleanup error:', error);
      }
    };
  }, [task.id]);

  // 超快速点击处理器
  const handleUltraFastClick = useCallback(
    (event: React.MouseEvent) => {
      const now = performance.now();

      // 防止过快点击（小于10ms的点击被忽略）
      if (now - lastClickTimeRef.current < 10) {
        return;
      }
      lastClickTimeRef.current = now;

      // 阻止事件冒泡
      event.stopPropagation();
      event.preventDefault();

      // 检查是否点击了交互元素
      const target = event.target as HTMLElement;
      if (target.closest('button, a, input, select, textarea, [role="button"]')) {
        return;
      }

      // 立即更新选中状态（不等待全局状态）
      const currentSelected = managerRef.current.getSelectedId();
      const newSelectedId = currentSelected === task.id ? null : task.id;

      // 立即更新本地状态
      managerRef.current.setSelectedId(newSelectedId);

      // 立即提供视觉反馈
      const cardElement = event.currentTarget as HTMLElement;
      safeTemporaryClassAnimated(cardElement, 'ultra-fast-feedback');
    },
    [task.id]
  );

  return {
    isSelected: localIsSelected,
    handleUltraFastClick,
  };
};

/**
 * 超快速任务卡片组件
 */
export const UltraFastTaskCard = memo<UltraFastTaskCardProps>(
  props => {
    const { task, onTaskClick, className, ...restProps } = props;
    const { isSelected, handleUltraFastClick } = useUltraFastSelection(task);

    // 最终的点击处理器
    const finalClickHandler = useCallback(
      (task: Task, event: React.MouseEvent) => {
        // 如果有外部点击处理函数，优先使用
        if (onTaskClick) {
          onTaskClick(task, event);
          return;
        }

        // 使用超快速点击处理
        handleUltraFastClick(event);
      },
      [onTaskClick, handleUltraFastClick]
    );

    // 优化的样式计算 - 使用 CSS 变量避免重新计算
    const optimizedClassName = useMemo(() => {
      return cn(
        'ultra-fast-task-card',
        // 使用数据属性而不是类名，减少 CSS 重新计算
        className
      );
    }, [className]);

    return (
      <div
        className={optimizedClassName}
        data-selected={isSelected}
        style={{
          // 启用最强的 GPU 加速
          transform: 'translate3d(0, 0, 0)',
          // 最强的渲染优化
          contain: 'layout style paint size',
          // 减少重绘
          isolation: 'isolate',
          // 优化合成
          willChange: 'transform, opacity',
        }}
      >
        <ConfigurableTaskCard
          {...restProps}
          task={task}
          onTaskClick={finalClickHandler}
          className={cn(
            // 超快速响应样式
            'transition-all duration-75 ease-out',
            // 使用数据属性选择器，避免类名切换
            'data-[selected=true]:bg-accent/10',
            'data-[selected=true]:border-accent/40',
            'data-[selected=true]:border-l-accent',
            'data-[selected=true]:border-l-4',
            'data-[selected=true]:shadow-md',
            'data-[selected=true]:translate-y-[-1px]',
            'data-[selected=true]:relative',
            'data-[selected=true]:z-10'
          )}
          data-selected={isSelected}
        />
      </div>
    );
  },
  // 极致优化的比较函数 - 只比较最关键的属性
  (prevProps, nextProps) => {
    return (
      prevProps.task.id === nextProps.task.id &&
      prevProps.task.taskNumber === nextProps.task.taskNumber &&
      prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
      // 简化车辆比较
      prevProps.vehicles.length === nextProps.vehicles.length
    );
  }
);

UltraFastTaskCard.displayName = 'UltraFastTaskCard';

export default UltraFastTaskCard;
