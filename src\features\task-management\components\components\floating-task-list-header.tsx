/**
 * 悬浮任务列表头部组件
 * 可拖拽调整位置的悬浮按钮，避免占用纵向空间
 */

'use client';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/tooltip';
import { cn } from '@/core/lib/utils';
import { getGroupingOptions } from '@/core/utils/task-grouping';
import {
  BarChart3,
  CheckCircle2,
  Columns,
  FileDown,
  FileUp,
  Grid3X3,
  GripVertical,
  List,
  Maximize2,
  Minimize2,
  MoreVertical,
  <PERSON><PERSON>,
  RotateCcw,
  Settings,
  Settings2,
} from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getTaskColumnId } from '../task-list-type-guards';
import { ALL_TASK_COLUMNS_CONFIG } from '../task-list.config';
import { TaskListHeaderProps } from '../task-list-header';
import { TaskListHeaderToggle } from '../task-list-header-toggle';
// 简化的性能监控工具
const createPerformanceMonitor = () => {
  let frameCount = 0;
  let startTime = 0;

  return {
    start: () => {
      frameCount = 0;
      startTime = performance.now();
    },
    frame: () => {
      frameCount++;
    },
    stop: () => {
      const duration = performance.now() - startTime;
      const fps = Math.round((frameCount * 1000) / duration);
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `🎯 Drag Performance: ${fps} FPS (${frameCount} frames in ${duration.toFixed(1)}ms)`
        );
      }
    },
  };
};

interface FloatingPosition {
  x: number;
  y: number;
}

interface FloatingTaskListHeaderProps extends TaskListHeaderProps {
  /** 初始位置 */
  initialPosition?: FloatingPosition;
  /** 位置变化回调 */
  onPositionChange?: (position: FloatingPosition) => void;
  /** 是否显示任务计数 */
  showTaskCount?: boolean;
  /** 切换到固定头部的回调 */
  onSwitchToFixedHeader?: () => void;
}

export function FloatingTaskListHeader({
  displayMode,
  onDisplayModeChange,
  densityMode,
  onDensityModeChange,
  vehicleDisplayMode,
  onVehicleDisplayModeChange,
  groupConfig,
  onToggleGrouping,
  onCancelGrouping,
  onSetGroupBy,
  columnVisibility,
  onColumnVisibilityChange,
  onOpenStyleEditor,
  onOpenCardConfig,
  onExportData,
  onImportData,
  onResetSettings,
  isLoadingPlantInfo,
  taskCount,
  filteredTaskCount,
  initialPosition = { x: 20, y: 100 },
  onPositionChange,
  showTaskCount = true,
  onSwitchToFixedHeader,
}: FloatingTaskListHeaderProps) {
  // 状态管理
  const [position, setPosition] = useState<FloatingPosition>(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // 性能监控
  const performanceMonitor = useRef(createPerformanceMonitor());

  // 拖拽处理 - 性能优化版本
  const dragOffsetRef = useRef({ x: 0, y: 0 });
  const animationFrameRef = useRef<number>();
  const pendingPositionRef = useRef<FloatingPosition | null>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    dragOffsetRef.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
    setIsDragging(true);
    e.preventDefault();

    // 启动性能监控
    performanceMonitor.current.start();

    // 禁用文本选择和用户选择
    document.body.style.userSelect = 'none';
    document.body.style.pointerEvents = 'none';
    if (containerRef.current) {
      containerRef.current.style.pointerEvents = 'auto';
    }
  }, []);

  // 使用 requestAnimationFrame 优化拖拽性能
  const updatePosition = useCallback(() => {
    if (pendingPositionRef.current) {
      // 记录帧性能
      performanceMonitor.current.frame();

      setPosition(pendingPositionRef.current);
      onPositionChange?.(pendingPositionRef.current);
      pendingPositionRef.current = null;
    }
  }, [onPositionChange]);

  // 缓存窗口尺寸以避免重复计算
  const windowSizeRef = useRef({ width: 0, height: 0 });

  useEffect(() => {
    const updateWindowSize = () => {
      windowSizeRef.current = {
        width: window.innerWidth,
        height: window.innerHeight,
      };
    };

    updateWindowSize();
    window.addEventListener('resize', updateWindowSize);
    return () => window.removeEventListener('resize', updateWindowSize);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      // 计算新位置
      const newPosition = {
        x: e.clientX - dragOffsetRef.current.x,
        y: e.clientY - dragOffsetRef.current.y,
      };

      // 边界检查 - 使用缓存的窗口尺寸
      const { width, height } = windowSizeRef.current;
      const maxX = width - 320; // 增加一些缓冲
      const maxY = height - 220;

      newPosition.x = Math.max(10, Math.min(newPosition.x, maxX));
      newPosition.y = Math.max(10, Math.min(newPosition.y, maxY));

      // 存储待更新的位置，避免频繁的状态更新
      pendingPositionRef.current = newPosition;

      // 取消之前的动画帧
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // 使用 requestAnimationFrame 批量更新
      animationFrameRef.current = requestAnimationFrame(updatePosition);
    },
    [isDragging, updatePosition]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);

    // 停止性能监控
    performanceMonitor.current.stop();

    // 恢复文本选择和用户选择
    document.body.style.userSelect = '';
    document.body.style.pointerEvents = '';

    // 确保最后一次位置更新
    if (pendingPositionRef.current) {
      setPosition(pendingPositionRef.current);
      onPositionChange?.(pendingPositionRef.current);
      pendingPositionRef.current = null;
    }

    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = undefined;
    }
  }, [onPositionChange]);

  // 事件监听 - 优化版本
  useEffect(() => {
    if (isDragging) {
      // 使用 passive 选项优化性能
      const options = { passive: false };
      document.addEventListener('mousemove', handleMouseMove, options);
      document.addEventListener('mouseup', handleMouseUp, options);

      // 防止页面滚动
      document.body.style.overflow = 'hidden';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.overflow = '';

        // 清理任何待处理的动画帧
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    }
    return undefined;
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 保存位置到localStorage - 防抖优化
  const savePositionTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // 清除之前的定时器
    if (savePositionTimeoutRef.current) {
      clearTimeout(savePositionTimeoutRef.current);
    }

    // 延迟保存，避免拖拽过程中频繁写入localStorage
    savePositionTimeoutRef.current = setTimeout(
      () => {
        localStorage.setItem('floating-header-position', JSON.stringify(position));
      },
      isDragging ? 1000 : 100
    ); // 拖拽时延迟更长

    return () => {
      if (savePositionTimeoutRef.current) {
        clearTimeout(savePositionTimeoutRef.current);
      }
    };
  }, [position, isDragging]);

  // 从localStorage恢复位置
  useEffect(() => {
    const saved = localStorage.getItem('floating-header-position');
    if (saved) {
      try {
        const savedPosition = JSON.parse(saved);
        setPosition(savedPosition);
      } catch (error) {
        console.warn('Failed to parse saved position:', error);
      }
    }
  }, []);

  return (
    <TooltipProvider>
      <div
        ref={containerRef}
        className={cn(
          'fixed z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/90',
          'border rounded-lg shadow-lg',
          // 性能优化：减少拖拽时的过渡效果
          isDragging
            ? 'shadow-xl scale-105 transition-none'
            : 'shadow-lg transition-all duration-200',
          isExpanded ? 'min-w-[400px]' : 'w-auto',
          // 硬件加速优化
          'transform-gpu backface-hidden'
        )}
        style={{
          transform: `translate3d(${position.x}px, ${position.y}px, 0)`,
          cursor: isDragging ? 'grabbing' : 'default',
          willChange: isDragging ? 'transform' : 'auto',
        }}
      >
        {/* 拖拽手柄和主按钮 */}
        <div className='flex items-center gap-1 p-1'>
          {/* 拖拽手柄 */}
          <div
            ref={dragHandleRef}
            className='flex items-center justify-center w-6 h-8 cursor-grab hover:bg-muted/50 rounded'
            onMouseDown={handleMouseDown}
          >
            <GripVertical className='h-4 w-4 text-muted-foreground' />
          </div>

          {/* 展开/收起按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='h-8 w-8 p-0'
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? <Minimize2 className='h-4 w-4' /> : <Maximize2 className='h-4 w-4' />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{isExpanded ? '收起工具栏' : '展开工具栏'}</TooltipContent>
          </Tooltip>

          {/* 任务计数 */}
          {showTaskCount && (
            <Badge variant='outline' className='text-xs'>
              {isLoadingPlantInfo ? '...' : `${filteredTaskCount}/${taskCount}`}
            </Badge>
          )}

          {/* 快速操作按钮 */}
          {!isExpanded && (
            <>
              {/* 显示模式切换 */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-8 w-8 p-0'
                    onClick={() => onDisplayModeChange(displayMode === 'table' ? 'card' : 'table')}
                  >
                    {displayMode === 'table' ? (
                      <Grid3X3 className='h-4 w-4' />
                    ) : (
                      <List className='h-4 w-4' />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  切换到{displayMode === 'table' ? '卡片' : '表格'}视图
                </TooltipContent>
              </Tooltip>

              {/* 分组切换 - 只在卡片模式下显示 */}
              {displayMode === 'card' && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={groupConfig.enabled ? 'default' : 'ghost'}
                      size='sm'
                      className='h-8 w-8 p-0'
                      onClick={onToggleGrouping}
                    >
                      <CheckCircle2 className='h-4 w-4' />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{groupConfig.enabled ? '取消分组' : '启用分组'}</TooltipContent>
                </Tooltip>
              )}

              {/* 切换到固定头部 */}
              {onSwitchToFixedHeader && (
                <TaskListHeaderToggle
                  useFloatingHeader={true}
                  onToggleHeaderMode={() => onSwitchToFixedHeader()}
                  inFloatingHeader={true}
                />
              )}
            </>
          )}

          {/* 更多操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                <MoreVertical className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-56'>
              <DropdownMenuLabel>任务列表操作</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* 显示模式 */}
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  {displayMode === 'table' ? (
                    <List className='h-4 w-4 mr-2' />
                  ) : (
                    <Grid3X3 className='h-4 w-4 mr-2' />
                  )}
                  显示模式
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    <DropdownMenuCheckboxItem
                      checked={displayMode === 'table'}
                      onCheckedChange={() => onDisplayModeChange('table')}
                    >
                      表格视图
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                      checked={displayMode === 'card'}
                      onCheckedChange={() => onDisplayModeChange('card')}
                    >
                      卡片视图
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>

              {/* 密度模式 */}
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Settings className='h-4 w-4 mr-2' />
                  密度
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    <DropdownMenuCheckboxItem
                      checked={densityMode === 'compact'}
                      onCheckedChange={() => onDensityModeChange('compact')}
                    >
                      紧凑
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                      checked={densityMode === 'normal'}
                      onCheckedChange={() => onDensityModeChange('normal')}
                    >
                      正常
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                      checked={densityMode === 'loose'}
                      onCheckedChange={() => onDensityModeChange('loose')}
                    >
                      宽松
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>

              <DropdownMenuSeparator />

              {/* 分组控制 */}
              <DropdownMenuItem onClick={onToggleGrouping}>
                <CheckCircle2 className='h-4 w-4 mr-2' />
                {groupConfig.enabled ? '取消分组' : '启用分组'}
              </DropdownMenuItem>

              {groupConfig.enabled && onSetGroupBy && (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <BarChart3 className='h-4 w-4 mr-2' />
                    分组字段
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      {getGroupingOptions(groupConfig).map((option: any) => (
                        <DropdownMenuCheckboxItem
                          key={option.value}
                          checked={groupConfig.groupBy === option.value}
                          onCheckedChange={() => onSetGroupBy(option.value)}
                        >
                          <div className='flex items-center gap-2'>
                            <span>{option.icon}</span>
                            <span>{option.label}</span>
                          </div>
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
              )}

              {groupConfig.enabled && (
                <DropdownMenuItem onClick={onCancelGrouping}>
                  <RotateCcw className='h-4 w-4 mr-2' />
                  重置分组
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {/* 列显示控制（表格模式） */}
              {displayMode === 'table' && (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <Columns className='h-4 w-4 mr-2' />
                    列显示
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      {ALL_TASK_COLUMNS_CONFIG.map((column: any) => (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          checked={columnVisibility[getTaskColumnId(column.id)] !== false}
                          onCheckedChange={(checked: boolean) =>
                            onColumnVisibilityChange(getTaskColumnId(column.id), checked)
                          }
                        >
                          {column.label}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
              )}

              {/* 车辆显示模式（卡片模式） */}
              {displayMode === 'card' && (
                <DropdownMenuItem
                  onClick={() =>
                    onVehicleDisplayModeChange(
                      vehicleDisplayMode === 'licensePlate' ? 'internalId' : 'licensePlate'
                    )
                  }
                >
                  <Settings className='h-4 w-4 mr-2' />
                  车辆显示: {vehicleDisplayMode === 'licensePlate' ? '车牌' : 'ID'}
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {/* 样式和配置 */}
              <DropdownMenuItem onClick={onOpenStyleEditor}>
                <Palette className='h-4 w-4 mr-2' />
                样式编辑器
              </DropdownMenuItem>

              {displayMode === 'card' && (
                <DropdownMenuItem onClick={onOpenCardConfig}>
                  <Settings className='h-4 w-4 mr-2' />
                  卡片配置
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {/* 数据管理 */}
              <DropdownMenuItem onClick={onExportData}>
                <FileDown className='h-4 w-4 mr-2' />
                导出数据
              </DropdownMenuItem>

              <DropdownMenuItem onClick={onImportData}>
                <FileUp className='h-4 w-4 mr-2' />
                导入数据
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={onResetSettings}>
                <Settings2 className='h-4 w-4 mr-2' />
                重置设置
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 展开的工具栏 */}
        {isExpanded && (
          <div className='border-t p-2 space-y-2'>
            {/* 第一行：显示模式和密度 */}
            <div className='flex items-center gap-2'>
              <div className='flex items-center gap-1 border rounded p-1'>
                <Button
                  variant={displayMode === 'table' ? 'default' : 'ghost'}
                  size='sm'
                  className='h-7 px-2'
                  onClick={() => onDisplayModeChange('table')}
                >
                  <List className='h-3 w-3 mr-1' />
                  表格
                </Button>
                <Button
                  variant={displayMode === 'card' ? 'default' : 'ghost'}
                  size='sm'
                  className='h-7 px-2'
                  onClick={() => onDisplayModeChange('card')}
                >
                  <Grid3X3 className='h-3 w-3 mr-1' />
                  卡片
                </Button>
              </div>

              <div className='flex items-center gap-1 border rounded p-1'>
                <Button
                  variant={densityMode === 'compact' ? 'default' : 'ghost'}
                  size='sm'
                  className='h-7 px-2 text-xs'
                  onClick={() => onDensityModeChange('compact')}
                >
                  紧凑
                </Button>
                <Button
                  variant={densityMode === 'normal' ? 'default' : 'ghost'}
                  size='sm'
                  className='h-7 px-2 text-xs'
                  onClick={() => onDensityModeChange('normal')}
                >
                  正常
                </Button>
                <Button
                  variant={densityMode === 'loose' ? 'default' : 'ghost'}
                  size='sm'
                  className='h-7 px-2 text-xs'
                  onClick={() => onDensityModeChange('loose')}
                >
                  宽松
                </Button>
              </div>
            </div>

            {/* 第二行：分组控制 - 只在卡片模式下显示 */}
            {displayMode === 'card' && (
              <div className='flex items-center gap-2'>
                <Button
                  variant={groupConfig.enabled ? 'default' : 'outline'}
                  size='sm'
                  className='h-7'
                  onClick={onToggleGrouping}
                >
                  <CheckCircle2 className='h-3 w-3 mr-1' />
                  {groupConfig.enabled ? '已分组' : '分组'}
                </Button>

                {groupConfig.enabled && onSetGroupBy && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant='outline' size='sm' className='h-7'>
                        <BarChart3 className='h-3 w-3 mr-1' />
                        字段
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='start' className='w-56'>
                      <DropdownMenuLabel>选择分组字段</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {getGroupingOptions(groupConfig).map((option: any) => (
                        <DropdownMenuCheckboxItem
                          key={option.value}
                          checked={groupConfig.groupBy === option.value}
                          onCheckedChange={() => onSetGroupBy(option.value)}
                        >
                          <div className='flex items-center gap-2'>
                            <span>{option.icon}</span>
                            <span>{option.label}</span>
                          </div>
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}

                {groupConfig.enabled && (
                  <Button variant='outline' size='sm' className='h-7' onClick={onCancelGrouping}>
                    <RotateCcw className='h-3 w-3 mr-1' />
                    取消分组
                  </Button>
                )}

                {/* 车辆显示模式（卡片模式） */}
                <Button
                  variant='outline'
                  size='sm'
                  className='h-7'
                  onClick={() =>
                    onVehicleDisplayModeChange(
                      vehicleDisplayMode === 'licensePlate' ? 'internalId' : 'licensePlate'
                    )
                  }
                >
                  {vehicleDisplayMode === 'licensePlate' ? '车牌' : 'ID'}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
