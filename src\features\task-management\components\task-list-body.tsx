// src/components/sections/task-list/components/task-list-body.tsx
'use client';

import React from 'react';

import type {
  ColumnDef,
  ColumnOrderState,
  ColumnSizingState,
  OnChangeFn,
  VisibilityState,
} from '@tanstack/react-table';
import { useDrop } from 'react-dnd';

import { ItemTypes } from '@/core/constants/dndItemTypes';
import { TaskRowHighlightProvider } from '@/core/contexts/TaskRowHighlightContext';
import type {
  CustomColumnDefinition,
  Task,
  TaskGroupConfig,
  TaskListDisplayMode,
  TaskListDensityMode,
  VehicleDisplayMode,
  Vehicle,
  TaskCardConfig,
  TaskWithVehicles,
  TaskGroup,
  TaskStatusFilter,
} from '@/core/types';

import { TaskListContentRenderer } from './task-list-content-renderer';

export interface TaskListBodyProps {
  // Display configuration
  displayMode: TaskListDisplayMode;
  densityMode: TaskListDensityMode;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: TaskStatusFilter;

  // Data
  filteredTasks: Task[];
  tasksWithVehicles: TaskWithVehicles[];
  taskGroups: TaskGroup[];
  allVehicles: Vehicle[];

  // Table configuration
  tableColumns: ColumnDef<Task>[];
  settings: {
    columnWidths: ColumnSizingState;
    columnOrder: ColumnOrderState;
    columnVisibility: VisibilityState;
    enableZebraStriping: boolean;
    groupConfig: TaskGroupConfig;
  };
  groupConfig?: TaskGroupConfig; // 独立的分组配置，优先级高于 settings.groupConfig

  // Styling
  densityStyles: any;
  taskCardConfig: TaskCardConfig;
  tableTotalWidth: number;
  estimateRowHeight: (index: number) => number;
  cardGridContainerClasses: string;
  getColumnBackgroundProps: (columnId: string) => any;
  getCellTextClasses?: (columnId: string) => string;
  getStatusLabelProps: (status: string) => any;

  // Drag and drop state
  dragOverTaskId: string | null;
  setDragOverTaskId: (taskId: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (lineId: string | null) => void;

  // Event handlers
  onColumnSizingChange: OnChangeFn<ColumnSizingState>;
  onColumnOrderChange: (
    updater: ColumnOrderState | ((old: ColumnOrderState) => ColumnOrderState)
  ) => void;
  onColumnVisibilityChange: OnChangeFn<VisibilityState>;
  onHeaderContextMenu: (e: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onHeaderDoubleClick: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onRowContextMenu: (e: React.MouseEvent, row: any) => void;
  onRowDoubleClick: (row: any) => void;
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, productionLineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;
  onTaskContextMenu: (e: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick: (task: Task) => void;
  onVehicleDrop: (vehicle: Vehicle, taskId: string) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicle: Vehicle, taskId: string, productionLineId: string) => void;
  onDropVehicleFromPanelOnTaskCard: (vehicle: Vehicle, taskId: string) => void;
  onTaskCardConfigChange: (config: TaskCardConfig) => void;
  onOpenCardConfigModal: () => void;
}

export function TaskListBody({
  displayMode,
  densityMode,
  vehicleDisplayMode,
  taskStatusFilter,
  filteredTasks,
  tasksWithVehicles,
  taskGroups,
  allVehicles,
  tableColumns,
  settings,
  groupConfig,
  densityStyles,
  taskCardConfig,
  tableTotalWidth,
  estimateRowHeight,
  cardGridContainerClasses,
  getColumnBackgroundProps,
  getCellTextClasses,
  getStatusLabelProps,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  onTaskContextMenu,
  onTaskDoubleClick,
  onVehicleDrop,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
  onDropVehicleFromPanelOnTaskCard,
  onTaskCardConfigChange,
  onOpenCardConfigModal,
}: TaskListBodyProps) {
  // Drop zone for vehicles
  const [{ isOver }, drop] = useDrop({
    accept: ItemTypes.VEHICLE,
    drop: () => {
      // Handle drop on empty area
      setDragOverTaskId(null);
      setDragOverProductionLineId(null);
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
    }),
  });

  // Empty state rendering
  if (filteredTasks.length === 0) {
    return (
      <div className='flex items-center justify-center h-full text-muted-foreground p-4'>
        当前状态无任务。
      </div>
    );
  }

  return (
    <div ref={drop as any} className='flex-1 min-h-0'>
      <TaskRowHighlightProvider>
        <TaskListContentRenderer
          displayMode={displayMode}
          filteredTasks={filteredTasks}
          tasksWithVehicles={tasksWithVehicles as Task[]}
          taskGroups={taskGroups}
          allVehicles={allVehicles}
          tableColumns={tableColumns}
          settings={settings as any}
          groupConfig={groupConfig}
          densityStyles={densityStyles}
          vehicleDisplayMode={vehicleDisplayMode}
          taskStatusFilter={taskStatusFilter}
          taskCardConfig={taskCardConfig}
          tableTotalWidth={tableTotalWidth}
          estimateRowHeight={estimateRowHeight as any}
          cardGridContainerClasses={cardGridContainerClasses}
          dragOverTaskId={dragOverTaskId}
          setDragOverTaskId={setDragOverTaskId}
          dragOverProductionLineId={dragOverProductionLineId}
          setDragOverProductionLineId={setDragOverProductionLineId}
          onColumnSizingChange={onColumnSizingChange}
          onColumnOrderChange={onColumnOrderChange}
          onColumnVisibilityChange={onColumnVisibilityChange}
          onHeaderContextMenu={onHeaderContextMenu as any}
          onHeaderDoubleClick={onHeaderDoubleClick}
          onRowContextMenu={onRowContextMenu}
          onRowDoubleClick={onRowDoubleClick}
          onDropOnProductionLine={onDropOnProductionLine}
          onToggleGroupCollapse={onToggleGroupCollapse}
          onCancelGrouping={onCancelGrouping}
          onTaskContextMenu={onTaskContextMenu}
          onTaskDoubleClick={onTaskDoubleClick}
          onVehicleDrop={onVehicleDrop}
          onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
          onOpenDeliveryOrderDetailsForVehicle={onOpenDeliveryOrderDetailsForVehicle}
          onOpenStyleEditor={onOpenStyleEditor}
          onCancelVehicleDispatch={onCancelVehicleDispatch}
          onVehicleDispatchedToLine={onVehicleDispatchedToLine}
          onDropVehicleFromPanelOnTaskCard={onDropVehicleFromPanelOnTaskCard}
          onTaskCardConfigChange={onTaskCardConfigChange}
          onOpenCardConfigModal={onOpenCardConfigModal}
          getColumnBackgroundProps={getColumnBackgroundProps}
          getCellTextClasses={getCellTextClasses}
          getStatusLabelProps={getStatusLabelProps}
        />
      </TaskRowHighlightProvider>
    </div>
  );
}
