// src/components/sections/task-list/components/task-list-card-content.tsx
import React from 'react';

import { cn } from '@/core/lib/utils';
import type {
  Task,
  TaskGroup,
  TaskListStoredSettings,
  Vehicle,
  VehicleDisplayMode,
} from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';

import { EnhancedTaskCardView } from './EnhancedTaskCardView';
import { TaskGroupHeader } from './task-group-header';

// Default card configuration
const defaultCardConfig = {
  size: 'small' as const,
  layout: 'compact' as const,
  theme: 'default' as const,
  spacing: 'normal' as const,
  borderRadius: 'medium' as const,
  shadow: 'small' as const,
  animation: 'subtle' as const,
  columns: 'auto' as const,
};

interface TaskListCardContentProps {
  // Data
  filteredTasks: Task[];
  vehicles: Vehicle[];
  taskGroups: TaskGroup[];

  // Settings
  settings: TaskListStoredSettings;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  taskCardConfig: TaskCardConfig;

  // Drag and Drop State
  dragOverTaskId: string | null;
  setDragOverTaskId: (taskId: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (lineId: string | null) => void;

  // Event Handlers
  handleVehicleDrop: (vehicle: Vehicle, taskId: string) => void;
  handleTaskContextMenu: (event: React.MouseEvent, task: Task) => void;
  handleRowDoubleClick: (task: Task) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onDropVehicleFromPanelOnTaskCard: (vehicle: Vehicle, taskId: string) => void;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;
  onTaskCardConfigChange: (config: TaskCardConfig) => void;
  onOpenCardConfigModal: () => void;

  // Utility Functions
  getStatusLabelProps: (status: string) => { label: string; variant: string };

  className?: string;
}

export function TaskListCardContent({
  filteredTasks,
  vehicles,
  taskGroups,
  settings,
  vehicleDisplayMode,
  taskStatusFilter,
  taskCardConfig,
  dragOverTaskId: _dragOverTaskId,
  setDragOverTaskId: _setDragOverTaskId,
  dragOverProductionLineId: _dragOverProductionLineId,
  setDragOverProductionLineId: _setDragOverProductionLineId,
  handleVehicleDrop: _handleVehicleDrop,
  handleTaskContextMenu,
  handleRowDoubleClick,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine: _onVehicleDispatchedToLine,
  onDropVehicleFromPanelOnTaskCard: _onDropVehicleFromPanelOnTaskCard,
  onDropVehicleOnLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  onTaskCardConfigChange: _onTaskCardConfigChange,
  onOpenCardConfigModal: _onOpenCardConfigModal,
  getStatusLabelProps: _getStatusLabelProps,
  className,
}: TaskListCardContentProps) {
  // 确保数据有效
  const validTasks = Array.isArray(filteredTasks) ? filteredTasks : [];
  const validVehicles = Array.isArray(vehicles) ? vehicles : [];
  const validTaskGroups = Array.isArray(taskGroups) ? taskGroups : [];

  // 如果启用了分组，渲染分组视图
  if (settings.groupConfig?.enabled && settings.groupConfig?.groupBy !== 'none') {
    return (
      <div className={cn('h-full flex flex-col min-h-0', className)}>
        <div className='flex-1 overflow-auto custom-scrollbar min-h-0'>
          <div className='space-y-4 p-4'>
            {validTaskGroups.length > 0 ? (
              validTaskGroups.map(group => (
                <div key={`group-${group.key}-${group.tasks.length}`} className='border rounded-lg'>
                  <TaskGroupHeader
                    group={group}
                    groupConfig={settings.groupConfig}
                    onToggleCollapse={onToggleGroupCollapse}
                    onCancelGrouping={onCancelGrouping}
                  />
                  {!group.collapsed && group.tasks.length > 0 && (
                    <div className='border-t'>
                      <EnhancedTaskCardView
                        filteredTasks={group.tasks}
                        vehicles={validVehicles}
                        settings={settings}
                        productionLineCount={3}
                        vehicleDisplayMode={vehicleDisplayMode}
                        taskStatusFilter={taskStatusFilter}
                        taskCardConfig={taskCardConfig || defaultCardConfig}
                        onTaskContextMenuAction={handleTaskContextMenu}
                        onTaskDoubleClickAction={handleRowDoubleClick}
                        onOpenVehicleCardContextMenuAction={onOpenVehicleCardContextMenu}
                        onOpenDeliveryOrderDetailsForVehicleAction={
                          onOpenDeliveryOrderDetailsForVehicle
                        }
                        onOpenStyleEditorAction={onOpenStyleEditor}
                        onCancelVehicleDispatchAction={onCancelVehicleDispatch}
                        onDropVehicleOnLine={onDropVehicleOnLine}
                      />
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className='text-center py-8 text-gray-500'>
                <p>分组启用但没有找到分组数据</p>
                <p className='text-sm mt-2'>分组字段: {settings.groupConfig?.groupBy}</p>
                <p className='text-sm'>任务数量: {filteredTasks.length}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 未分组的普通卡片视图
  return (
    <div className={cn('h-full', className)}>
      <EnhancedTaskCardView
        filteredTasks={validTasks}
        vehicles={validVehicles}
        settings={settings}
        productionLineCount={3} // This should be passed as a prop
        vehicleDisplayMode={vehicleDisplayMode}
        taskStatusFilter={taskStatusFilter}
        taskCardConfig={taskCardConfig || defaultCardConfig}
        onTaskContextMenuAction={handleTaskContextMenu}
        onTaskDoubleClickAction={handleRowDoubleClick}
        onOpenVehicleCardContextMenuAction={onOpenVehicleCardContextMenu}
        onOpenDeliveryOrderDetailsForVehicleAction={onOpenDeliveryOrderDetailsForVehicle}
        onOpenStyleEditorAction={onOpenStyleEditor}
        onCancelVehicleDispatchAction={onCancelVehicleDispatch}
        onDropVehicleOnLine={onDropVehicleOnLine}
      />
    </div>
  );
}
