// src/components/sections/task-list/hooks/use-task-list-business-logic.ts
import { useCallback } from 'react';

import { shallow } from 'zustand/shallow';

import { useToast } from '@/shared/hooks/use-toast';
import { useCurrentPlantInfo } from '@/features/task-management/hooks/useCurrentPlantInfo';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import type { Task, Vehicle } from '@/core/types';
import { safeJsonParse } from '@/core/utils/common/dataProcessing';

import { useTaskListStyles } from './use-task-list-styles';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

/**
 * 任务列表业务逻辑Hook
 * 负责处理任务列表的核心业务逻辑
 */
export function useTaskListBusinessLogic() {
  const { toast } = useToast();
  const { getStatusLabelProps } = useTaskListStyles();

  // Store actions
  const dispatchVehicleToTask = useAppStore(state => state.dispatchVehicleToTask);
  const cancelVehicleDispatch = useAppStore(state => state.cancelVehicleDispatch);
  const allTasks = useAppStore(state => state.tasks, shallow);
  const allVehicles = useAppStore(state => state.vehicles, shallow);

  // UI state
  const { selectedPlantId } = useUiStore();
  const { plants } = useCurrentPlantInfo();

  /**
   * 处理车辆调度到生产线
   */
  const handleDropOnProductionLine = useCallback(
    async (vehicle: Vehicle, taskId: string, lineId: string) => {
      console.log('🚗 车辆拖拽调度开始:', { vehicle, taskId, lineId });

      const task = allTasks.find(t => t.id === taskId);
      if (!task) {
        console.error('❌ 未找到目标任务:', taskId);
        toast({
          title: '调度失败',
          description: '未找到目标任务。',
          variant: 'destructive',
        });
        return;
      }

      console.log('📋 找到目标任务:', {
        taskNumber: task.taskNumber,
        dispatchStatus: task.dispatchStatus,
        taskStatus: (task as any).taskStatus,
        taskScenario: (task as any).taskScenario,
      });

      // 临时允许所有状态的任务进行调度，用于测试
      // if (task.dispatchStatus !== 'InProgress') {
      //   const statusLabel = getStatusLabelProps(task.dispatchStatus).label;
      //   console.error('❌ 任务状态不允许调度:', { taskNumber: task.taskNumber, status: task.dispatchStatus, statusLabel });
      //   toast({
      //     title: '调度失败',
      //     description: `任务 "${task.taskNumber}" 当前状态为 "${statusLabel}"，无法调度车辆。`,
      //     variant: 'destructive',
      //   });
      //   return;
      // }

      if (!vehicle) {
        console.error('❌ 车辆信息丢失');
        toast({
          title: '调度失败',
          description: '拖拽的车辆信息丢失。',
          variant: 'destructive',
        });
        return;
      }

      console.log('✅ 开始调度车辆:', {
        vehicleId: vehicle.id,
        vehicleNumber: vehicle.vehicleNumber,
      });

      try {
        const updatedVehicleResult = await dispatchVehicleToTask(vehicle.id, taskId, lineId);
        console.log('🔄 调度结果:', updatedVehicleResult);

        if (updatedVehicleResult) {
          const plantName =
            plants.find(p => p.id === selectedPlantId)?.name || selectedPlantId || 'N/A';
          console.log('✅ 车辆调度成功:', {
            vehicleNumber: updatedVehicleResult.vehicleNumber,
            taskNumber: task.taskNumber,
            lineId,
            plantName,
          });
          toast({
            title: '车辆已调度',
            description: `车辆 ${updatedVehicleResult.vehicleNumber} 已成功调度到任务 ${task.taskNumber} (生产线 ${lineId}, 厂区: ${plantName}).`,
          });
        } else {
          console.error('❌ 调度失败，返回结果为空');
          toast({
            title: '调度失败',
            description: `无法调度车辆 ${vehicle.vehicleNumber} 到生产线 ${lineId}。请重试。`,
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('❌ 车辆调度异常:', error);
        toast({
          title: '调度失败',
          description: `调度车辆时发生错误：${error instanceof Error ? error.message : '未知错误'}`,
          variant: 'destructive',
        });
      }
    },
    [allTasks, dispatchVehicleToTask, toast, getStatusLabelProps, plants, selectedPlantId]
  );

  /**
   * 处理车辆调度取消
   */
  const handleCancelVehicleDispatch = useCallback(
    async (vehicleId: string) => {
      try {
        const success = await cancelVehicleDispatch(vehicleId);
        if (success) {
          toast({
            title: '调度已取消',
            description: '车辆调度已成功取消。',
          });
        } else {
          toast({
            title: '取消失败',
            description: '无法取消车辆调度，请重试。',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Cancel vehicle dispatch error:', error);
        toast({
          title: '取消失败',
          description: `取消车辆调度时发生错误：${error instanceof Error ? error.message : '未知错误'}`,
          variant: 'destructive',
        });
      }
    },
    [cancelVehicleDispatch, toast]
  );

  /**
   * 处理从车辆面板拖拽车辆到任务卡片
   */
  const handleDropVehicleFromPanelOnTaskCard = useCallback(
    async (vehicle: Vehicle, taskId: string) => {
      console.log('🚗 车辆从面板拖拽到任务卡片:', { vehicle, taskId });

      const task = allTasks.find(t => t.id === taskId);
      if (!task) {
        console.error('❌ 未找到目标任务:', taskId);
        toast({
          title: '调度失败',
          description: '未找到目标任务。',
          variant: 'destructive',
        });
        return;
      }

      // 检查任务状态是否允许调度
      if (task.dispatchStatus !== 'InProgress') {
        toast({
          title: '调度失败',
          description: `任务 "${task.taskNumber}" 当前状态不允许调度车辆。`,
          variant: 'destructive',
        });
        return;
      }

      try {
        // 默认分配到第一条生产线 (L1)
        const defaultLineId = 'L1';
        const result = await dispatchVehicleToTask(vehicle.id, taskId, defaultLineId);

        if (result) {
          console.log('✅ 车辆调度成功:', result);
          toast({
            title: '调度成功',
            description: `车辆 ${vehicle.vehicleNumber} 已成功调度到任务 ${task.taskNumber} 的生产线 ${defaultLineId}。`,
          });
        } else {
          throw new Error('调度操作返回空结果');
        }
      } catch (error) {
        console.error('❌ 车辆调度失败:', error);
        toast({
          title: '调度失败',
          description: `车辆调度失败：${error instanceof Error ? error.message : '未知错误'}`,
          variant: 'destructive',
        });
      }
    },
    [allTasks, dispatchVehicleToTask, toast]
  );

  /**
   * 验证任务是否可以接受车辆调度
   */
  const canAcceptVehicleDispatch = useCallback((task: Task): boolean => {
    return task.dispatchStatus === 'InProgress';
  }, []);

  /**
   * 验证车辆是否可以被调度
   */
  const canDispatchVehicle = useCallback((vehicle: Vehicle): boolean => {
    // 车辆必须是可用状态且未被分配
    return (
      (!vehicle.assignedTaskId && vehicle.status === 'pending') || vehicle.status === 'returned'
    );
  }, []);

  /**
   * 获取任务的可调度车辆数量
   */
  const getAvailableVehicleCount = useCallback((task: Task): number => {
    // 这里可以根据任务的具体需求计算可调度的车辆数量
    const maxVehicles = task.vehicleCount || 1;
    const currentVehicles = task.vehicles?.length || 0;
    return Math.max(0, maxVehicles - currentVehicles);
  }, []);

  /**
   * 获取任务的完成进度
   */
  const getTaskProgress = useCallback((task: Task): number => {
    if (task.requiredVolume <= 0) return 0;
    return Math.min(100, (task.completedVolume / task.requiredVolume) * 100);
  }, []);

  /**
   * 检查任务是否逾期
   */
  const isTaskOverdue = useCallback((task: Task): boolean => {
    if (!task.supplyDate || !task.supplyTime) return false;

    const supplyDateTime = new Date(`${task.supplyDate} ${task.supplyTime}`);
    const now = new Date();

    return now > supplyDateTime && task.dispatchStatus !== 'Completed';
  }, []);

  /**
   * 获取任务的优先级
   */
  const getTaskPriority = useCallback(
    (task: Task): 'high' | 'medium' | 'low' => {
      if (isTaskOverdue(task)) return 'high';
      if (task.dispatchStatus === 'InProgress') return 'medium';
      return 'low';
    },
    [isTaskOverdue]
  );

  /**
   * 批量操作：取消多个车辆的调度
   */
  const handleBatchCancelDispatch = useCallback(
    async (vehicleIds: string[]) => {
      const results = await Promise.allSettled(vehicleIds.map(id => cancelVehicleDispatch(id)));

      const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast({
          title: '批量操作完成',
          description: `成功取消 ${successful} 个车辆调度${failed > 0 ? `，${failed} 个失败` : ''}。`,
        });
      } else {
        toast({
          title: '批量操作失败',
          description: '所有车辆调度取消操作都失败了。',
          variant: 'destructive',
        });
      }
    },
    [cancelVehicleDispatch, toast]
  );

  /**
   * 导出任务数据
   */
  const handleExportData = useCallback(() => {
    try {
      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        tasks: allTasks,
        vehicles: allVehicles,
        metadata: {
          source: 'TMH Task Dispatcher',
          description: '任务列表数据导出',
          taskCount: allTasks.length,
          vehicleCount: allVehicles.length,
        },
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      // 使用安全的下载函数
      safeDownloadFile(
        jsonString,
        `task-list-data-${new Date().toISOString().split('T')[0]}.json`,
        'application/json'
      );

      toast({
        title: '数据已导出',
        description: `成功导出 ${allTasks.length} 个任务和 ${allVehicles.length} 个车辆的数据`,
      });
    } catch (error) {
      console.error('Failed to export task data:', error);
      toast({
        title: '导出失败',
        description: '任务数据导出失败，请重试',
        variant: 'destructive',
      });
    }
  }, [allTasks, allVehicles, toast]);

  /**
   * 导入任务数据
   */
  const handleImportData = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = event => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const jsonString = e.target?.result as string;
          const importedData = safeJsonParse(jsonString, null);

          if (!importedData) {
            throw new Error('Invalid JSON format');
          }

          // 验证数据结构
          // if (!importedData.tasks || !Array.isArray(importedData.tasks)) {
          //   throw new Error('Invalid data structure: missing tasks array');
          // }

          // if (!importedData.vehicles || !Array.isArray(importedData.vehicles)) {
          //   throw new Error('Invalid data structure: missing vehicles array');
          // }

          // 这里可以添加更多的数据导入逻辑
          // 目前只显示成功消息，实际的数据导入需要通过store方法实现
          // toast({
          //   title: '数据格式验证通过',
          //   description: `检测到 ${importedData.tasks.length} 个任务和 ${importedData.vehicles.length} 个车辆`,
          // });

          // TODO: 实现实际的数据导入逻辑
          console.log('Imported data:', importedData);
        } catch (error) {
          console.error('Failed to import task data:', error);
          toast({
            title: '导入失败',
            description: error instanceof Error ? error.message : '数据导入失败，请检查文件格式',
            variant: 'destructive',
          });
        }
      };
      reader.readAsText(file);
    };
    input.click();
  }, [toast]);

  return {
    // 核心业务方法
    handleDropOnProductionLine,
    handleCancelVehicleDispatch,
    handleDropVehicleFromPanelOnTaskCard,
    handleBatchCancelDispatch,

    // 数据管理方法
    handleExportData,
    handleImportData,

    // 验证方法
    canAcceptVehicleDispatch,
    canDispatchVehicle,

    // 计算方法
    getAvailableVehicleCount,
    getTaskProgress,
    getTaskPriority,

    // 状态检查
    isTaskOverdue,

    // 便捷属性
    hasSelectedPlant: !!selectedPlantId,
    plantName: plants.find(p => p.id === selectedPlantId)?.name || '未选择厂区',
  };
}
