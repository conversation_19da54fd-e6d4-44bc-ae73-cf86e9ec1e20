// src/components/sections/task-list/hooks/use-task-list-columns.ts
import { useCallback, useMemo } from 'react';

import type { CellContext, ColumnDef } from '@tanstack/react-table';

import { allTextColorOptionsMap } from '@/models/column-specific-style-modal';
import { cn } from '@/core/lib/utils';
import { useReminderStore } from '@/features/task-management/store/reminderStore';
import type { DensityStyleValues, StyleableColumnId, Task, Vehicle } from '@/core/types';

import { DispatchedVehiclesCell } from '../components/cells/DispatchedVehiclesCell';
// Cell components
import { DispatchReminderCell } from '../components/cells/DispatchReminderCell';
import { MessageCell } from '../components/cells/MessageCell';
import { ProductionLinesCell } from '../components/cells/ProductionLinesCell';
import { TaskProgressCell } from '../components/cells/TaskProgressCell';
import { getStyleableColumnId, getTaskWithVehicles } from '../components/task-list-type-guards';
import { ALL_TASK_COLUMNS_CONFIG } from '../components/task-list.config';

interface UseTaskListColumnsProps {
  densityStyles: DensityStyleValues;
  vehicleDisplayMode: 'licensePlate' | 'internalId';
  inTaskVehicleCardStyles: any;
  productionLineCount: number;
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  getColumnBackgroundProps: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => { style: React.CSSProperties; className: string };
  settings: any; // 添加settings参数
}

/**
 * 任务列表表格列配置Hook
 * 负责生成和管理表格列的配置
 */
export function useTaskListColumns({
  densityStyles,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  onDropOnProductionLine,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleCardContextMenu,
  getColumnBackgroundProps,
  settings,
}: UseTaskListColumnsProps) {
  const { messages, markAsRead } = useReminderStore();

  // 获取单元格文本样式
  const getCellTextClasses = useCallback(
    (columnId: StyleableColumnId): string => {
      const styles = settings.columnTextStyles[columnId];
      const classes: string[] = [];

      // 添加调试日志
      console.log('📝 getCellTextClasses 调用:', {
        columnId,
        styles,
        allColumnTextStyles: settings.columnTextStyles,
        densityStyles: {
          cellFontSize: densityStyles.cellFontSize,
          cellFontWeight: densityStyles.cellFontWeight,
        },
      });

      if (styles?.color && styles.color !== 'default') {
        const colorOption = allTextColorOptionsMap.get(styles.color);
        const colorClass = colorOption ? colorOption.className : 'text-foreground';
        classes.push(colorClass);
        console.log('📝 颜色样式:', { color: styles.color, colorClass });
      } else {
        classes.push('text-foreground');
        console.log('📝 使用默认颜色: text-foreground');
      }

      if (styles?.textAlign && styles.textAlign !== 'default') {
        classes.push(styles.textAlign);
        console.log('📝 文本对齐:', styles.textAlign);
      } else {
        classes.push('text-left');
        console.log('📝 使用默认对齐: text-left');
      }

      if (styles?.fontSize && styles.fontSize !== 'default') {
        classes.push(styles.fontSize);
        console.log('📝 字体大小:', styles.fontSize);
      } else {
        classes.push(densityStyles.cellFontSize);
        console.log('📝 使用密度字体大小:', densityStyles.cellFontSize);
      }

      if (styles?.fontWeight && styles.fontWeight !== 'default') {
        classes.push(styles.fontWeight);
        console.log('📝 字体粗细:', styles.fontWeight);
      } else {
        classes.push(densityStyles.cellFontWeight);
        console.log('📝 使用密度字体粗细:', densityStyles.cellFontWeight);
      }

      const result = cn(classes);
      console.log('📝 getCellTextClasses 返回结果:', { classes, result });
      return result;
    },
    [settings.columnTextStyles, densityStyles]
  );

  // Memoize column metadata separately to reduce re-renders
  const columnMeta = useMemo(
    () => ({
      getColumnBackgroundProps,
      densityStyles,
    }),
    [getColumnBackgroundProps, densityStyles]
  );

  // Memoize cell renderers that don't depend on dynamic data
  const cellRenderers = useMemo(
    () => ({
      dispatchReminder: (task: Task, columnId: StyleableColumnId) => (
        <DispatchReminderCell task={task} textClassName={getCellTextClasses(columnId)} />
      ),
      messages: (task: Task, columnId: StyleableColumnId) => {
        const taskMessages = messages.filter(msg => msg.taskId === task.id);
        const unreadCount = taskMessages.filter(msg => !msg.read).length;
        return (
          <MessageCell
            task={task}
            taskMessages={taskMessages}
            unreadCount={unreadCount}
            onMarkAsRead={markAsRead}
            textClassName={getCellTextClasses(columnId)}
          />
        );
      },
      completedProgress: (task: Task, columnId: StyleableColumnId) => {
        const progressValue =
          task.requiredVolume > 0 ? (task.completedVolume / task.requiredVolume) * 100 : 0;
        return (
          <TaskProgressCell
            progressValue={progressValue}
            textClassName={getCellTextClasses(columnId)}
          />
        );
      },
      dispatchedVehicles: (task: Task) => (
        <DispatchedVehiclesCell
          task={task}
          taskVehicles={getTaskWithVehicles(task)?.vehicles || []}
          vehicleDisplayMode={vehicleDisplayMode}
          inTaskVehicleCardStyles={inTaskVehicleCardStyles}
          productionLineCount={productionLineCount}
          density={settings.density}
          onCancelDispatch={onCancelVehicleDispatch}
          onOpenStyleEditor={onOpenStyleEditor}
          onOpenDeliveryOrderDetails={(vehicle, currentTask) =>
            onOpenDeliveryOrderDetails(vehicle, currentTask)
          }
          onOpenContextMenu={(e, vehicle, currentTask) =>
            onOpenVehicleCardContextMenu(e, vehicle, currentTask)
          }
        />
      ),
      productionLines: (task: Task) => (
        <ProductionLinesCell
          task={task}
          productionLineCount={productionLineCount}
          densityStyles={densityStyles}
          onDropVehicleOnLine={onDropOnProductionLine}
        />
      ),
      supplyTime: (task: Task, columnId: StyleableColumnId) => {
        const combinedSupplyTime = `${task.supplyDate || ''} ${task.supplyTime || ''}`.trim();
        return (
          <span
            className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}
            title={combinedSupplyTime}
          >
            {combinedSupplyTime}
          </span>
        );
      },
      default: (value: any, columnId: StyleableColumnId) => (
        <span className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}>
          {String(value ?? '')}
        </span>
      ),
    }),
    [
      getCellTextClasses,
      messages,
      markAsRead,
      vehicleDisplayMode,
      inTaskVehicleCardStyles,
      productionLineCount,
      settings.density,
      densityStyles,
      onCancelVehicleDispatch,
      onOpenStyleEditor,
      onOpenDeliveryOrderDetails,
      onOpenVehicleCardContextMenu,
      onDropOnProductionLine,
      settings.columnTextStyles,
      settings.columnBackgrounds,
    ]
  );

  // 生成表格列配置
  const tableColumns = useMemo<ColumnDef<Task>[]>(() => {
    return Array.from(ALL_TASK_COLUMNS_CONFIG).map(colDef => {
      const baseCol: ColumnDef<Task> = {
        id: colDef.id,
        accessorKey: colDef.id,
        header: () => colDef.label,
        size: settings.columnWidths[colDef.id] || colDef.defaultWidth || 150,
        minSize: colDef.id === 'dispatchedVehicles' ? 200 : colDef.minWidth || 50,
        maxSize: colDef.id === 'dispatchedVehicles' ? 600 : colDef.maxWidth,
        enableResizing: colDef.isResizable !== false,
        enableHiding: !colDef.isMandatory,
        meta: { customDef: { ...colDef, ...columnMeta } },
        cell: (info: CellContext<Task, any>) => {
          const task = info.row.original;
          const columnId = getStyleableColumnId(info.column.id);

          switch (columnId) {
            case 'dispatchReminder':
              return cellRenderers.dispatchReminder(task, columnId);
            case 'messages':
              return cellRenderers.messages(task, columnId);
            case 'completedProgress':
              return cellRenderers.completedProgress(task, columnId);
            case 'dispatchedVehicles':
              return cellRenderers.dispatchedVehicles(task);
            case 'productionLines':
              return cellRenderers.productionLines(task);
            case 'supplyTime':
              return cellRenderers.supplyTime(task, columnId);
            default:
              // 直接从任务对象获取值，而不是依赖 info.getValue()
              const value = (task as any)[info.column.id];
              return cellRenderers.default(value, columnId);
          }
        },
      };
      return { ...baseCol }; // 确保返回新对象
    });
  }, [
    settings.columnWidths, // Affects column sizes
    settings.columnTextStyles, // Affects text styling
    settings.columnBackgrounds, // Affects background styling
    columnMeta, // Affects meta data
    cellRenderers, // Affects cell rendering
  ]);

  return {
    tableColumns,
    getCellTextClasses,
    cellRenderers,
  };
}
