// src/components/sections/task-list/hooks/use-task-list-styles.ts
import { useCallback, useMemo } from 'react';

import { allBackgroundColorOptionsMap } from '@/models/column-specific-style-modal';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import type { Task } from '@/core/types';

import { getCardGridClasses, getDensityStyles } from '../components/task-list-density-config';
import { getStyleableColumnId } from '../components/task-list-type-guards';

/**
 * 任务列表设置接口
 */
interface TaskListSettings {
  density: 'compact' | 'normal' | 'loose';
  columnBackgrounds: Record<string, string>;
  columnWidths: Record<string, number>;
  tableStyleConfig: {
    stickyColumnStyle?: {
      backgroundColor?: string;
    };
  };
  statusLabelStyles: Record<
    string,
    {
      label: any;
      backgroundColor?: string;
      textColor?: string;
      className?: string;
    }
  >;
  inTaskVehicleCardStyles: {
    height?: number;
    gap?: number;
    vehiclesPerRow?: number;
  };
}

/**
 * 任务列表样式Hook返回值接口
 */
interface UseTaskListStylesReturn {
  // 密度相关
  currentDensityStyles: ReturnType<typeof getDensityStyles>;
  cardGridContainerClasses: string;
  estimateRowHeight: (task?: Task) => number;

  // 列样式
  getColumnBackgroundProps: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => {
    style: React.CSSProperties;
    className: string;
  };
  tableTotalWidth: number;

  // 状态样式
  getStatusLabelProps: (status: string) => {
    label: any;
    style: React.CSSProperties;
    className: string;
  };

  // 主题样式
  themeStyles: any;
  responsiveStyles: any;

  // 便捷方法
  getDensityClass: (property: string) => any;
  isCompactMode: boolean;
  isLooseMode: boolean;
  isNormalMode: boolean;

  // 车辆卡片样式
  inTaskVehicleCardStyles: {
    height?: number;
    gap?: number;
    vehiclesPerRow?: number;
  };
}

/**
 * 任务列表样式计算Hook
 * 负责计算各种样式相关的值和函数
 */
export function useTaskListStyles(settings?: Partial<TaskListSettings>): UseTaskListStylesReturn {
  const { settings: internalSettings } = useTaskListSettings();
  const { vehicleDisplayMode } = useUiStore();

  // 使用传入的settings或内部获取的settings，确保不为undefined
  const activeSettings = settings || internalSettings || {};

  // 如果activeSettings仍然为空对象，提供默认值
  const safeSettings: TaskListSettings = {
    density: 'normal' as const,
    columnBackgrounds: {},
    columnWidths: {},
    tableStyleConfig: {},
    statusLabelStyles: {},
    inTaskVehicleCardStyles: {},
    ...activeSettings,
  };

  // 当前密度样式
  const currentDensityStyles = useMemo(() => {
    return getDensityStyles(safeSettings.density);
  }, [safeSettings.density]);

  // 卡片网格容器样式
  const cardGridContainerClasses = useMemo(() => {
    return getCardGridClasses(safeSettings.density);
  }, [safeSettings.density]);

  // 行高估算函数
  const estimateRowHeight = useCallback(
    (task?: Task): number => {
      const baseHeight =
        safeSettings.density === 'compact' ? 36 : safeSettings.density === 'loose' ? 46 : 42;
      if (
        !task ||
        vehicleDisplayMode !== 'internalId' ||
        !task.vehicles ||
        task.vehicles.length === 0
      ) {
        return baseHeight;
      }

      // 计算车辆卡片的高度
      const vehicleCardHeight = safeSettings.inTaskVehicleCardStyles?.height || 32;
      const gap = safeSettings.inTaskVehicleCardStyles?.gap || 4;
      const vehiclesPerRow = safeSettings.inTaskVehicleCardStyles?.vehiclesPerRow || 3;
      const numRows = Math.ceil(task.vehicles.length / vehiclesPerRow);
      const vehiclesHeight = numRows * vehicleCardHeight + (numRows - 1) * gap;

      // Add some padding
      return baseHeight + vehiclesHeight + 16;
    },
    [safeSettings.density, vehicleDisplayMode, safeSettings.inTaskVehicleCardStyles]
  );

  // 列背景属性获取函数
  const getColumnBackgroundProps = useCallback(
    (
      columnId: string,
      isHeader: boolean,
      isFixed: boolean
    ): { style: React.CSSProperties; className: string } => {
      const safeColumnId = getStyleableColumnId(columnId);
      const bgSetting = safeSettings.columnBackgrounds[safeColumnId];
      const bgOption = bgSetting ? allBackgroundColorOptionsMap.get(bgSetting) : null;
      let style: React.CSSProperties = {};
      let className = '';

      // 添加调试日志
      console.log('🎨 getColumnBackgroundProps 调用:', {
        columnId,
        safeColumnId,
        isHeader,
        isFixed,
        bgSetting,
        bgOption: bgOption ? {
          label: bgOption.label,
          value: bgOption.value,
          specificSolidColor: bgOption.specificSolidColor,
          themeClassName: bgOption.themeClassName
        } : null,
        allColumnBackgrounds: safeSettings.columnBackgrounds
      });

      if (bgOption) {
        if (isFixed && bgOption.specificSolidColor) {
          style.backgroundColor = bgOption.specificSolidColor;
          console.log('🎨 固定列使用specificSolidColor:', bgOption.specificSolidColor);
        } else if (bgOption.themeClassName) {
          className = bgOption.themeClassName;
          console.log('🎨 使用themeClassName:', bgOption.themeClassName);
        } else if (bgOption.specificSolidColor) {
          style.backgroundColor = bgOption.specificSolidColor;
          console.log('🎨 普通列使用specificSolidColor:', bgOption.specificSolidColor);
        }
      } else if (isFixed) {
        // 为固定列设置默认的不透明背景色
        const stickyStyle = safeSettings.tableStyleConfig?.stickyColumnStyle;
        if (stickyStyle?.backgroundColor) {
          // 如果配置中有背景色，优先使用配置的值
          if (stickyStyle.backgroundColor.startsWith('bg-')) {
            // 如果是Tailwind类名，添加到className
            className = stickyStyle.backgroundColor;
            console.log('🎨 固定列使用配置的Tailwind类名:', stickyStyle.backgroundColor);
          } else {
            // 如果是具体的颜色值，设置到style
            style.backgroundColor = stickyStyle.backgroundColor;
            console.log('🎨 固定列使用配置的颜色值:', stickyStyle.backgroundColor);
          }
        } else {
          // 默认不透明背景色 - 修复透明度问题
          className = 'bg-background';
          style.backgroundColor = 'hsl(var(--background))';
          console.log('🎨 固定列使用默认背景色');
        }
      }

      const result = { style, className };
      console.log('🎨 getColumnBackgroundProps 返回结果:', result);
      return result;
    },
    [safeSettings.columnBackgrounds, safeSettings.tableStyleConfig]
  );

  // 表格总宽度计算
  const tableTotalWidth = useMemo(() => {
    const widths = Object.values(safeSettings.columnWidths) as number[];
    return widths.reduce((sum, width) => sum + width, 0);
  }, [safeSettings.columnWidths]);

  // 状态标签属性获取函数
  const getStatusLabelProps = useCallback(
    (
      status: string
    ): {
      label: string;
      style: React.CSSProperties;
      className: string;
    } => {
      const statusConfig = safeSettings.statusLabelStyles?.[status];
      let style: React.CSSProperties = {};
      let className = '';
      let label = statusConfig?.label;
      if (statusConfig) {
        if (statusConfig.backgroundColor) {
          style.backgroundColor = statusConfig.backgroundColor;
        }
        if (statusConfig.textColor) {
          style.color = statusConfig.textColor;
        }
        if (statusConfig.className) {
          className = statusConfig.className;
        }
      }

      return {
        style,
        className,
        label: label,
      };
    },
    [safeSettings.statusLabelStyles]
  );

  // 主题相关样式
  const themeStyles = useMemo(() => {
    return {
      // 表格样式
      table: {
        headerBg: 'bg-muted/50',
        rowHover: 'hover:bg-muted/50',
        rowSelected: 'bg-accent/50',
        border: 'border-border',
      },
      // 卡片样式
      card: {
        bg: 'bg-card',
        border: 'border-border',
        shadow: 'shadow-sm',
        hover: 'hover:shadow-md',
      },
      // 拖拽样式
      drag: {
        over: 'bg-primary/10 ring-2 ring-primary',
        placeholder: 'bg-muted/30 border-2 border-dashed border-muted-foreground/30',
      },
    };
  }, []);

  // 响应式样式
  const responsiveStyles = useMemo(() => {
    return {
      // 移动端适配
      mobile: {
        padding: 'p-2',
        margin: 'm-1',
        fontSize: 'text-sm',
      },
      // 桌面端
      desktop: {
        padding: 'p-4',
        margin: 'm-2',
        fontSize: 'text-base',
      },
    };
  }, []);

  return {
    // 密度相关
    currentDensityStyles,
    cardGridContainerClasses,
    estimateRowHeight,

    // 列样式
    getColumnBackgroundProps,
    tableTotalWidth,

    // 状态样式
    getStatusLabelProps,

    // 主题样式
    themeStyles,
    responsiveStyles,

    // 便捷方法
    getDensityClass: (property: string) => {
      // 将字符串类型的property转换为DensityStyleValues的key类型
      const key = property as keyof typeof currentDensityStyles;
      return currentDensityStyles[key];
    },
    isCompactMode: safeSettings.density === 'compact',
    isLooseMode: safeSettings.density === 'loose',
    isNormalMode: safeSettings.density === 'normal',

    // 车辆卡片样式
    inTaskVehicleCardStyles: safeSettings.inTaskVehicleCardStyles,
  };
}
