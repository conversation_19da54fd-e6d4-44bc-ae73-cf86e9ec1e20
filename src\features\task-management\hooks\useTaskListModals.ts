/**
 * 任务列表模态框状态管理Hook
 * 将所有模态框相关的状态和逻辑集中管理
 */

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import { useQRCodeModal } from '@/shared/hooks/useQRCodeModal';
import { useTankTruckDispatchModal } from '@/features/task-management/hooks/useTankTruckDispatchModal';
import { useTaskAbbreviationModal } from '@/features/task-management/hooks/useTaskAbbreviationModal';
import { useTaskContextMenu } from '@/features/task-management/hooks/useTaskContextMenu';
import { useVehicleCardContextMenu } from '@/features/vehicle-dispatch/hooks/useVehicleCardContextMenu';
import { EVENTS, globalEventEmitter } from '@/core/utils/eventEmitter';
import type { Task, Vehicle } from '@/core/types';
import type { TankTruckDispatchData } from '@/models/tank-truck-dispatch-modal';

// 发货单详情模态框Hook
function useDeliveryOrderDetailsModal() {
  const [isDeliveryOrderDetailsModalOpen, setIsDeliveryOrderDetailsModalOpen] = useState(false);
  const [selectedVehicleForDeliveryOrder, setSelectedVehicleForDeliveryOrder] =
    useState<Vehicle | null>(null);
  const [selectedTaskForDeliveryOrder, setSelectedTaskForDeliveryOrder] = useState<Task | null>(
    null
  );

  const openDeliveryOrderDetailsModal = useCallback((vehicle: Vehicle, task?: Task) => {
    setSelectedVehicleForDeliveryOrder(vehicle);
    setSelectedTaskForDeliveryOrder(task || null);
    setIsDeliveryOrderDetailsModalOpen(true);
  }, []);

  const closeDeliveryOrderDetailsModal = useCallback(() => {
    setIsDeliveryOrderDetailsModalOpen(false);
    setSelectedVehicleForDeliveryOrder(null);
    setSelectedTaskForDeliveryOrder(null);
  }, []);

  return {
    isDeliveryOrderDetailsModalOpen,
    selectedVehicleForDeliveryOrder,
    selectedTaskForDeliveryOrder,
    openDeliveryOrderDetailsModal,
    closeDeliveryOrderDetailsModal,
  };
}

interface UseTaskListModalsProps {
  onVehicleDispatch: (vehicle: Vehicle, taskId: string, lineId: string) => Promise<void>;
  getTaskById: (taskId: string) => Task | undefined;
  allVehicles: Vehicle[];
}

/**
 * 任务列表模态框状态管理Hook
 */
export function useTaskListModals({
  onVehicleDispatch,
  getTaskById,
  allVehicles,
}: UseTaskListModalsProps) {
  const { toast } = useToast();

  // 车辆调度模态框状态
  const [isVehicleDispatchModalOpen, setIsVehicleDispatchModalOpen] = useState(false);
  const [selectedTaskForVehicleDispatch, setSelectedTaskForVehicleDispatch] = useState<Task | null>(
    null
  );

  // 任务进度模态框状态
  const [isTaskProgressModalOpen, setIsTaskProgressModalOpen] = useState(false);
  const [selectedTaskForProgress, setSelectedTaskForProgress] = useState<Task | null>(null);

  // 配比历史模态框状态
  const [isRatioHistoryModalOpen, setIsRatioHistoryModalOpen] = useState(false);
  const [selectedTaskForRatioHistory, setSelectedTaskForRatioHistory] = useState<Task | null>(null);

  // 使用现有的模态框Hook
  const taskContextMenu = useTaskContextMenu();
  const vehicleCardContextMenu = useVehicleCardContextMenu();
  const qrCodeModal = useQRCodeModal();
  const taskAbbreviationModal = useTaskAbbreviationModal();
  const deliveryOrderDetailsModal = useDeliveryOrderDetailsModal();

  // 罐车发车单模态框
  const tankTruckDispatchModal = useTankTruckDispatchModal({
    onDispatchConfirm: async (
      vehicle: Vehicle,
      task: Task,
      lineId: string,
      dispatchData: TankTruckDispatchData
    ) => {
      await onVehicleDispatch(vehicle, task.id, lineId);
      toast({
        title: '发车成功',
        description: `车辆 ${vehicle.vehicleNumber} 已成功发车到 ${task.taskNumber}`,
      });
    },
  });

  // 车辆调度模态框操作
  const openVehicleDispatchModal = useCallback((task: Task) => {
    setSelectedTaskForVehicleDispatch(task);
    setIsVehicleDispatchModalOpen(true);
  }, []);

  const closeVehicleDispatchModal = useCallback(() => {
    setIsVehicleDispatchModalOpen(false);
    setSelectedTaskForVehicleDispatch(null);
  }, []);

  // 任务进度模态框操作
  const openTaskProgressModal = useCallback((task: Task) => {
    setSelectedTaskForProgress(task);
    setIsTaskProgressModalOpen(true);
  }, []);

  const closeTaskProgressModal = useCallback(() => {
    setIsTaskProgressModalOpen(false);
    setSelectedTaskForProgress(null);
  }, []);

  // 配比历史模态框操作
  const openRatioHistoryModal = useCallback((task: Task) => {
    setSelectedTaskForRatioHistory(task);
    setIsRatioHistoryModalOpen(true);
  }, []);

  const closeRatioHistoryModal = useCallback(() => {
    setIsRatioHistoryModalOpen(false);
    setSelectedTaskForRatioHistory(null);
  }, []);

  // 处理发送生产指令
  const handleSendProductionInstruction = useCallback(
    (taskId: string) => {
      const task = getTaskById(taskId);
      if (!task) {
        toast({
          title: '错误',
          description: '找不到指定的任务',
          variant: 'destructive',
        });
        return;
      }

      // 获取可用的车辆（状态为pending的车辆）
      const availableVehicles = allVehicles.filter((v: Vehicle) => v.status === 'pending');

      if (availableVehicles.length === 0) {
        console.warn('⚠️ No available vehicles found');
        toast({
          title: '无可用车辆',
          description: '当前没有可用的车辆进行调度',
          variant: 'destructive',
        });
        return;
      }

      // 选择第一个可用车辆，默认生产线为L1
      const selectedVehicle = availableVehicles[0];
      if (!selectedVehicle) {
        console.error('❌ Selected vehicle is null');
        toast({
          title: '无可用车辆',
          description: '当前没有可用的车辆进行调度',
          variant: 'destructive',
        });
        return;
      }

      const defaultLineId = 'L1';
      console.log('🎯 Opening tank truck dispatch modal:', {
        task: task.taskNumber,
        vehicle: selectedVehicle.vehicleNumber,
        lineId: defaultLineId,
      });

      // 打开罐车发车单模态框
      tankTruckDispatchModal.openModal(task, selectedVehicle, defaultLineId);
      console.log('✅ Tank truck dispatch modal opened');
    },
    [getTaskById, allVehicles, tankTruckDispatchModal, toast]
  );

  // 处理拖拽到生产线（带模态框）
  const handleDropOnProductionLineWithModal = useCallback(
    (vehicle: Vehicle, taskId: string, lineId: string) => {
      const task = getTaskById(taskId);
      if (!task) {
        toast({
          title: '错误',
          description: '找不到指定的任务',
          variant: 'destructive',
        });
        return;
      }

      // 打开罐车发车单模态框
      tankTruckDispatchModal.openModal(task, vehicle, lineId);
    },
    [getTaskById, tankTruckDispatchModal, toast]
  );

  // 监听全局事件
  useEffect(() => {
    console.log('🎧 Setting up global event listeners for tank truck dispatch modal');

    const handleSendProductionInstructionEvent = (taskId: string) => {
      console.log('📨 Received SEND_PRODUCTION_INSTRUCTION event:', taskId);
      handleSendProductionInstruction(taskId);
    };

    const handleOpenVehicleDispatchModalEvent = (task: Task) => {
      openVehicleDispatchModal(task);
    };

    const handleOpenTaskProgressModalEvent = (task: Task) => {
      openTaskProgressModal(task);
    };

    globalEventEmitter.on(EVENTS.SEND_PRODUCTION_INSTRUCTION, handleSendProductionInstructionEvent);
    globalEventEmitter.on(EVENTS.OPEN_VEHICLE_DISPATCH_MODAL, handleOpenVehicleDispatchModalEvent);
    globalEventEmitter.on(EVENTS.OPEN_TASK_PROGRESS_MODAL, handleOpenTaskProgressModalEvent);

    return () => {
      globalEventEmitter.off(
        EVENTS.SEND_PRODUCTION_INSTRUCTION,
        handleSendProductionInstructionEvent
      );
      globalEventEmitter.off(
        EVENTS.OPEN_VEHICLE_DISPATCH_MODAL,
        handleOpenVehicleDispatchModalEvent
      );
      globalEventEmitter.off(EVENTS.OPEN_TASK_PROGRESS_MODAL, handleOpenTaskProgressModalEvent);
    };
  }, [handleSendProductionInstruction, openVehicleDispatchModal, openTaskProgressModal]);

  return {
    // 车辆调度模态框
    isVehicleDispatchModalOpen,
    selectedTaskForVehicleDispatch,
    openVehicleDispatchModal,
    closeVehicleDispatchModal,

    // 任务进度模态框
    isTaskProgressModalOpen,
    selectedTaskForProgress,
    openTaskProgressModal,
    closeTaskProgressModal,

    // 配比历史模态框
    isRatioHistoryModalOpen,
    selectedTaskForRatioHistory,
    openRatioHistoryModal,
    closeRatioHistoryModal,

    // 罐车发车单模态框
    tankTruckDispatchModal,

    // 其他模态框
    taskContextMenu,
    vehicleCardContextMenu,
    qrCodeModal,
    taskAbbreviationModal,
    deliveryOrderDetailsModal,

    // 事件处理器
    handleSendProductionInstruction,
    handleDropOnProductionLineWithModal,
  };
}
