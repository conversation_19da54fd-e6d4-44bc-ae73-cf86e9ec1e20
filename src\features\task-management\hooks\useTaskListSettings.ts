// src/hooks/useTaskListSettings.ts
'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { produce } from 'immer';
import { flushSync } from 'react-dom';

import { getDensityNumericConfig } from '@/features/task-management/components/task-list-density-config';
import { ALL_TASK_COLUMNS_CONFIG } from '@/features/task-management/components/task-list.config';
import { useToast } from '@/shared/hooks/use-toast';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

import type {
  ColumnTextStyle,
  CustomColumnDefinition,
  InTaskVehicleCardStyle,
  StyleableColumnId,
  TaskGroupConfig,
  TaskListDensityMode,
  TaskListDisplayMode,
  TaskListStoredSettings,
  VehicleDisplayMode,
} from '@/core/types';

// 防抖工具函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface UseTaskListSettingsReturn {
  settings: TaskListStoredSettings;
  isSettingsLoaded: boolean;
  updateSetting: (key: keyof TaskListStoredSettings, value: any) => void;
  // 便捷更新方法
  updateDisplayMode: (mode: TaskListDisplayMode) => void;
  updateDensity: (density: Exclude<TaskListDensityMode, '' | 'card'>) => void;
  updateVehicleDisplayMode: (mode: VehicleDisplayMode) => void;
  updateColumnVisibility: (columnId: string, visible: boolean) => void;
  updateStickyColumnStyle: (
    stickyStyle: Partial<
      NonNullable<TaskListStoredSettings['tableStyleConfig']>['stickyColumnStyle']
    >
  ) => void;
  toggleGrouping: () => void;
  cancelGrouping: () => void;
  resetSettings: () => void;
  // 原有方法
  handleColumnVisibilityChange: (columnId: string, visible: boolean) => void;
  handleColumnOrderChange: (newOrder: string[]) => void;
  handleSingleColumnWidthChange: (columnId: string, width: number) => void;
  handleColumnTextStyleChange: (
    columnId: StyleableColumnId,
    styleProperty: keyof ColumnTextStyle,
    valueKey: string
  ) => void;
  handleColumnBackgroundChange: (columnId: StyleableColumnId, backgroundColor: string) => void;
  handleVehiclesPerRowChange: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;
  handleCustomDispatchVehiclesColumnWidth: (width: number) => void;
  resetAllSettings: () => void;
  exportSettings: () => void;
  handleImportFile: (event: React.ChangeEvent<HTMLInputElement>) => void;
  triggerImport: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  isColumnVisibilityModalOpen: boolean;
  openColumnVisibilityModal: () => void;
  closeColumnVisibilityModal: () => void;
  isColumnSpecificStyleModalOpen: boolean;
  editingColumnDef: CustomColumnDefinition | null;
  openColumnSpecificStyleModal: (columnDef: CustomColumnDefinition) => void;
  closeColumnSpecificStyleModal: () => void;
  isStyleEditorModalOpen: boolean;
  openStyleEditorModal: () => void;
  closeStyleEditorModal: () => void;
  // 分组确认弹框
  isGroupByColumnConfirmOpen: boolean;
  groupingColumnDef: CustomColumnDefinition | null;
  openGroupByColumnConfirm: (columnDef: CustomColumnDefinition) => void;
  closeGroupByColumnConfirm: () => void;
  confirmGroupByColumn: () => void;
  // 列选择分组模态框
  isGroupByColumnSelectOpen: boolean;
  openGroupByColumnSelect: () => void;
  closeGroupByColumnSelect: () => void;
  confirmGroupByColumnSelect: (columnId: string) => void;
  updateGroupConfig: (newGroupConfig: Partial<TaskGroupConfig>) => void;
  handleToggleGrouping: () => void;
  handleSetGroupBy: (groupBy: TaskGroupConfig['groupBy']) => void;
  handleToggleGroupCollapse: (groupId: string) => void;
  isGroupConfigModalOpen: boolean;
  setIsGroupConfigModalOpen: (open: boolean) => void;
}

const TASK_LIST_SETTINGS_KEY = 'taskListSettings_v3.3'; // Incremented version

// Initial column order derived from ALL_TASK_COLUMNS_CONFIG sorted by 'order'
const initialColumnOrderForInit = ALL_TASK_COLUMNS_CONFIG.slice() // Create a copy before sorting
  .sort((a, b) => (a.order || 999) - (b.order || 999))
  .map(c => c.id as string);

const initialColumnVisibilityForInit = ALL_TASK_COLUMNS_CONFIG.reduce(
  (acc, colDef) => {
    const colId = String(colDef.id);
    acc[colId] = colDef.isMandatory || colDef.defaultVisible || false;
    if (colId === 'completedProgress' && (colDef.defaultVisible || colDef.isMandatory)) {
      acc['requiredVolume'] = false;
      acc['completedVolume'] = false;
    }
    return acc;
  },
  {} as Record<string, boolean>
);

const initialColumnWidthsForInit = ALL_TASK_COLUMNS_CONFIG.reduce(
  (acc, colDef) => {
    if (colDef.defaultWidth) {
      acc[colDef.id as string] = colDef.defaultWidth;
    }
    return acc;
  },
  {} as Record<string, number>
);

const initialInTaskVehicleCardStyles: InTaskVehicleCardStyle = {
  cardWidth: 'w-14',
  cardHeight: 'h-8',
  fontSize: 'text-[12px]',
  fontColor: 'text-foreground',
  vehicleNumberFontWeight: 'font-medium',
  cardBgColor: 'bg-card/80', // Default card background
  cardGradient: undefined,
  gradientEnabled: false,
  gradientDirection: 'to-r',
  gradientStartColor: '#3b82f6',
  gradientEndColor: '#8b5cf6',
  statusDotSize: 'w-1 h-1',
  borderRadius: 'rounded-md',
  boxShadow: 'shadow-sm',
  vehiclesPerRow: 4,
  gap: 0,
  cardSize: 'small',
};

/**
 * 默认任务分组配置
 *
 * 提供美观且实用的分组功能默认设置，包含分组方式、排序选项、
 * 样式配置等完整的分组功能配置。
 *
 * @constant
 * @type {TaskGroupConfig}
 * @since 2.1.0
 *
 * @see {@link TaskGroupConfig} 分组配置接口定义
 */
const initialTaskGroupConfig: TaskGroupConfig = {
  groupBy: 'none',
  enabled: false,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: [
    'projectName',
    'strength',
    'pouringMethod',
    'supplyDate',
    'pumpTruck',
    'constructionUnit',
    'constructionSite',
  ],
  disallowedGroupColumns: [
    'taskNumber',
    'vehicleCount',
    'completedVolume',
    'requiredVolume',
    'contactPhone',
    'supplyTime',
    'publishDate',
    'dispatchedVehicles',
  ],
  groupHeaderStyle: {
    backgroundColor: 'bg-muted/50',
    textColor: 'text-foreground',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

const initialTaskListSettings: TaskListStoredSettings = {
  displayMode: 'table',
  density: 'compact', // Changed to 'compact' as requested
  enableZebraStriping: false,
  columnOrder: initialColumnOrderForInit,
  columnVisibility: initialColumnVisibilityForInit,
  columnWidths: initialColumnWidthsForInit,
  columnTextStyles: {},
  columnBackgrounds: {},
  inTaskVehicleCardStyles: initialInTaskVehicleCardStyles,
  selectedPlantId: null,
  vehicleDisplayMode: 'licensePlate',
  groupConfig: initialTaskGroupConfig,
  // 添加表格样式配置，包含固定列样式
  tableStyleConfig: {
    headerStyle: {
      backgroundColor: 'bg-slate-100',
      textColor: 'text-slate-700',
      fontSize: 'text-sm',
      fontWeight: 'font-semibold',
      borderColor: 'border-slate-200',
      padding: 'px-3 py-2',
    },
    rowStyle: {
      evenRowBg: 'bg-white',
      oddRowBg: 'bg-slate-50',
      hoverBg: 'hover:bg-blue-50',
      borderColor: 'border-slate-200',
      textColor: 'text-slate-700',
      fontSize: 'text-sm',
      padding: 'px-3 py-2',
    },
    stickyColumnStyle: {
      backgroundColor: 'bg-white', // 默认不透明背景
      borderColor: 'border-slate-200',
      shadowRight: 'shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]',
      zIndex: 'z-10',
    },
  },
};

/**
 * 任务列表设置管理Hook
 *
 * 提供任务列表的完整配置管理功能，包括显示模式、样式配置、分组设置、
 * 列配置等。配置数据会自动持久化到localStorage，并支持导入导出功能。
 *
 * 主要功能：
 * - 配置的加载和保存
 * - 样式设置的实时更新
 * - 分组配置管理
 * - 列可见性和样式配置
 * - 配置的导入导出
 * - 模态框状态管理
 *
 * @hook
 * @since 2.0.0
 *
 * @returns {UseTaskListSettingsReturn} 设置管理对象，包含配置数据和操作方法
 *
 * @example
 * ```typescript
 * const {
 *   settings,
 *   updateSettings,
 *   exportSettings,
 *   importSettings,
 *   resetToDefaults
 * } = useTaskListSettings();
 *
 * // 更新显示模式
 * updateSettings({ displayMode: 'card' });
 *
 * // 更新样式配置
 * updateSettings({
 *   columnTextStyles: {
 *     taskNumber: { fontSize: 'text-lg', fontWeight: 'font-bold' }
 *   }
 * });
 *
 * // 导出配置
 * const config = exportSettings();
 *
 * // 重置为默认配置
 * resetToDefaults();
 * ```
 *
 * @note 配置变更会自动保存到localStorage，键名为'taskListSettings_v3.3'
 * @warning 不要直接修改settings对象，请使用updateSettings方法
 *
 * @see {@link TaskListSettings} 配置接口定义
 * @see {@link UseTaskListSettingsReturn} 返回值接口定义
 */
export function useTaskListSettings(): UseTaskListSettingsReturn {
  const [settings, setSettingsState] = useState<TaskListStoredSettings>(initialTaskListSettings);
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false);
  const [, forceUpdate] = useState({});
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isInitialLoadRef = useRef(true);
  const initialSettingsAppliedRef = useRef(false);

  const [isColumnVisibilityModalOpen, setIsColumnVisibilityModalOpen] = useState(false);
  const [isColumnSpecificStyleModalOpen, setIsColumnSpecificStyleModalOpen] = useState(false);
  const [editingColumnDef, setEditingColumnDef] = useState<CustomColumnDefinition | null>(null);
  const [isStyleEditorModalOpen, setIsStyleEditorModalOpen] = useState(false);
  const [isGroupConfigModalOpen, setIsGroupConfigModalOpen] = useState(false);

  // 分组确认弹框状态
  const [isGroupByColumnConfirmOpen, setGroupByColumnConfirmOpen] = useState(false);
  const [groupingColumnDef, setGroupingColumnDef] = useState<CustomColumnDefinition | null>(null);

  // 列选择分组模态框状态
  const [isGroupByColumnSelectOpen, setGroupByColumnSelectOpen] = useState(false);

  // 跟踪是否是手动更新状态
  const isManualUpdateRef = useRef(false);
  const hasManualGroupConfigRef = useRef(false);

  // Use a ref to track the previous settings to avoid unnecessary saves
  const prevSettingsRef = useRef<string>('');

  // Memoize the loaded settings to prevent unnecessary re-creation
  const [rawLoadedSettings, setRawLoadedSettings] = useState<string | null>(null);

  const loadedSettings = useMemo(() => {
    if (rawLoadedSettings === null) {
      console.log('🔍 使用初始设置 (rawLoadedSettings为null)');
      return initialTaskListSettings;
    }
    try {
      const parsed = JSON.parse(rawLoadedSettings);
      console.log('🔍 加载的设置详情:', {
        size: rawLoadedSettings.length,
        columnBackgrounds: parsed.columnBackgrounds,
        columnBackgroundsCount: Object.keys(parsed.columnBackgrounds || {}).length,
        tableStyleConfig: parsed.tableStyleConfig,
        stickyColumnBg: parsed.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
        displayMode: parsed.displayMode,
        density: parsed.density,
      });
      return parsed;
    } catch (error) {
      console.error('🔍 解析设置失败:', error);
      return initialTaskListSettings;
    }
  }, [rawLoadedSettings]);

  // Memoize the reconciled settings to prevent infinite loops
  const reconciledSettings = useMemo((): TaskListStoredSettings => {
    console.log('🔍 开始配置合并:', {
      loadedColumnBackgrounds: Object.keys(loadedSettings.columnBackgrounds || {}).length,
      loadedTableStyleConfig: !!loadedSettings.tableStyleConfig,
      loadedStickyColumnBg: loadedSettings.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
    });

    const baseSettings = {
      ...loadedSettings,
      // 保留加载的样式配置，不要被初始值覆盖
      columnTextStyles: loadedSettings.columnTextStyles || {},
      columnBackgrounds: loadedSettings.columnBackgrounds || {},
      inTaskVehicleCardStyles: {
        ...initialInTaskVehicleCardStyles,
        ...(loadedSettings.inTaskVehicleCardStyles || {}),
      },
      groupConfig: {
        ...initialTaskGroupConfig,
        ...(loadedSettings.groupConfig || {}),
        groupHeaderStyle: {
          ...initialTaskGroupConfig.groupHeaderStyle,
          ...(loadedSettings.groupConfig?.groupHeaderStyle || {}),
        },
      },
      // 确保表格样式配置正确合并，特别是固定列样式
      tableStyleConfig: {
        ...initialTaskListSettings.tableStyleConfig,
        ...(loadedSettings.tableStyleConfig || {}),
        headerStyle: {
          ...initialTaskListSettings.tableStyleConfig?.headerStyle,
          ...(loadedSettings.tableStyleConfig?.headerStyle || {}),
        },
        rowStyle: {
          ...initialTaskListSettings.tableStyleConfig?.rowStyle,
          ...(loadedSettings.tableStyleConfig?.rowStyle || {}),
        },
        stickyColumnStyle: {
          ...initialTaskListSettings.tableStyleConfig?.stickyColumnStyle,
          ...(loadedSettings.tableStyleConfig?.stickyColumnStyle || {}),
        },
      },
    };

    console.log('🔍 配置合并完成:', {
      finalColumnBackgrounds: Object.keys(baseSettings.columnBackgrounds || {}).length,
      finalTableStyleConfig: !!baseSettings.tableStyleConfig,
      finalStickyColumnBg: baseSettings.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
    });

    // Column Order Reconciliation
    const allColumnIdsFromConfigSorted = ALL_TASK_COLUMNS_CONFIG.slice()
      .sort((a, b) => (a.order || 999) - (b.order || 999))
      .map(c => c.id as string);

    if (loadedSettings.columnOrder && Array.isArray(loadedSettings.columnOrder)) {
      const currentConfigIdsSet = new Set(allColumnIdsFromConfigSorted);
      const validStoredOrder = loadedSettings.columnOrder.filter((id: string) =>
        currentConfigIdsSet.has(id)
      );
      const allCurrentColsInStoredOrder =
        validStoredOrder.length === allColumnIdsFromConfigSorted.length &&
        allColumnIdsFromConfigSorted.every(id => validStoredOrder.includes(id));

      if (allCurrentColsInStoredOrder) {
        baseSettings.columnOrder = validStoredOrder;
      } else {
        baseSettings.columnOrder = allColumnIdsFromConfigSorted;
      }
    } else {
      baseSettings.columnOrder = allColumnIdsFromConfigSorted;
    }

    // Column Visibility Reconciliation
    const newVisibility: Record<string, boolean> = {};
    ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
      if (colDef.isMandatory) {
        newVisibility[colDef.id as string] = true;
      } else if (
        loadedSettings.columnVisibility &&
        loadedSettings.columnVisibility.hasOwnProperty(colDef.id as string)
      ) {
        newVisibility[colDef.id as string] =
          loadedSettings.columnVisibility[colDef.id as string] || false;
      } else {
        newVisibility[colDef.id as string] = colDef.defaultVisible || false;
      }
    });

    // Handle mutual exclusivity between completedProgress and requiredVolume/completedVolume
    if (newVisibility['completedProgress']) {
      newVisibility['requiredVolume'] = false;
      newVisibility['completedVolume'] = false;
    } else if (
      newVisibility['requiredVolume'] !== false ||
      newVisibility['completedVolume'] !== false
    ) {
      newVisibility['completedProgress'] = false;
    }
    baseSettings.columnVisibility = newVisibility;

    // Column Widths Reconciliation
    const newWidths: Record<string, number> = {};
    ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
      newWidths[colDef.id as string] =
        (loadedSettings.columnWidths && loadedSettings.columnWidths[colDef.id as string]) ||
        colDef.defaultWidth ||
        100;
    });
    baseSettings.columnWidths = newWidths;

    return baseSettings;
  }, [loadedSettings]);

  useEffect(() => {
    const loadSettings = () => {
      try {
        // 直接从localStorage加载
        let stored = localStorage.getItem(TASK_LIST_SETTINGS_KEY);

        // 如果localStorage中没有，尝试从sessionStorage备份加载
        if (!stored) {
          stored = sessionStorage.getItem(TASK_LIST_SETTINGS_KEY + '_backup');
          if (stored) {
            console.log('📝 从sessionStorage备份恢复设置');
            // 恢复到localStorage
            localStorage.setItem(TASK_LIST_SETTINGS_KEY, stored);
          }
        }

        if (stored) {
          console.log('📝 成功加载设置:', {
            size: stored.length,
            source: localStorage.getItem(TASK_LIST_SETTINGS_KEY)
              ? 'localStorage'
              : 'sessionStorage',
          });
        } else {
          console.log('📝 未找到保存的设置，将使用默认设置');
        }

        setRawLoadedSettings(stored);
      } catch (error) {
        console.error('❌ 加载设置失败:', error);
        // 不要直接设置默认配置，而是设置为null让后续逻辑处理
        setRawLoadedSettings(null);
      }
      setIsSettingsLoaded(true);
    };

    loadSettings();

    // 监听storage事件，实现跨标签页同步
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === TASK_LIST_SETTINGS_KEY && e.newValue) {
        try {
          console.log('📝 检测到其他标签页的设置变更，同步更新');
          setRawLoadedSettings(e.newValue);
        } catch (error) {
          console.error('同步设置失败:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [toast]); // Removed isSettingsLoaded from dependency array to avoid infinite loop

  // Update settings state when reconciledSettings changes
  useEffect(() => {
    if (!isSettingsLoaded) {
      console.log('🔍 设置未加载，跳过状态更新');
      return;
    }

    // 计算新设置的字符串表示
    const newSettingsString = JSON.stringify(reconciledSettings);

    console.log('🔍 reconciledSettings 变化检查:', {
      isSettingsLoaded,
      isInitialLoad: isInitialLoadRef.current,
      hasManualGroupConfig: hasManualGroupConfigRef.current,
      initialSettingsApplied: initialSettingsAppliedRef.current,
      newSettingsLength: newSettingsString.length,
      prevSettingsLength: prevSettingsRef.current?.length || 0,
      settingsEqual: newSettingsString === prevSettingsRef.current,
      columnBackgrounds: Object.keys(reconciledSettings.columnBackgrounds || {}).length,
      hasTableStyleConfig: !!reconciledSettings.tableStyleConfig,
    });

    // 如果设置没有实际变化，跳过更新
    if (newSettingsString === prevSettingsRef.current) {
      console.log('🔍 设置未变化，跳过状态更新');
      return;
    }

    // 简化逻辑：只要设置已加载且设置未应用，就应用设置
    if (!initialSettingsAppliedRef.current) {
      console.log('🔍 初始加载应用设置');
      setSettingsState(reconciledSettings);
      prevSettingsRef.current = newSettingsString;
      initialSettingsAppliedRef.current = true;
      isInitialLoadRef.current = false;
    } else if (!hasManualGroupConfigRef.current && !isInitialLoadRef.current) {
      console.log('🔍 reconciledSettings 触发状态更新');
      setSettingsState(reconciledSettings);
      prevSettingsRef.current = newSettingsString;
    }
  }, [reconciledSettings, isSettingsLoaded]);

  // 防抖保存设置
  const debouncedSaveSettings = useCallback(
    debounce(async (settingsToSave: TaskListStoredSettings) => {
      const settingsString = JSON.stringify(settingsToSave);
      console.log('🔍 保存设置到 localStorage (防抖版)');
      console.log('🔍 保存的设置内容:', {
        columnBackgrounds: settingsToSave.columnBackgrounds,
        columnBackgroundsCount: Object.keys(settingsToSave.columnBackgrounds || {}).length,
        tableStyleConfig: settingsToSave.tableStyleConfig,
        stickyColumnBg: settingsToSave.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
        stackTrace: new Error().stack?.split('\n').slice(1, 5).join('\n'), // 添加调用栈追踪
      });

      // 确保在客户端环境下保存
      if (typeof window === 'undefined') {
        console.log('📝 服务器端跳过保存设置');
        return;
      }

      // 检查是否是默认配置
      const isDefaultConfig =
        JSON.stringify(settingsToSave) === JSON.stringify(initialTaskListSettings);
      if (isDefaultConfig) {
        console.warn('⚠️ 尝试保存默认配置，这可能会覆盖用户设置！');
        console.warn('⚠️ 调用栈:', new Error().stack);
      }

      try {
        // 获取旧值用于storage事件
        const oldValue = localStorage.getItem(TASK_LIST_SETTINGS_KEY);

        // 直接保存到localStorage
        localStorage.setItem(TASK_LIST_SETTINGS_KEY, settingsString);

        // 保存到sessionStorage作为备份
        sessionStorage.setItem(TASK_LIST_SETTINGS_KEY + '_backup', settingsString);

        // 强制触发storage事件，确保跨标签页同步
        window.dispatchEvent(
          new StorageEvent('storage', {
            key: TASK_LIST_SETTINGS_KEY,
            newValue: settingsString,
            oldValue: oldValue,
            storageArea: localStorage,
          })
        );

        // 更新引用
        prevSettingsRef.current = settingsString;

        console.log('✅ 设置保存成功 (直接方式):', {
          size: settingsString.length,
          columnBackgrounds: Object.keys(settingsToSave.columnBackgrounds || {}).length,
          hasTableStyleConfig: !!settingsToSave.tableStyleConfig,
          isDefaultConfig,
        });
      } catch (error) {
        console.error('❌ 设置保存失败:', error);
      }
    }, 300),
    []
  );

  useEffect(() => {
    // Don't save during initial load or manual group config updates to avoid infinite loop
    if (!isSettingsLoaded || isInitialLoadRef.current || hasManualGroupConfigRef.current) {
      console.log('🔍 跳过保存:', {
        isSettingsLoaded,
        isInitialLoad: isInitialLoadRef.current,
        hasManualGroupConfig: hasManualGroupConfigRef.current,
        initialSettingsApplied: initialSettingsAppliedRef.current,
      });
      return;
    }

    // 额外检查：确保初始设置已经应用完成
    if (!initialSettingsAppliedRef.current) {
      console.log('🔍 初始设置未应用完成，跳过保存');
      return;
    }

    const settingsString = JSON.stringify(settings);
    // Only save if settings actually changed
    if (settingsString === prevSettingsRef.current) {
      console.log('🔍 设置未变化，跳过保存');
      return;
    }

    // 检查是否是默认配置（避免保存默认配置覆盖用户设置）
    const isDefaultConfig = JSON.stringify(settings) === JSON.stringify(initialTaskListSettings);
    if (
      isDefaultConfig &&
      prevSettingsRef.current &&
      prevSettingsRef.current !== JSON.stringify(initialTaskListSettings)
    ) {
      console.log('🔍 检测到默认配置但之前有用户设置，跳过保存以避免覆盖');
      return;
    }

    console.log('🔍 设置已变化，准备保存:', {
      oldLength: prevSettingsRef.current?.length || 0,
      newLength: settingsString.length,
      columnBackgrounds: settings.columnBackgrounds,
      tableStyleConfig: settings.tableStyleConfig,
      isDefaultConfig,
    });

    // 使用防抖保存
    debouncedSaveSettings(settings);
  }, [settings, isSettingsLoaded, debouncedSaveSettings]);

  const updateSetting = useCallback(
    <K extends keyof TaskListStoredSettings>(key: K, value: TaskListStoredSettings[K]) => {
      setSettingsState(prev => ({ ...prev, [key]: value }));
    },
    []
  );

  // 便捷更新方法
  const updateDisplayMode = useCallback(
    (mode: TaskListDisplayMode) => {
      updateSetting('displayMode', mode);
    },
    [updateSetting]
  );

  const updateDensity = useCallback(
    (density: Exclude<TaskListDensityMode, '' | 'card'>) => {
      updateSetting('density', density);
    },
    [updateSetting]
  );

  const updateVehicleDisplayMode = useCallback(
    (mode: VehicleDisplayMode) => {
      updateSetting('vehicleDisplayMode', mode);
    },
    [updateSetting]
  );

  // 更新固定列样式配置
  const updateStickyColumnStyle = useCallback(
    (
      stickyStyle: Partial<
        NonNullable<TaskListStoredSettings['tableStyleConfig']>['stickyColumnStyle']
      >
    ) => {
      setSettingsState(
        produce(draft => {
          if (!draft.tableStyleConfig) {
            draft.tableStyleConfig = {};
          }
          if (!draft.tableStyleConfig.stickyColumnStyle) {
            draft.tableStyleConfig.stickyColumnStyle = {};
          }
          Object.assign(draft.tableStyleConfig.stickyColumnStyle, stickyStyle);
        })
      );
    },
    []
  );

  const updateColumnVisibility = useCallback((columnId: string, visible: boolean) => {
    setSettingsState(
      produce(draft => {
        draft.columnVisibility[columnId] = visible;

        if (columnId === 'completedProgress' && visible) {
          draft.columnVisibility['requiredVolume'] = false;
          draft.columnVisibility['completedVolume'] = false;
        } else if ((columnId === 'requiredVolume' || columnId === 'completedVolume') && visible) {
          if (
            columnId === 'requiredVolume'
              ? draft.columnVisibility['completedVolume'] !== false
              : draft.columnVisibility['requiredVolume'] !== false
          ) {
            draft.columnVisibility['completedProgress'] = false;
          }
        }
      })
    );
  }, []);

  const toggleGrouping = useCallback(() => {
    handleToggleGrouping();
  }, []);

  const cancelGrouping = useCallback(() => {
    updateGroupConfig({ enabled: false });
  }, []);

  const resetSettings = useCallback(() => {
    resetAllSettings();
  }, []);

  // 优化列可见性更新
  const handleColumnVisibilityChange = useCallback((columnId: string, checked: boolean) => {
    setSettingsState(
      produce(draft => {
        draft.columnVisibility[columnId] = checked;

        if (columnId === 'completedProgress' && checked) {
          draft.columnVisibility['requiredVolume'] = false;
          draft.columnVisibility['completedVolume'] = false;
        } else if ((columnId === 'requiredVolume' || columnId === 'completedVolume') && checked) {
          if (
            columnId === 'requiredVolume'
              ? draft.columnVisibility['completedVolume'] !== false
              : draft.columnVisibility['requiredVolume'] !== false
          ) {
            draft.columnVisibility['completedProgress'] = false;
          }
        }
      })
    );
  }, []);

  const handleColumnOrderChange = useCallback(
    (newOrder: string[]) => {
      updateSetting('columnOrder', newOrder);
    },
    [updateSetting]
  );

  const handleSingleColumnWidthChange = useCallback((columnId: string, width: number) => {
    setSettingsState(prev => {
      const newWidths = { ...prev.columnWidths, [columnId]: width };
      return { ...prev, columnWidths: newWidths };
    });
  }, []);

  // 字段样式更新
  const handleColumnTextStyleChange = useCallback(
    (columnId: StyleableColumnId, styleProperty: keyof ColumnTextStyle, valueKey: string) => {
      flushSync(() => {
        setSettingsState(
          produce(draft => {
            if (!draft.columnTextStyles[columnId]) {
              draft.columnTextStyles[columnId] = {};
            }

            if (valueKey === 'default') {
              delete draft.columnTextStyles[columnId][styleProperty];
              // 如果对象为空，删除整个键
              if (Object.keys(draft.columnTextStyles[columnId]).length === 0) {
                delete draft.columnTextStyles[columnId];
              }
            } else {
              draft.columnTextStyles[columnId][styleProperty] = valueKey;
            }
          })
        );
      });
    },
    []
  );

  const handleColumnBackgroundChange = useCallback(
    (columnId: string, valueKeyToStore: string) => {
      console.log('🎨 handleColumnBackgroundChange:', { columnId, valueKeyToStore });

      // 确保不在初始加载期间
      if (isInitialLoadRef.current) {
        console.log('🎨 初始加载期间，跳过背景设置');
        return;
      }

      flushSync(() => {
        setSettingsState(
          produce(draft => {
            const newBackgrounds = { ...draft.columnBackgrounds };
            console.log('🎨 当前背景设置:', draft.columnBackgrounds);

            if (
              valueKeyToStore === 'default-solid' ||
              valueKeyToStore === 'default-semi' ||
              valueKeyToStore === ''
            ) {
              console.log('🎨 删除背景设置:', columnId);
              delete newBackgrounds[columnId];
            } else {
              console.log('🎨 设置背景:', { columnId, valueKeyToStore });
              newBackgrounds[columnId] = valueKeyToStore;
            }

            draft.columnBackgrounds = newBackgrounds;
            console.log('🎨 新背景设置:', newBackgrounds);
          })
        );
      });

      // 强制触发保存（绕过防抖）
      setTimeout(() => {
        const currentSettings = JSON.stringify(settings);
        console.log('🎨 强制检查保存:', { currentSettings: currentSettings.length });
      }, 100);
    },
    [settings]
  );

  const handleVehiclesPerRowChange = useCallback((vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => {
    setSettingsState(prev => {
      const cardWidthString = prev.inTaskVehicleCardStyles.cardWidth || 'w-14';
      const cardWidthPx = (() => {
        switch (cardWidthString) {
          case 'w-8':
            return 32;
          case 'w-9':
            return 36;
          case 'w-10':
            return 40;
          case 'w-12':
            return 48;
          case 'w-14':
            return 56;
          case 'w-16':
            return 64;
          case 'w-20':
            return 80;
          case 'w-24':
            return 96;
          default: {
            const match = (cardWidthString as string).match(/w-(\d+)/);
            return match ? parseInt(match[1] || '14', 10) * 4 : 56;
          }
        }
      })();

      const currentDensity = prev.density || 'normal';
      const densityConfig = getDensityNumericConfig(currentDensity);
      const gapPx = densityConfig.gap;
      const cellPaddingPx = densityConfig.cellHorizontalPadding;

      const extraWidthForScrollbarEtc = 11;

      const newColumnWidth =
        vehiclesPerRow * cardWidthPx +
        (vehiclesPerRow - 1) * gapPx +
        cellPaddingPx +
        extraWidthForScrollbarEtc;

      return {
        ...prev,
        inTaskVehicleCardStyles: {
          ...prev.inTaskVehicleCardStyles,
          vehiclesPerRow,
        },
        columnWidths: {
          ...prev.columnWidths,
          dispatchedVehicles: Math.max(150, Math.round(newColumnWidth)),
        },
      };
    });
  }, []);

  const handleCustomDispatchVehiclesColumnWidth = useCallback((width: number) => {
    setSettingsState(prev => {
      return {
        ...prev,
        columnWidths: {
          ...prev.columnWidths,
          dispatchedVehicles: width,
        },
      };
    });
  }, []);

  const resetAllSettings = useCallback(() => {
    try {
      // 设置标志防止保存
      isInitialLoadRef.current = true;
      initialSettingsAppliedRef.current = false;

      localStorage.removeItem(TASK_LIST_SETTINGS_KEY);
      sessionStorage.removeItem(TASK_LIST_SETTINGS_KEY + '_backup');

      setSettingsState(initialTaskListSettings);
      prevSettingsRef.current = JSON.stringify(initialTaskListSettings);

      // 延迟重置标志
      setTimeout(() => {
        isInitialLoadRef.current = false;
        initialSettingsAppliedRef.current = true;
      }, 100);

      toast({ title: '样式已重置', description: '任务列表样式已恢复为默认设置。' });
    } catch (error) {
      console.error('Failed to reset settings:', error);
      setSettingsState(initialTaskListSettings);
      toast({
        title: '重置失败',
        description: '无法重置配置，已恢复为初始设置。',
        variant: 'destructive',
      });
    }
  }, [toast]);

  /**
   * 更新分组配置
   */
  const updateGroupConfig = useCallback((newGroupConfig: Partial<TaskGroupConfig>) => {
    console.log('🔍 updateGroupConfig 调用:', newGroupConfig);

    isManualUpdateRef.current = true;
    hasManualGroupConfigRef.current = true;

    flushSync(() => {
      setSettingsState(
        produce(draft => {
          console.log('🔍 updateGroupConfig 设置前:', draft.groupConfig);
          Object.assign(draft.groupConfig, newGroupConfig);
          if (newGroupConfig.groupHeaderStyle) {
            Object.assign(draft.groupConfig.groupHeaderStyle, newGroupConfig.groupHeaderStyle);
          }
          console.log('🔍 updateGroupConfig 设置后:', draft.groupConfig);
        })
      );
    });

    // 强制重新渲染
    setTimeout(() => {
      forceUpdate({});
    }, 0);

    // 延迟重置手动更新标志
    setTimeout(() => {
      hasManualGroupConfigRef.current = false;
    }, 1000);
  }, []);

  /**
   * 切换分组功能开关
   */
  const handleToggleGrouping = useCallback(() => {
    isManualUpdateRef.current = true;
    hasManualGroupConfigRef.current = true;

    setSettingsState(
      produce(draft => {
        draft.groupConfig.enabled = !draft.groupConfig.enabled;
      })
    );
  }, []);

  /**
   * 设置分组字段
   */
  const handleSetGroupBy = useCallback((groupBy: TaskGroupConfig['groupBy']) => {
    isManualUpdateRef.current = true;
    hasManualGroupConfigRef.current = true;

    setSettingsState(
      produce(draft => {
        draft.groupConfig.groupBy = groupBy;
        draft.groupConfig.enabled = groupBy !== 'none';
        // 清空折叠状态，让新分组默认展开
        draft.groupConfig.defaultCollapsed = [];
      })
    );
  }, []);

  /**
   * 切换分组折叠状态 - 优化版本，减少状态更新频率
   */
  const handleToggleGroupCollapse = useCallback((groupKey: string) => {
    isManualUpdateRef.current = true;
    hasManualGroupConfigRef.current = true;

    setSettingsState(
      produce(draft => {
        const currentCollapsed = draft.groupConfig.defaultCollapsed || [];
        const isCollapsed = currentCollapsed.includes(groupKey);

        draft.groupConfig.defaultCollapsed = isCollapsed
          ? currentCollapsed.filter(key => key !== groupKey)
          : [...currentCollapsed, groupKey];
      })
    );
  }, []);

  const exportSettings = useCallback(() => {
    if (!isSettingsLoaded) {
      toast({ title: '设置尚未加载', description: '请稍后再试。', variant: 'destructive' });
      return;
    }
    const jsonString = JSON.stringify(settings, null, 2);
    // 使用安全的下载函数
    safeDownloadFile(jsonString, 'task-list-settings.json', 'application/json');

    toast({ title: '配置已导出', description: '任务列表样式配置已导出。' });
  }, [settings, toast, isSettingsLoaded]);

  const handleImportFile = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const text = (e.target as FileReader)?.result;
          if (typeof text !== 'string') throw new Error('File content is not a string.');

          const imported = JSON.parse(text) as Partial<TaskListStoredSettings>;
          const reconciled: TaskListStoredSettings = {
            ...initialTaskListSettings,
            ...imported,
            inTaskVehicleCardStyles: {
              ...initialTaskListSettings.inTaskVehicleCardStyles,
              ...(imported.inTaskVehicleCardStyles || {}),
              cardGradient: imported.inTaskVehicleCardStyles?.cardGradient, // Ensure gradient is loaded
            },

            columnBackgrounds: imported.columnBackgrounds || {},
            columnTextStyles: imported.columnTextStyles || {},
            groupConfig: {
              ...initialTaskGroupConfig,
              ...(imported.groupConfig || {}),
              groupHeaderStyle: {
                ...initialTaskGroupConfig.groupHeaderStyle,
                ...(imported.groupConfig?.groupHeaderStyle || {}),
              },
            },
          };

          // Column Order Reconciliation on import
          const allColumnIdsFromConfigSortedOnImport = ALL_TASK_COLUMNS_CONFIG.slice()
            .sort((a, b) => (a.order || 999) - (b.order || 999))
            .map(c => c.id as string);

          if (imported.columnOrder && Array.isArray(imported.columnOrder)) {
            const currentConfigIdsSetOnImport = new Set(allColumnIdsFromConfigSortedOnImport);
            const validStoredOrderOnImport = imported.columnOrder.filter(id =>
              currentConfigIdsSetOnImport.has(id)
            );
            const allCurrentColsInImportedOrder =
              validStoredOrderOnImport.length === allColumnIdsFromConfigSortedOnImport.length &&
              allColumnIdsFromConfigSortedOnImport.every(id =>
                validStoredOrderOnImport.includes(id)
              );
            if (allCurrentColsInImportedOrder) {
              reconciled.columnOrder = validStoredOrderOnImport;
            } else {
              reconciled.columnOrder = allColumnIdsFromConfigSortedOnImport;
              toast({
                title: '列顺序部分重置',
                description: '导入的列顺序与当前配置不完全匹配，已进行调整。',
                variant: 'default',
                duration: 7000,
              });
            }
          } else {
            reconciled.columnOrder = allColumnIdsFromConfigSortedOnImport;
          }

          const newVisibility: Record<string, boolean> = {};
          ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
            if (colDef.isMandatory) newVisibility[colDef.id as string] = true;
            else if (
              imported.columnVisibility &&
              imported.columnVisibility.hasOwnProperty(colDef.id as string)
            ) {
              newVisibility[colDef.id as string] =
                imported.columnVisibility[colDef.id as string] || false;
            } else newVisibility[colDef.id as string] = colDef.defaultVisible || false;
          });
          if (newVisibility['completedProgress']) {
            newVisibility['requiredVolume'] = false;
            newVisibility['completedVolume'] = false;
          } else if (
            newVisibility['requiredVolume'] !== false ||
            newVisibility['completedVolume'] !== false
          ) {
            newVisibility['completedProgress'] = false;
          }
          reconciled.columnVisibility = newVisibility;

          const newWidths: Record<string, number> = {};
          ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
            newWidths[colDef.id as string] =
              (imported.columnWidths && imported.columnWidths[colDef.id as string]) ||
              colDef.defaultWidth ||
              100;
          });
          reconciled.columnWidths = newWidths;

          setSettingsState(reconciled);
          toast({ title: '配置已导入', description: '任务列表样式已加载并应用。' });
        } catch (err) {
          console.error('Failed to import settings:', err);
          toast({
            title: '导入失败',
            description: err instanceof Error ? err.message : '无法解析配置文件。',
            variant: 'destructive',
          });
        }
      };
      reader.onerror = () =>
        toast({ title: '导入失败', description: '读取文件时发生错误。', variant: 'destructive' });
      reader.readAsText(file);
      if (event.target) event.target.value = '';
    },
    [toast]
  );

  const triggerImport = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const openColumnVisibilityModal = useCallback(() => setIsColumnVisibilityModalOpen(true), []);
  const closeColumnVisibilityModal = useCallback(() => setIsColumnVisibilityModalOpen(false), []);

  const openColumnSpecificStyleModal = useCallback((columnDef: CustomColumnDefinition) => {
    setEditingColumnDef(columnDef);
    setIsColumnSpecificStyleModalOpen(true);
  }, []);
  const closeColumnSpecificStyleModal = useCallback(() => {
    setIsColumnSpecificStyleModalOpen(false);
    setEditingColumnDef(null);
  }, []);

  const openStyleEditorModal = useCallback(() => setIsStyleEditorModalOpen(true), []);
  const closeStyleEditorModal = useCallback(() => setIsStyleEditorModalOpen(false), []);

  // 分组确认弹框方法
  const openGroupByColumnConfirm = useCallback((columnDef: CustomColumnDefinition) => {
    setGroupingColumnDef(columnDef);
    setGroupByColumnConfirmOpen(true);
  }, []);

  const closeGroupByColumnConfirm = useCallback(() => {
    setGroupByColumnConfirmOpen(false);
    setGroupingColumnDef(null);
  }, []);

  const confirmGroupByColumn = useCallback(() => {
    if (groupingColumnDef) {
      console.log('🔍 confirmGroupByColumn 执行:', {
        columnId: groupingColumnDef.id,
        columnLabel: groupingColumnDef.label,
        timestamp: new Date().toLocaleTimeString(),
      });

      // 使用 Zustand 分组状态
      try {
        const { useGroupingStore } = require('@/features/task-management/store/groupingStore');
        const groupingStore = useGroupingStore.getState();
        groupingStore.updateGroupConfig({
          enabled: true,
          groupBy: groupingColumnDef.id as any,
          defaultCollapsed: [], // 清空折叠状态，让新分组默认展开
        });
        console.log('✅ Zustand 分组状态已更新');
      } catch (error) {
        console.error('❌ Zustand 分组状态更新失败:', error);

        // 回退到旧的状态管理
        isManualUpdateRef.current = true;
        hasManualGroupConfigRef.current = true;
        setSettingsState(
          produce(draft => {
            draft.groupConfig.enabled = true;
            draft.groupConfig.groupBy = groupingColumnDef.id as any;
            draft.groupConfig.defaultCollapsed = [];
          })
        );
      }

      // 关闭弹框
      setGroupByColumnConfirmOpen(false);
      setGroupingColumnDef(null);
    }
  }, [groupingColumnDef]);

  // 列选择分组模态框方法
  const openGroupByColumnSelect = useCallback(() => {
    setGroupByColumnSelectOpen(true);
  }, []);

  const closeGroupByColumnSelect = useCallback(() => {
    setGroupByColumnSelectOpen(false);
  }, []);

  const confirmGroupByColumnSelect = useCallback((columnId: string) => {
    console.log('🔍 confirmGroupByColumnSelect 开始:', {
      columnId,
      currentGroupBy: settings.groupConfig.groupBy,
      currentEnabled: settings.groupConfig.enabled,
      timestamp: new Date().toLocaleTimeString(),
    });

    isManualUpdateRef.current = true;
    hasManualGroupConfigRef.current = true;

    flushSync(() => {
      setSettingsState(
        produce(draft => {
          console.log('🔍 设置前:', {
            enabled: draft.groupConfig.enabled,
            groupBy: draft.groupConfig.groupBy,
          });

          draft.groupConfig.enabled = true;
          draft.groupConfig.groupBy = columnId as any;
          // 清空折叠状态，让新分组默认展开
          draft.groupConfig.defaultCollapsed = [];

          console.log('🔍 设置后:', {
            enabled: draft.groupConfig.enabled,
            groupBy: draft.groupConfig.groupBy,
          });
        })
      );
    });

    // 强制重新渲染
    setTimeout(() => {
      forceUpdate({});
    }, 0);

    // 延迟重置手动更新标志，确保不会被其他 useEffect 覆盖
    setTimeout(() => {
      hasManualGroupConfigRef.current = false;
    }, 1000);

    console.log('🔍 状态更新完成');
    // 关闭模态框
    setGroupByColumnSelectOpen(false);
  }, []);

  // 最终调试日志
  console.log('🔍 useTaskListSettings 返回值:', {
    isSettingsLoaded,
    settingsColumnBackgrounds: Object.keys(settings.columnBackgrounds || {}).length,
    settingsTableStyleConfig: !!settings.tableStyleConfig,
    settingsStickyColumnBg: settings.tableStyleConfig?.stickyColumnStyle?.backgroundColor,
  });

  return {
    settings,
    isSettingsLoaded,
    updateSetting,
    // 便捷更新方法
    updateDisplayMode,
    updateDensity,
    updateVehicleDisplayMode,
    updateColumnVisibility,
    updateStickyColumnStyle,
    toggleGrouping,
    cancelGrouping,
    resetSettings,
    // 原有方法
    handleColumnVisibilityChange,
    handleColumnOrderChange,
    handleSingleColumnWidthChange,
    handleColumnTextStyleChange,
    handleColumnBackgroundChange,
    handleVehiclesPerRowChange,
    handleCustomDispatchVehiclesColumnWidth,
    resetAllSettings,
    exportSettings,
    handleImportFile,
    triggerImport,
    fileInputRef,

    isColumnVisibilityModalOpen,
    openColumnVisibilityModal,
    closeColumnVisibilityModal,
    isColumnSpecificStyleModalOpen,
    editingColumnDef,
    openColumnSpecificStyleModal,
    closeColumnSpecificStyleModal,

    isStyleEditorModalOpen,
    openStyleEditorModal,
    closeStyleEditorModal,

    // 分组确认弹框
    isGroupByColumnConfirmOpen,
    groupingColumnDef,
    openGroupByColumnConfirm,
    closeGroupByColumnConfirm,
    confirmGroupByColumn,

    // 列选择分组模态框
    isGroupByColumnSelectOpen,
    openGroupByColumnSelect,
    closeGroupByColumnSelect,
    confirmGroupByColumnSelect,

    // 分组相关
    updateGroupConfig,
    handleToggleGrouping,
    handleSetGroupBy,
    handleToggleGroupCollapse,
    isGroupConfigModalOpen,
    setIsGroupConfigModalOpen,
  };
}
