/**
 * 车辆调度业务逻辑Hook
 * 处理车辆调度的核心业务逻辑
 */

import { useState, useEffect, useMemo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import { useToast } from '@/shared/hooks/use-toast';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import {
  getOutboundVehicles,
  getPendingVehicles,
  getReturnedVehicles,
} from '@/features/vehicle-dispatch/services/vehicleFilteringService';

import type { Vehicle } from '@/core/types';

/**
 * 车辆调度业务逻辑Hook
 */
export function useVehicleDispatchLogic() {
  const { toast } = useToast();

  // Store状态
  const vehiclesFromStore = useAppStore(state => state.vehicles, shallow);
  const reorderVehiclesInListStoreAction = useAppStore(state => state.reorderVehiclesInList);
  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);

  // 车辆显示模式状态
  const {
    vehicleDisplayMode,
    setVehicleDisplayMode,
    vehicleListDisplayMode,
    setVehicleListDisplayMode,
  } = useUiStore(
    state => ({
      vehicleDisplayMode: state.vehicleDisplayMode,
      setVehicleDisplayMode: state.setVehicleDisplayMode,
      vehicleListDisplayMode: state.vehicleListDisplayMode,
      setVehicleListDisplayMode: state.setVehicleListDisplayMode,
    }),
    shallow
  );

  // 车辆数据处理 - 统一使用store中的数据
  const allVehicles = useMemo(() => {
    return Array.isArray(vehiclesFromStore) ? vehiclesFromStore : [];
  }, [vehiclesFromStore]);

  // 车辆列表状态
  const [pendingVehicles, setPendingVehicles] = useState(() => getPendingVehicles(allVehicles));
  const [returnedVehicles, setReturnedVehicles] = useState(() => getReturnedVehicles(allVehicles));
  const outboundVehicles = useMemo(() => getOutboundVehicles(allVehicles), [allVehicles]);

  // 全局调度状态
  const [globalDispatchActive, setGlobalDispatchActive] = useState(true);

  // 同步车辆数据
  useEffect(() => {
    setPendingVehicles(getPendingVehicles(allVehicles));
    setReturnedVehicles(getReturnedVehicles(allVehicles));
  }, [allVehicles]);

  // 车辆排序逻辑
  const handleVisualMove = useCallback(
    (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => {
      const setVehicles = statusList === 'pending' ? setPendingVehicles : setReturnedVehicles;

      setVehicles(prevVehicles => {
        const draggedIndex = prevVehicles.findIndex(v => v.id === draggedId);
        const hoverIndex = prevVehicles.findIndex(v => v.id === hoverId);

        if (draggedIndex === -1 || hoverIndex === -1) return prevVehicles;

        const newVehicles = [...prevVehicles];
        const [draggedVehicle] = newVehicles.splice(draggedIndex, 1);
        if (draggedVehicle) {
          newVehicles.splice(hoverIndex, 0, draggedVehicle);
        }

        return newVehicles;
      });
    },
    []
  );

  // 提交排序到Store
  const handleCommitReorder = useCallback(
    (statusList: 'pending' | 'returned') => {
      const vehicles = statusList === 'pending' ? pendingVehicles : returnedVehicles;
      reorderVehiclesInListStoreAction(
        statusList,
        vehicles.map(v => v.id)
      );

      toast({
        title: '排序已保存',
        description: `${statusList === 'pending' ? '待发车' : '已返回'}车辆列表排序已更新`,
      });
    },
    [pendingVehicles, returnedVehicles, reorderVehiclesInListStoreAction, toast]
  );

  // 切换全局调度状态
  const toggleGlobalDispatch = useCallback(() => {
    setGlobalDispatchActive(prev => !prev);
    toast({
      title: globalDispatchActive ? '调度模式已关闭' : '调度模式已开启',
      description: globalDispatchActive ? '车辆拖拽功能已禁用' : '现在可以拖拽车辆进行调度',
    });
  }, [globalDispatchActive, toast]);

  // 重置车辆排序
  const resetVehicleOrder = useCallback(() => {
    setPendingVehicles(getPendingVehicles(allVehicles));
    setReturnedVehicles(getReturnedVehicles(allVehicles));
    toast({
      title: '排序已重置',
      description: '车辆列表已恢复到默认排序',
    });
  }, [allVehicles, toast]);

  // 获取车辆统计信息
  const getVehicleStats = useCallback(() => {
    return {
      total: allVehicles.length,
      pending: pendingVehicles.length,
      outbound: outboundVehicles.length,
      returned: returnedVehicles.length,
      lastUpdated: new Date().toISOString(),
    };
  }, [allVehicles, pendingVehicles.length, outboundVehicles.length, returnedVehicles.length]);

  return {
    // 车辆数据
    allVehicles,
    pendingVehicles,
    outboundVehicles,
    returnedVehicles,

    // 显示模式
    vehicleDisplayMode,
    setVehicleDisplayMode,
    vehicleListDisplayMode,
    setVehicleListDisplayMode,

    // 调度状态
    globalDispatchActive,
    toggleGlobalDispatch,

    // 排序操作
    handleVisualMove,
    handleCommitReorder,
    resetVehicleOrder,

    // 工具函数
    getVehicleStats,

    // 过滤器
    taskStatusFilter,
  };
}
