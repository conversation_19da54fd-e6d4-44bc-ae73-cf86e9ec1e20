/**
 * 调度模块统一入口
 * 将调度相关功能独立打包，实现按需加载
 */

// ==================== 组件导出 ====================

// 任务列表相关 - 暂时注释掉不存在的组件

// 车辆调度相关 - 暂时注释掉不存在的组件

// ==================== Store导出 ====================

export { useTaskListStore } from '@/features/task-management/store/taskListStore';
export { useVehicleDispatchStore } from '@/features/vehicle-dispatch/store/vehicleDispatchStore';
export { useAppStore } from '@/infrastructure/storage/stores/appStore';

// ==================== Hooks导出 ====================

// 任务相关Hooks - 暂时注释掉不存在的组件
export { useTaskListBusinessLogic } from '@/features/task-management/hooks/use-task-list-business-logic';
export { useTaskContextMenu } from '@/features/task-management/hooks/useTaskContextMenu';
export { useTaskRowHighlight } from '@/features/task-management/hooks/useTaskRowHighlight';
export { useTaskVehicleDragAndDrop } from '@/features/vehicle-dispatch/hooks/useTaskVehicleDragAndDrop';

// 车辆调度相关Hooks
export { useVehicleCardDrag } from '@/features/vehicle-dispatch/hooks/useVehicleCardDrag';
export { useVehicleCardContextMenu } from '@/features/vehicle-dispatch/hooks/useVehicleCardContextMenu';
export { useTankTruckDispatchModal } from '@/features/task-management/hooks/useTankTruckDispatchModal';
export { usePumpTruckDispatchModal } from '@/features/task-management/hooks/usePumpTruckDispatchModal';

// ==================== 服务导出 ====================

// 导出服务函数而不是不存在的对象
export { calculateTaskProgress } from '@/features/task-management/services/taskService';
export {
  getOutboundVehicles,
  getPendingVehicles,
  getReturnedVehicles,
} from '@/features/vehicle-dispatch/services/vehicleFilteringService';
export { filterTasksForDisplay } from '@/features/task-management/services/taskFilteringService';

// ==================== 类型导出 ====================

export type { Task, Vehicle, TaskDispatchStatus, VehicleStatus } from '@/core/types/unified-types';

// ==================== 懒加载组件 ====================

import { lazy } from 'react';
import { createComponentLazyComponent } from '../../core/config/code-splitting';

// 调度相关模态框 - 按使用频率分组懒加载
export const TaskEditModal = createComponentLazyComponent(() => import('@/models/TaskEditModal'), {
  componentName: 'TaskEditModal',
  category: 'modal',
  priority: 'high',
});

export const TankTruckDispatchModal = createComponentLazyComponent(
  () => import('@/models/TankTruckDispatchModal'),
  { componentName: 'TankTruckDispatchModal', category: 'modal', priority: 'high' }
);

export const PumpTruckDispatchModal = createComponentLazyComponent(
  () => import('@/models/PumpTruckDispatchModal'),
  { componentName: 'PumpTruckDispatchModal', category: 'modal', priority: 'medium' }
);

export const TaskProgressModal = createComponentLazyComponent(
  () => import('@/models/TaskProgressModal'),
  { componentName: 'TaskProgressModal', category: 'modal', priority: 'medium' }
);

// 调度相关图表 - 懒加载 (暂时注释掉不存在的组件)
export const TaskProgressChart = lazy(() => import('@/shared/components/charts/TaskProgressChart'));
export const VehicleDensityChart = lazy(
  () => import('@/shared/components/charts/VehicleDensityChart')
);

// ==================== 模块配置 ====================

export const DISPATCH_MODULE_CONFIG = {
  name: 'dispatch',
  version: '1.0.0',
  description: '车辆调度管理模块',
  features: [
    'task-management',
    'vehicle-dispatch',
    'drag-and-drop',
    'real-time-updates',
    'progress-tracking',
  ],
  dependencies: ['@dnd-kit/core', '@dnd-kit/sortable', '@tanstack/react-table', 'zustand'],
} as const;

// ==================== 模块初始化 ====================

export function initializeDispatchModule() {
  console.log('🚛 初始化调度模块...');

  // 预加载关键数据
  if (typeof window !== 'undefined') {
    // 预加载任务数据 (暂时注释掉不存在的方法)
    // import('@/services/taskService').then(({ taskService }) => {
    //   taskService.preloadTasks();
    // });

    // 预加载车辆数据 (暂时注释掉不存在的方法)
    // import('@/services/vehicleFilteringService').then(({ vehicleFilteringService }) => {
    //   vehicleFilteringService.preloadVehicles();
    // });

    console.log('🚛 调度模块初始化完成');
  }
}

// ==================== 性能监控 ====================

export function getDispatchModuleMetrics() {
  return {
    moduleSize: '~600KB',
    loadTime: performance.now(),
    componentsLoaded: ['TaskList', 'VehicleDispatch', 'TaskCard', 'VehicleCard'],
    lazyComponentsAvailable: [
      'TaskEditModal',
      'TankTruckDispatchModal',
      'PumpTruckDispatchModal',
      'TaskProgressChart',
      'VehicleDensityChart',
    ],
  };
}
