/**
 * 车辆调度专用Store
 * 管理车辆调度相关的状态和操作
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { produce } from 'immer';
import type { Vehicle, VehicleDisplayMode, TaskListDisplayMode } from '@/core/types';

export interface VehicleDispatchState {
  // 车辆数据
  allVehicles: Vehicle[];
  pendingVehicles: Vehicle[];
  outboundVehicles: Vehicle[];
  returnedVehicles: Vehicle[];

  // 显示模式
  vehicleDisplayMode: VehicleDisplayMode;
  vehicleListDisplayMode: TaskListDisplayMode;

  // 调度状态
  globalDispatchActive: boolean;
  isReordering: boolean;

  // 选中状态
  selectedVehicleIds: string[];
  selectedVehicle: Vehicle | null;

  // 右键菜单状态
  contextMenu: {
    isOpen: boolean;
    position: { x: number; y: number } | null;
    vehicle: Vehicle | null;
  };

  // 拖拽状态
  dragState: {
    isDragging: boolean;
    draggedVehicle: Vehicle | null;
    sourceList: 'pending' | 'returned' | null;
    targetList: 'pending' | 'returned' | null;
    hoverIndex: number | null;
  };

  // 操作状态
  operationStatus: {
    dispatching: boolean;
    canceling: boolean;
    reordering: boolean;
    loading: boolean;
  };

  // 错误状态
  error: string | null;

  // 统计信息
  stats: {
    total: number;
    pending: number;
    outbound: number;
    returned: number;
    lastUpdated: number | null;
  };
}

export interface VehicleDispatchActions {
  // 数据操作
  setAllVehicles: (vehicles: Vehicle[]) => void;
  updateVehicle: (vehicleId: string, updates: Partial<Vehicle>) => void;
  refreshVehicleLists: () => void;

  // 显示模式操作
  setVehicleDisplayMode: (mode: VehicleDisplayMode) => void;
  setVehicleListDisplayMode: (mode: TaskListDisplayMode) => void;

  // 调度操作
  toggleGlobalDispatch: () => void;
  setGlobalDispatchActive: (active: boolean) => void;

  // 选择操作
  selectVehicle: (vehicleId: string) => void;
  selectMultipleVehicles: (vehicleIds: string[]) => void;
  clearVehicleSelection: () => void;
  setSelectedVehicle: (vehicle: Vehicle | null) => void;

  // 右键菜单操作
  openContextMenu: (vehicle: Vehicle, position: { x: number; y: number }) => void;
  closeContextMenu: () => void;

  // 拖拽操作
  startDrag: (vehicle: Vehicle, sourceList: 'pending' | 'returned') => void;
  updateDragTarget: (targetList: 'pending' | 'returned', hoverIndex: number) => void;
  endDrag: () => void;

  // 排序操作
  reorderVehicles: (
    sourceList: 'pending' | 'returned',
    dragIndex: number,
    hoverIndex: number
  ) => void;
  commitReorder: (listType: 'pending' | 'returned') => void;
  resetVehicleOrder: () => void;

  // 操作状态
  setOperationStatus: (
    operation: keyof VehicleDispatchState['operationStatus'],
    status: boolean
  ) => void;
  setError: (error: string | null) => void;

  // 统计信息
  updateStats: () => void;

  // 重置状态
  reset: () => void;
}

const initialState: VehicleDispatchState = {
  // 车辆数据
  allVehicles: [],
  pendingVehicles: [],
  outboundVehicles: [],
  returnedVehicles: [],

  // 显示模式
  vehicleDisplayMode: 'licensePlate',
  vehicleListDisplayMode: 'horizontal' as any,

  // 调度状态
  globalDispatchActive: false,
  isReordering: false,

  // 选中状态
  selectedVehicleIds: [],
  selectedVehicle: null,

  // 右键菜单状态
  contextMenu: {
    isOpen: false,
    position: null,
    vehicle: null,
  },

  // 拖拽状态
  dragState: {
    isDragging: false,
    draggedVehicle: null,
    sourceList: null,
    targetList: null,
    hoverIndex: null,
  },

  // 操作状态
  operationStatus: {
    dispatching: false,
    canceling: false,
    reordering: false,
    loading: false,
  },

  // 错误状态
  error: null,

  // 统计信息
  stats: {
    total: 0,
    pending: 0,
    outbound: 0,
    returned: 0,
    lastUpdated: null,
  },
};

export const useVehicleDispatchStore = create<VehicleDispatchState & VehicleDispatchActions>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // 数据操作
      setAllVehicles: vehicles => {
        set({ allVehicles: vehicles });
        get().refreshVehicleLists();
        get().updateStats();
      },

      updateVehicle: (vehicleId, updates) =>
        set(
          produce(draft => {
            // 更新所有车辆列表
            const allIndex = draft.allVehicles.findIndex((v: any) => v.id === vehicleId);
            if (allIndex !== -1) {
              Object.assign(draft.allVehicles[allIndex], updates);
            }

            // 更新分类列表
            const updateList = (list: Vehicle[]) => {
              const index = list.findIndex((v: any) => v.id === vehicleId);
              if (index !== -1 && list[index]) {
                Object.assign(list[index], updates);
              }
            };

            updateList(draft.pendingVehicles);
            updateList(draft.outboundVehicles);
            updateList(draft.returnedVehicles);
          })
        ),

      refreshVehicleLists: () => {
        const { allVehicles } = get();
        set({
          pendingVehicles: allVehicles.filter(v => v.status === 'pending'),
          outboundVehicles: allVehicles.filter(v => v.status === 'outbound'),
          returnedVehicles: allVehicles.filter(v => v.status === 'returned'),
        });
      },

      // 显示模式操作
      setVehicleDisplayMode: mode => set({ vehicleDisplayMode: mode }),
      setVehicleListDisplayMode: mode => set({ vehicleListDisplayMode: mode }),

      // 调度操作
      toggleGlobalDispatch: () =>
        set(
          produce(draft => {
            draft.globalDispatchActive = !draft.globalDispatchActive;
          })
        ),
      setGlobalDispatchActive: active => set({ globalDispatchActive: active }),

      // 选择操作
      selectVehicle: vehicleId => set({ selectedVehicleIds: [vehicleId] }),
      selectMultipleVehicles: vehicleIds => set({ selectedVehicleIds: vehicleIds }),
      clearVehicleSelection: () => set({ selectedVehicleIds: [] }),
      setSelectedVehicle: vehicle => set({ selectedVehicle: vehicle }),

      // 右键菜单操作
      openContextMenu: (vehicle, position) =>
        set({
          contextMenu: {
            isOpen: true,
            position,
            vehicle,
          },
        }),
      closeContextMenu: () =>
        set({
          contextMenu: {
            isOpen: false,
            position: null,
            vehicle: null,
          },
        }),

      // 拖拽操作
      startDrag: (vehicle, sourceList) =>
        set({
          dragState: {
            isDragging: true,
            draggedVehicle: vehicle,
            sourceList,
            targetList: null,
            hoverIndex: null,
          },
        }),

      updateDragTarget: (targetList, hoverIndex) =>
        set(
          produce(draft => {
            draft.dragState.targetList = targetList;
            draft.dragState.hoverIndex = hoverIndex;
          })
        ),

      endDrag: () =>
        set({
          dragState: {
            isDragging: false,
            draggedVehicle: null,
            sourceList: null,
            targetList: null,
            hoverIndex: null,
          },
        }),

      // 排序操作
      reorderVehicles: (sourceList, dragIndex, hoverIndex) =>
        set(
          produce(draft => {
            const list = sourceList === 'pending' ? draft.pendingVehicles : draft.returnedVehicles;
            const [draggedVehicle] = list.splice(dragIndex, 1);
            list.splice(hoverIndex, 0, draggedVehicle);
          })
        ),

      commitReorder: listType => {
        // 这里可以调用API保存排序结果
        console.log(`Committing reorder for ${listType} vehicles`);
      },

      resetVehicleOrder: () => {
        get().refreshVehicleLists();
      },

      // 操作状态
      setOperationStatus: (operation, status) =>
        set(
          produce(draft => {
            draft.operationStatus[operation] = status;
          })
        ),

      setError: error => set({ error }),

      // 统计信息
      updateStats: () => {
        const { allVehicles, pendingVehicles, outboundVehicles, returnedVehicles } = get();
        set({
          stats: {
            total: allVehicles.length,
            pending: pendingVehicles.length,
            outbound: outboundVehicles.length,
            returned: returnedVehicles.length,
            lastUpdated: Date.now(),
          },
        });
      },

      // 重置状态
      reset: () => set(initialState),
    })),
    {
      name: 'vehicle-dispatch-store',
    }
  )
);
