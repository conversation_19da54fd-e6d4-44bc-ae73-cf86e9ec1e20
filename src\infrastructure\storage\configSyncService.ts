/**
 * 配置同步服务
 * 确保配置在服务器部署后能正确持久化和同步
 */

import { TaskListStoredSettings } from '@/core/types';

export interface ConfigSyncOptions {
  enableCrossTabSync?: boolean;
  enableBackup?: boolean;
  syncInterval?: number;
  maxRetries?: number;
}

class ConfigSyncService {
  private static instance: ConfigSyncService;
  private syncInterval: number = 5000; // 5秒同步一次
  private maxRetries: number = 3;
  private enableCrossTabSync: boolean = true;
  private enableBackup: boolean = true;
  private syncTimer: NodeJS.Timeout | null = null;
  private retryCount: Map<string, number> = new Map();

  static getInstance(): ConfigSyncService {
    if (!ConfigSyncService.instance) {
      ConfigSyncService.instance = new ConfigSyncService();
    }
    return ConfigSyncService.instance;
  }

  /**
   * 初始化配置同步服务
   */
  initialize(options: ConfigSyncOptions = {}) {
    this.enableCrossTabSync = options.enableCrossTabSync ?? true;
    this.enableBackup = options.enableBackup ?? true;
    this.syncInterval = options.syncInterval ?? 5000;
    this.maxRetries = options.maxRetries ?? 3;

    if (typeof window !== 'undefined') {
      this.startPeriodicSync();
      this.setupCrossTabSync();
    }
  }

  /**
   * 保存配置（增强版）
   */
  async saveConfig<T>(key: string, data: T): Promise<boolean> {
    if (typeof window === 'undefined') {
      console.log(`📝 服务器端跳过保存: ${key}`);
      return false;
    }

    const serializedData = JSON.stringify(data);
    let success = false;

    try {
      // 在设置新值之前获取旧值
      const oldValue = localStorage.getItem(key);

      // 1. 保存到localStorage
      localStorage.setItem(key, serializedData);
      success = true;

      // 2. 保存到sessionStorage作为备份
      if (this.enableBackup) {
        sessionStorage.setItem(`${key}_backup`, serializedData);
      }

      // 3. 触发跨标签页同步事件
      if (this.enableCrossTabSync) {
        window.dispatchEvent(new StorageEvent('storage', {
          key,
          newValue: serializedData,
          oldValue: oldValue,
          storageArea: localStorage
        }));
      }

      // 4. 保存到IndexedDB（如果支持）
      if (this.enableBackup) {
        this.saveToIndexedDB(key, data).catch(error => {
          console.warn('IndexedDB保存失败:', error);
        });
      }

      console.log(`✅ 配置保存成功: ${key}`);
      this.retryCount.delete(key);
      return true;

    } catch (error) {
      console.error(`❌ 配置保存失败: ${key}`, error);
      
      // 重试机制
      const currentRetries = this.retryCount.get(key) || 0;
      if (currentRetries < this.maxRetries) {
        this.retryCount.set(key, currentRetries + 1);
        setTimeout(() => {
          this.saveConfig(key, data);
        }, 1000 * (currentRetries + 1)); // 递增延迟重试
      }
      
      return false;
    }
  }

  /**
   * 加载配置（增强版）
   */
  async loadConfig<T>(key: string, defaultValue: T): Promise<T> {
    if (typeof window === 'undefined') {
      console.log(`📝 服务器端使用默认配置: ${key}`);
      return defaultValue;
    }

    try {
      // 1. 优先从localStorage加载
      let stored = localStorage.getItem(key);
      
      // 2. 如果localStorage中没有，尝试从sessionStorage备份加载
      if (!stored && this.enableBackup) {
        stored = sessionStorage.getItem(`${key}_backup`);
        if (stored) {
          console.log(`📝 从sessionStorage备份恢复: ${key}`);
          // 恢复到localStorage
          localStorage.setItem(key, stored);
        }
      }

      // 3. 如果还是没有，尝试从IndexedDB加载
      if (!stored && this.enableBackup) {
        const indexedDBData = await this.loadFromIndexedDB(key);
        if (indexedDBData) {
          stored = JSON.stringify(indexedDBData);
          console.log(`📝 从IndexedDB恢复: ${key}`);
          // 恢复到localStorage
          localStorage.setItem(key, stored);
        }
      }

      if (stored) {
        const parsedData = JSON.parse(stored);
        console.log(`✅ 配置加载成功: ${key}`);
        return parsedData;
      }

      console.log(`📝 使用默认配置: ${key}`);
      return defaultValue;

    } catch (error) {
      console.error(`❌ 配置加载失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      this.syncConfigs();
    }, this.syncInterval);
  }

  /**
   * 设置跨标签页同步
   */
  private setupCrossTabSync() {
    if (!this.enableCrossTabSync) return;

    window.addEventListener('storage', (e) => {
      if (e.key && e.newValue) {
        console.log(`📝 检测到跨标签页配置变更: ${e.key}`);
        // 触发自定义事件，通知应用配置已更新
        window.dispatchEvent(new CustomEvent('configSync', {
          detail: { key: e.key, value: e.newValue }
        }));
      }
    });
  }

  /**
   * 同步配置
   */
  private async syncConfigs() {
    // 这里可以实现与服务器的配置同步逻辑
    // 目前主要确保本地存储的一致性
    try {
      const keys = ['taskListSettings_v3.3'];
      
      for (const key of keys) {
        const localData = localStorage.getItem(key);
        const sessionData = sessionStorage.getItem(`${key}_backup`);
        
        // 如果localStorage中的数据丢失，但sessionStorage中有备份，则恢复
        if (!localData && sessionData) {
          localStorage.setItem(key, sessionData);
          console.log(`🔄 自动恢复配置: ${key}`);
        }
      }
    } catch (error) {
      console.error('配置同步失败:', error);
    }
  }

  /**
   * 保存到IndexedDB
   */
  private async saveToIndexedDB(key: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('TMHConfigDB', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['configs'], 'readwrite');
        const store = transaction.objectStore('configs');
        
        store.put({ key, data, timestamp: Date.now() });
        
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('configs')) {
          db.createObjectStore('configs', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * 从IndexedDB加载
   */
  private async loadFromIndexedDB(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('TMHConfigDB', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['configs'], 'readonly');
        const store = transaction.objectStore('configs');
        const getRequest = store.get(key);
        
        getRequest.onsuccess = () => {
          const result = getRequest.result;
          resolve(result ? result.data : null);
        };
        
        getRequest.onerror = () => reject(getRequest.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('configs')) {
          db.createObjectStore('configs', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.retryCount.clear();
  }
}

// 全局实例
export const configSyncService = ConfigSyncService.getInstance();

// 便捷方法
export const saveTaskListSettings = (settings: TaskListStoredSettings) => {
  return configSyncService.saveConfig('taskListSettings_v3.3', settings);
};

export const loadTaskListSettings = (defaultSettings: TaskListStoredSettings) => {
  return configSyncService.loadConfig('taskListSettings_v3.3', defaultSettings);
};

// 初始化服务
if (typeof window !== 'undefined') {
  configSyncService.initialize({
    enableCrossTabSync: true,
    enableBackup: true,
    syncInterval: 5000,
    maxRetries: 3
  });
}
