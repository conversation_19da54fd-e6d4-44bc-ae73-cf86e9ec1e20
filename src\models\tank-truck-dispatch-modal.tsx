'use client';

import React, { useMemo, useCallback, useState } from 'react';

import { Save, Truck, X } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import type { Task, Vehicle } from '@/core/types';

interface TankTruckDispatchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Partial<Task>;
  vehicle?: Partial<Vehicle>;
  onConfirm: (dispatchData: TankTruckDispatchData) => void;
}

export interface TankTruckDispatchData {
  // 顶部基本信息（不可编辑）
  dispatchNumber: string;
  taskNumber: string;
  contractNumber: string;

  // 左侧第一列
  materialType: string; // 货物类型：砼、砂浆、水票
  productionLine: string; // 生产线：线1、线2...
  vehicleNumber: string; // 车号
  driverName: string; // 司机
  pouringMethod: string; // 浇筑方式：泵送、自卸

  // 左侧第二列
  mortarType: string; // 砂浆类型（联动货物类型）
  mainOperator: string; // 主机操作
  qualityInspector: string; // 质检员
  outputTemperature: string; // 出机温度
  siteDispatcher: string; // 现场调度

  // 左侧中间
  volumeSettingType: string; // 方量设定类型：最大设定、不超载、根据皮重实时调整
  volumeFrom: number; // 方量起始值
  volumeTo: number; // 方量结束值
  volumeUnit: string; // 方量单位
  pumpTruckNumber: string; // 泵车号

  // 右侧工程信息（大部分不可编辑）
  projectName: string;
  constructionUnit: string;
  constructionSite: string;
  strengthGrade: string;
  slump: string; // 坍落度
  dispatcher: string; // 调度员
  expectedPumpTruck: string; // 预计泵车
  productType: string; // 产品种类
  printVolume: number; // 打印方量
  printWeight: number; // 打印重量
  loadCapacity: string; // 载重
  prepaidBalance: string; // 预存余额
  dispatchMethod: string; // 发车方式

  // 各种提示（可填可选）
  comments: string; // 注释
  productionTips: string; // 生产提示
  driverTips: string; // 司机提示
  printTips: string; // 打印提示
  printTicket: boolean; // 打票

  // 底部多选项
  autoPlay: boolean;
  sandMortar: boolean;
  washPump: boolean;
  carryPump: boolean;
  notToMixingStation: boolean;
  allowWeighRoomEdit: boolean;
}

export const TankTruckDispatchModal: React.FC<TankTruckDispatchModalProps> = ({
  open,
  onOpenChange,
  task,
  vehicle,
  onConfirm,
}) => {
  console.log('🚛 TankTruckDispatchModal render:', {
    open,
    task: task?.taskNumber,
    vehicle: vehicle?.vehicleNumber,
    hasOnConfirm: !!onConfirm,
  });
  const [formData, setFormData] = useState<TankTruckDispatchData>({
    // 顶部基本信息（不可编辑）
    dispatchNumber: 'JC125-00001',
    taskNumber: task?.taskNumber || 'JC125-00003',
    contractNumber: task?.taskNumber || 'JC125-00002',

    // 左侧第一列
    materialType: '砼',
    productionLine: '线1',
    vehicleNumber: vehicle?.vehicleNumber || '10',
    driverName: '未志国',
    pouringMethod: '自卸',

    // 左侧第二列
    mortarType: '',
    mainOperator: '',
    qualityInspector: '',
    outputTemperature: '',
    siteDispatcher: '',

    // 左侧中间
    volumeSettingType: '最大设定',
    volumeFrom: 12,
    volumeTo: 12,
    volumeUnit: '方',
    pumpTruckNumber: '',

    // 右侧工程信息
    projectName: task?.projectName || '个人自建',
    constructionUnit: task?.constructionUnit || '长治市郊善堂烧机工程有限公司',
    constructionSite: task?.constructionSite || '独居',
    strengthGrade: task?.strength || 'C15',
    slump: '180±20',
    dispatcher: '系统管理员',
    expectedPumpTruck: '',
    productType: 'C15商品混凝土',
    printVolume: 12,
    printWeight: 30.04,
    loadCapacity: '12方',
    prepaidBalance: '0.00',
    dispatchMethod: '生成发货单',

    // 各种提示
    comments: '',
    productionTips: '',
    driverTips: '',
    printTips: '',
    printTicket: false,

    // 底部多选项
    autoPlay: true,
    sandMortar: false,
    washPump: false,
    carryPump: false,
    notToMixingStation: false,
    allowWeighRoomEdit: false,
  });

  // 货物类型和砂浆类型的联动选项
  const getMortarTypeOptions = (materialType: string) => {
    switch (materialType) {
      case '砼':
        return []; // 砼类型时砂浆类型不可选择
      case '砂浆':
        return ['接茬', '润管', '抹灰', '砌筑', '防水'];
      case '水票':
        return ['内部结算', '客户结算'];
      default:
        return [];
    }
  };

  const updateField = (field: keyof TankTruckDispatchData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // 当货物类型改变时，重置砂浆类型
      if (field === 'materialType') {
        newData.mortarType = '';
      }

      return newData;
    });
  };

  const handleConfirm = () => {
    onConfirm(formData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-6xl max-h-[90vh] overflow-y-auto p-0'>
        <DialogHeader className='px-2 py-1 border-b bg-blue-50'>
          <DialogTitle className='flex items-center gap-1 text-xs font-bold text-blue-900'>
            <Truck className='w-3 h-3' />
            罐车发车单
          </DialogTitle>
        </DialogHeader>

        <div className='p-1 space-y-1'>
          {/* 基础信息卡片 */}
          <div className='bg-gray-50 rounded border p-1'>
            <div className='text-xs font-medium text-gray-700 mb-1'>基础信息</div>
            <div className='grid grid-cols-3 gap-1'>
              <div>
                <Label className='text-xs text-gray-500'>发货单编号</Label>
                <Input value={formData.dispatchNumber} className='h-5 text-xs bg-white' readOnly />
              </div>
              <div>
                <Label className='text-xs text-gray-500'>任务编号</Label>
                <Input value={formData.taskNumber} className='h-5 text-xs bg-white' readOnly />
              </div>
              <div>
                <Label className='text-xs text-gray-500'>合同编号</Label>
                <Input value={formData.contractNumber} className='h-5 text-xs bg-white' readOnly />
              </div>
            </div>
          </div>

          {/* 主体内容区域 - 紧凑卡片布局 */}
          <div className='grid grid-cols-3 gap-1'>
            {/* 车辆调度卡片 */}
            <div className='bg-blue-50 rounded border p-1'>
              <div className='text-xs font-medium text-blue-700 mb-1 flex items-center gap-1'>
                <Truck className='w-3 h-3' />
                车辆调度
              </div>
              <div className='space-y-1'>
                <div>
                  <Label className='text-xs text-gray-500'>货物类型</Label>
                  <Select
                    value={formData.materialType}
                    onValueChange={v => updateField('materialType', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='砼'>砼</SelectItem>
                      <SelectItem value='砂浆'>砂浆</SelectItem>
                      <SelectItem value='水票'>水票</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>生产线</Label>
                  <Select
                    value={formData.productionLine}
                    onValueChange={v => updateField('productionLine', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='线1'>线1</SelectItem>
                      <SelectItem value='线2'>线2</SelectItem>
                      <SelectItem value='线3'>线3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>车号</Label>
                  <Select
                    value={formData.vehicleNumber}
                    onValueChange={v => updateField('vehicleNumber', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='10'>10</SelectItem>
                      <SelectItem value='11'>11</SelectItem>
                      <SelectItem value='12'>12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>司机</Label>
                  <Select
                    value={formData.driverName}
                    onValueChange={v => updateField('driverName', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='未志国'>未志国</SelectItem>
                      <SelectItem value='张三'>张三</SelectItem>
                      <SelectItem value='李四'>李四</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>浇筑方式</Label>
                  <Select
                    value={formData.pouringMethod}
                    onValueChange={v => updateField('pouringMethod', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='泵送'>泵送</SelectItem>
                      <SelectItem value='自卸'>自卸</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>砂浆类型</Label>
                  <Select
                    value={formData.mortarType}
                    onValueChange={v => updateField('mortarType', v)}
                    disabled={getMortarTypeOptions(formData.materialType).length === 0}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue
                        placeholder={formData.materialType === '砼' ? '不可选择' : '请选择'}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {getMortarTypeOptions(formData.materialType).map(option => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>主机操作</Label>
                  <Select
                    value={formData.mainOperator}
                    onValueChange={v => updateField('mainOperator', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue placeholder='请选择' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='操作员A'>操作员A</SelectItem>
                      <SelectItem value='操作员B'>操作员B</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>质检员</Label>
                  <Select
                    value={formData.qualityInspector}
                    onValueChange={v => updateField('qualityInspector', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue placeholder='请选择' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='质检员1'>质检员1</SelectItem>
                      <SelectItem value='质检员2'>质检员2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>出机温度</Label>
                  <Input
                    value={formData.outputTemperature}
                    onChange={e => updateField('outputTemperature', e.target.value)}
                    className='h-5 text-xs'
                    placeholder='请输入'
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>现场调度</Label>
                  <Select
                    value={formData.siteDispatcher}
                    onValueChange={v => updateField('siteDispatcher', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue placeholder='请选择' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='调度员1'>调度员1</SelectItem>
                      <SelectItem value='调度员2'>调度员2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* 工程信息卡片 */}
            <div className='bg-green-50 rounded border p-1'>
              <div className='text-xs font-medium text-green-700 mb-1 flex items-center gap-1'>
                <Save className='w-3 h-3' />
                工程信息
              </div>
              <div className='space-y-1'>
                <div>
                  <Label className='text-xs text-gray-500'>工程名称</Label>
                  <Input value={formData.projectName} className='h-5 text-xs bg-gray-50' readOnly />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>建设单位</Label>
                  <Input
                    value={formData.constructionUnit}
                    className='h-5 text-xs bg-gray-50'
                    readOnly
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>施工部位</Label>
                  <Input
                    value={formData.constructionSite}
                    className='h-5 text-xs bg-gray-50'
                    readOnly
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>强度等级</Label>
                  <Input
                    value={formData.strengthGrade}
                    className='h-5 text-xs bg-gray-50'
                    readOnly
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>坍落度</Label>
                  <Input value={formData.slump} className='h-5 text-xs bg-gray-50' readOnly />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>调度员</Label>
                  <Input value={formData.dispatcher} className='h-5 text-xs bg-gray-50' readOnly />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>产品种类</Label>
                  <Input value={formData.productType} className='h-5 text-xs bg-gray-50' readOnly />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>打印方量</Label>
                  <Input
                    type='number'
                    value={formData.printVolume}
                    onChange={e => updateField('printVolume', Number(e.target.value))}
                    className='h-5 text-xs'
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>打印重量</Label>
                  <Input
                    type='number'
                    value={formData.printWeight}
                    onChange={e => updateField('printWeight', Number(e.target.value))}
                    className='h-5 text-xs'
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>发车方式</Label>
                  <Select
                    value={formData.dispatchMethod}
                    onValueChange={v => updateField('dispatchMethod', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='生成发货单'>生成发货单</SelectItem>
                      <SelectItem value='直接发车'>直接发车</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* 装载设置卡片 */}
            <div className='bg-orange-50 rounded border p-1'>
              <div className='text-xs font-medium text-orange-700 mb-1 flex items-center gap-1'>
                <Truck className='w-3 h-3' />
                装载设置
              </div>
              <div className='space-y-1'>
                {/* 方量设定类型 */}
                <div>
                  <Label className='text-xs text-gray-500'>设定类型</Label>
                  <Select
                    value={formData.volumeSettingType}
                    onValueChange={v => updateField('volumeSettingType', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='最大设定'>最大设定</SelectItem>
                      <SelectItem value='不超载'>不超载</SelectItem>
                      <SelectItem value='根据皮重实时调整'>根据皮重实时调整</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 方量输入 */}
                <div className='grid grid-cols-3 gap-1 items-end'>
                  <div>
                    <Label className='text-xs text-gray-500'>方量</Label>
                    <Input
                      type='number'
                      value={formData.volumeFrom}
                      onChange={e => updateField('volumeFrom', Number(e.target.value))}
                      className='h-5 text-xs'
                    />
                  </div>
                  <div className='text-center text-xs'>-</div>
                  <div>
                    <Input
                      type='number'
                      value={formData.volumeTo}
                      onChange={e => updateField('volumeTo', Number(e.target.value))}
                      className='h-5 text-xs'
                    />
                  </div>
                </div>

                <div>
                  <Label className='text-xs text-gray-500'>单位</Label>
                  <Input
                    value={formData.volumeUnit}
                    onChange={e => updateField('volumeUnit', e.target.value)}
                    className='h-5 text-xs'
                  />
                </div>

                {/* 泵车号 */}
                <div>
                  <Label className='text-xs text-gray-500'>泵车号</Label>
                  <Select
                    value={formData.pumpTruckNumber}
                    onValueChange={v => updateField('pumpTruckNumber', v)}
                  >
                    <SelectTrigger className='h-5 text-xs'>
                      <SelectValue placeholder='请选择' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='泵车发车单'>泵车发车单</SelectItem>
                      <SelectItem value='返回泵车'>返回泵车</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 车辆信息表格 */}
                <div className='border rounded text-xs'>
                  <div className='bg-gray-50 px-1 py-0.5 text-xs font-medium border-b'>
                    车辆信息
                  </div>
                  <table className='w-full'>
                    <thead className='bg-gray-50'>
                      <tr>
                        <th className='px-1 py-0.5 text-left text-xs'>车辆</th>
                        <th className='px-1 py-0.5 text-left text-xs'>司机</th>
                        <th className='px-1 py-0.5 text-left text-xs'>方量</th>
                        <th className='px-1 py-0.5 text-center text-xs'>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className='border-t'>
                        <td className='px-1 py-0.5 text-xs'>{formData.vehicleNumber}</td>
                        <td className='px-1 py-0.5 text-xs'>{formData.driverName}</td>
                        <td className='px-1 py-0.5 text-xs'>{formData.volumeFrom}</td>
                        <td className='px-1 py-0.5 text-center'>
                          <Button variant='ghost' size='sm' className='h-4 w-4 p-0'>
                            <X className='w-3 h-3' />
                          </Button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* 提示信息和操作选项合并卡片 */}
          <div className='col-span-3 bg-purple-50 rounded border p-1'>
            <div className='text-xs font-medium text-purple-700 mb-1 flex items-center gap-1'>
              <Save className='w-3 h-3' />
              提示信息与操作选项
            </div>
            <div className='grid grid-cols-4 gap-1'>
              <div>
                <Label className='text-xs text-gray-500'>注释</Label>
                <Input
                  value={formData.comments}
                  onChange={e => updateField('comments', e.target.value)}
                  className='h-5 text-xs'
                  placeholder='可填写注释'
                />
              </div>

              <div>
                <Label className='text-xs text-gray-500'>生产提示</Label>
                <Input
                  value={formData.productionTips}
                  onChange={e => updateField('productionTips', e.target.value)}
                  className='h-5 text-xs'
                  placeholder='可填写生产提示'
                />
              </div>

              <div>
                <Label className='text-xs text-gray-500'>司机提示</Label>
                <Input
                  value={formData.driverTips}
                  onChange={e => updateField('driverTips', e.target.value)}
                  className='h-5 text-xs'
                  placeholder='可填写司机提示'
                />
              </div>

              <div>
                <Label className='text-xs text-gray-500'>打印提示</Label>
                <Input
                  value={formData.printTips}
                  onChange={e => updateField('printTips', e.target.value)}
                  className='h-5 text-xs'
                  placeholder='可填写打印提示'
                />
              </div>
            </div>

            {/* 操作选项 */}
            <div className='mt-1 pt-1 border-t border-purple-200'>
              <div className='grid grid-cols-6 gap-1'>
                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='autoPlay'
                    checked={formData.autoPlay}
                    onCheckedChange={checked => updateField('autoPlay', checked)}
                  />
                  <Label htmlFor='autoPlay' className='text-xs'>
                    自动播放
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='sandMortar'
                    checked={formData.sandMortar}
                    onCheckedChange={checked => updateField('sandMortar', checked)}
                  />
                  <Label htmlFor='sandMortar' className='text-xs'>
                    背砂浆
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='washPump'
                    checked={formData.washPump}
                    onCheckedChange={checked => updateField('washPump', checked)}
                  />
                  <Label htmlFor='washPump' className='text-xs'>
                    接洗泵水
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='carryPump'
                    checked={formData.carryPump}
                    onCheckedChange={checked => updateField('carryPump', checked)}
                  />
                  <Label htmlFor='carryPump' className='text-xs'>
                    带拖泵
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='notToMixingStation'
                    checked={formData.notToMixingStation}
                    onCheckedChange={checked => updateField('notToMixingStation', checked)}
                  />
                  <Label htmlFor='notToMixingStation' className='text-xs'>
                    不发往搅拌站
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='allowWeighRoomEdit'
                    checked={formData.allowWeighRoomEdit}
                    onCheckedChange={checked => updateField('allowWeighRoomEdit', checked)}
                  />
                  <Label htmlFor='allowWeighRoomEdit' className='text-xs'>
                    允许磅房修改方量
                  </Label>
                </div>

                <div className='flex items-center space-x-1'>
                  <Checkbox
                    id='printTicket'
                    checked={formData.printTicket}
                    onCheckedChange={checked => updateField('printTicket', checked)}
                  />
                  <Label htmlFor='printTicket' className='text-xs'>
                    打票
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='flex justify-end gap-1 px-2 py-1 border-t bg-gray-50'>
          <Button
            variant='outline'
            onClick={handleCancel}
            className='flex items-center gap-1 h-6 text-xs'
          >
            <X className='w-3 h-3' />
            退出
          </Button>
          <Button
            onClick={handleConfirm}
            className='flex items-center gap-1 h-6 text-xs bg-blue-600 hover:bg-blue-700'
          >
            <Save className='w-3 h-3' />
            保存
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
