'use client';

import React, { useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Button } from '@/shared/components/button';
import { TaskProgressChart } from '@/shared/components/charts/TaskProgressChart';
import type { Task } from '@/core/types';

// 数据类型定义
export interface ProgressPoint {
  day: string;
  date: string;
  volume: number;
  dailyVolume: number;
  dispatchCount: number;
  vehicles: string[];
}

export interface VehicleDispatchRecord {
  id: string;
  vehicleNumber: string;
  dispatchTime: string; // 格式: "2025-07-01 08:30"
  volume: number;
  day: string;
}

export interface TaskProgressData {
  projectName: string;
  contractNumber: string;
  firstDispatchTime: string;
  lastDispatchTime: string;
  plannedVolume: number;
  completedVolume: number;
  progressData: ProgressPoint[];
  vehicleRecords: VehicleDispatchRecord[];
}

// 生成真实的车辆发车数据 - 每天2-10趟车
const generateRealisticVehicleData = (
  startDate: string,
  days: number,
  dailyVehicleCount: number[]
): { vehicleRecords: VehicleDispatchRecord[]; progressData: ProgressPoint[] } => {
  const vehicleRecords: VehicleDispatchRecord[] = [];
  const progressData: ProgressPoint[] = [];
  let cumulativeVolume = 0;
  let vehicleId = 1;

  for (let dayIndex = 0; dayIndex < days; dayIndex++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + dayIndex);
    const dateStr = currentDate.toISOString().split('T')[0] || '';
    const dayLabel =
      days === 1
        ? `${currentDate.getDate()}日`
        : days <= 31
          ? `${currentDate.getDate()}日`
          : `${currentDate.getMonth() + 1}/${currentDate.getDate()}`;

    const vehicleCount = dailyVehicleCount[dayIndex] || 0;
    const dayVehicles: string[] = [];
    let dailyVolume = 0;

    if (vehicleCount > 0) {
      // 生成当天的发车时间（工作时间8:00-18:00）
      const workStartHour = 8;
      const workEndHour = 18;
      const workHours = workEndHour - workStartHour;

      for (let i = 0; i < vehicleCount; i++) {
        // 在工作时间内均匀分布发车时间
        const hourOffset = (workHours / vehicleCount) * i + Math.random() * 0.5;
        const hour = Math.floor(workStartHour + hourOffset);
        const minute = Math.floor((hourOffset % 1) * 60);

        const vehicleNumber = `京A${(12345 + vehicleId - 1).toString().padStart(5, '0')}`;
        const volume = 8; // 每车8立方米
        const dispatchTime = `${dateStr} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

        vehicleRecords.push({
          id: vehicleId.toString(),
          vehicleNumber,
          dispatchTime,
          volume,
          day: dayLabel,
        });

        dayVehicles.push(vehicleNumber);
        dailyVolume += volume;
        vehicleId++;
      }
    }

    cumulativeVolume += dailyVolume;
    progressData.push({
      day: dayLabel,
      date: dateStr,
      volume: cumulativeVolume,
      dailyVolume,
      dispatchCount: vehicleCount,
      vehicles: dayVehicles,
    });
  }

  return { vehicleRecords, progressData };
};

// 生成模拟数据 - 支持不同时间跨度，每天2-10趟车
const generateMockData = (timeSpan: 'short' | 'medium' | 'long' = 'medium'): TaskProgressData => {
  let projectName = '';
  let startDate = '';
  let dailyVehicleCount: number[] = [];
  let days = 1;

  if (timeSpan === 'short') {
    // 短期项目：1天内，8趟车
    projectName = '紧急路面修复工程（1天完成）';
    startDate = '2025-01-15';
    days = 1;
    dailyVehicleCount = [8]; // 1天，8趟车
  } else if (timeSpan === 'long') {
    // 长期项目：跨年项目，5个月
    projectName = '大型基础设施建设工程（跨年项目）';
    startDate = '2024-10-01';
    days = 138; // 约5个月
    // 每月平均20个工作日，每天3-6趟车
    dailyVehicleCount = Array(138)
      .fill(0)
      .map((_, i) => {
        const dayOfWeek =
          (new Date('2024-10-01').getTime() + i * 24 * 60 * 60 * 1000) % (7 * 24 * 60 * 60 * 1000);
        const isWeekend = dayOfWeek < 2 * 24 * 60 * 60 * 1000; // 简化的周末判断
        return isWeekend ? 0 : Math.floor(Math.random() * 4) + 3; // 3-6趟车
      });
  } else {
    // 中期项目：12天（默认）
    projectName = '长治市源城区成（家川）小（锋）线成家川-平顺界段路面改造工程';
    startDate = '2025-07-01';
    days = 12;
    dailyVehicleCount = [3, 2, 4, 0, 2, 0, 0, 0, 0, 3, 0, 0]; // 12天，有些天不发车
  }

  // 使用生成函数创建真实数据
  const { vehicleRecords, progressData } = generateRealisticVehicleData(
    startDate,
    days,
    dailyVehicleCount
  );
  const firstDispatchTime = vehicleRecords.length > 0 ? vehicleRecords[0]?.dispatchTime || '' : '';
  const lastDispatchTime =
    vehicleRecords.length > 0 ? vehicleRecords[vehicleRecords.length - 1]?.dispatchTime || '' : '';

  return {
    projectName,
    contractNumber: 'CZSG-2025-001',
    firstDispatchTime,
    lastDispatchTime,
    plannedVolume: timeSpan === 'short' ? 32 : timeSpan === 'long' ? 200 : 100,
    completedVolume: timeSpan === 'short' ? 32 : timeSpan === 'long' ? 80 : 80.23,
    progressData,
    vehicleRecords,
  };
};

interface TaskProgressModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task | null;
  progressData?: TaskProgressData;
}

export const TaskProgressModalV3: React.FC<TaskProgressModalProps> = ({
  open,
  onOpenChange,
  task,
  progressData: externalData,
}) => {
  // 使用外部数据或模拟数据
  const progressData = useMemo(() => {
    return externalData || generateMockData();
  }, [externalData]);

  if (!task) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-7xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='text-xl font-bold text-gray-800'>任务进度详情</DialogTitle>
        </DialogHeader>

        <div className='space-y-3'>
          {/* 项目基本信息 - 紧凑布局 */}
          <div className='grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg'>
            <div>
              <h3 className='text-sm font-medium text-gray-700 mb-1'>项目信息</h3>
              <p className='text-sm text-gray-900 mb-1 leading-tight'>{progressData.projectName}</p>
              <p className='text-xs text-gray-600'>合同编号: {progressData.contractNumber}</p>
            </div>
            <div>
              <h3 className='text-sm font-medium text-gray-700 mb-1'>进度概况</h3>
              <div className='grid grid-cols-2 gap-3 text-sm'>
                <div>
                  <span className='text-gray-600'>计划量:</span>
                  <span className='ml-1 font-medium'>{progressData.plannedVolume}m³</span>
                </div>
                <div>
                  <span className='text-gray-600'>完成量:</span>
                  <span className='ml-1 font-medium text-blue-600'>
                    {progressData.completedVolume}m³
                  </span>
                </div>
                <div>
                  <span className='text-gray-600'>首次发车:</span>
                  <span className='ml-1 text-xs'>{progressData.firstDispatchTime}</span>
                </div>
                <div>
                  <span className='text-gray-600'>最近发车:</span>
                  <span className='ml-1 text-xs'>{progressData.lastDispatchTime}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 自定义任务进度图表 - 紧凑布局 */}
          <div className='border rounded-lg overflow-hidden'>
            <TaskProgressChart
              data={progressData.progressData}
              vehicleRecords={progressData.vehicleRecords}
              plannedVolume={progressData.plannedVolume}
              completedVolume={progressData.completedVolume}
              width={1200}
              height={410}
            />
          </div>

          {/* 统计信息 - 紧凑布局 */}
          <div className='grid grid-cols-6 gap-3'>
            <div className='bg-blue-50 p-2 rounded border border-blue-200'>
              <div className='text-xs text-blue-600 mb-1'>计划量</div>
              <div className='text-base font-bold text-blue-800'>{progressData.plannedVolume}</div>
              <div className='text-xs text-blue-600'>m³</div>
            </div>
            <div className='bg-green-50 p-2 rounded border border-green-200'>
              <div className='text-xs text-green-600 mb-1'>完成量</div>
              <div className='text-base font-bold text-green-800'>
                {progressData.completedVolume}
              </div>
              <div className='text-xs text-green-600'>m³</div>
            </div>
            <div className='bg-purple-50 p-2 rounded border border-purple-200'>
              <div className='text-xs text-purple-600 mb-1'>完成率</div>
              <div className='text-base font-bold text-purple-800'>
                {((progressData.completedVolume / progressData.plannedVolume) * 100).toFixed(1)}%
              </div>
            </div>
            <div className='bg-orange-50 p-2 rounded border border-orange-200'>
              <div className='text-xs text-orange-600 mb-1'>总发车</div>
              <div className='text-base font-bold text-orange-800'>
                {progressData.vehicleRecords.length}
              </div>
              <div className='text-xs text-orange-600'>车次</div>
            </div>
            <div className='bg-indigo-50 p-2 rounded border border-indigo-200'>
              <div className='text-xs text-indigo-600 mb-1'>剩余量</div>
              <div className='text-base font-bold text-indigo-800'>
                {(progressData.plannedVolume - progressData.completedVolume).toFixed(1)}
              </div>
              <div className='text-xs text-indigo-600'>m³</div>
            </div>
            <div className='bg-gray-50 p-2 rounded border border-gray-200'>
              <div className='text-xs text-gray-600 mb-1'>工期</div>
              <div className='text-base font-bold text-gray-800'>12</div>
              <div className='text-xs text-gray-600'>天</div>
            </div>
          </div>

          {/* 底部按钮 - 紧凑布局 */}
          <div className='flex justify-end gap-2 pt-3 border-t'>
            <Button
              variant='outline'
              onClick={() => onOpenChange(false)}
              className='h-8 px-4 text-sm'
            >
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// 同时导出为默认名称以保持兼容性
export const TaskProgressModal = TaskProgressModalV3;
