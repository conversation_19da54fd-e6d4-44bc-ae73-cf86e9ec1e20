'use client';

/**
 * 配比趋势图表组件
 * 显示配比参数的历史变化趋势
 */

import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';

export interface RatioTrendChartProps {
  data?: any;
  options?: any;
  width?: number;
  height?: number;
  className?: string;
  onLoad?: () => void;
  ratioId?: string;
}

interface RatioDataPoint {
  date: string;
  cement: number;
  water: number;
  sand: number;
  gravel: number;
  waterCementRatio: number;
  strength: number;
  slump: number;
}

const RatioTrendChart: React.FC<RatioTrendChartProps> = ({
  data,
  options,
  width = 800,
  height = 400,
  className = '',
  onLoad,
  ratioId,
}) => {
  const [isClient, setIsClient] = React.useState(false);

  // 客户端挂载后设置标志
  React.useEffect(() => {
    setIsClient(true);
  }, []);

  // 生成配比趋势数据
  const chartData = useMemo<RatioDataPoint[]>(() => {
    if (data && Array.isArray(data)) {
      return data;
    }

    // 生成过去30天的配比数据
    const points: RatioDataPoint[] = [];
    // 使用固定的基准时间避免水合错误
    const now = isClient ? new Date() : new Date('2024-01-15T00:00:00Z');

    for (let i = 29; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      // 基础配比参数 (C30混凝土)
      const baseCement = 350;
      const baseWater = 175;
      const baseSand = 650;
      const baseGravel = 1200;

      // 添加一些确定性变化 (±5%)，基于日期索引避免随机性
      const seed = i * 0.1; // 基于索引的种子
      const variation = () => 0.95 + (Math.sin(seed) * 0.05 + 0.05);

      const cement = Math.round(baseCement * variation());
      const water = Math.round(baseWater * variation());
      const sand = Math.round(baseSand * variation());
      const gravel = Math.round(baseGravel * variation());

      const waterCementRatio = Math.round((water / cement) * 100) / 100;

      // 强度在28-32MPa之间变化，使用确定性函数
      const strength = Math.round((28 + Math.sin(seed * 2) * 2 + 2) * 10) / 10;

      // 坍落度在180-220mm之间变化，使用确定性函数
      const slump = Math.round(180 + Math.sin(seed * 3) * 20 + 20);

      points.push({
        date: date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
        cement,
        water,
        sand,
        gravel,
        waterCementRatio,
        strength,
        slump,
      });
    }

    return points;
  }, [data, isClient]);

  // 组件加载完成回调
  React.useEffect(() => {
    onLoad?.();
  }, [onLoad]);

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className='bg-white p-4 border border-gray-200 rounded-lg shadow-lg max-w-xs'>
          <p className='font-medium text-gray-900 mb-2'>{`日期: ${label}`}</p>
          <div className='space-y-1 text-sm'>
            <p className='text-gray-700'>{`水泥: ${data.cement}kg/m³`}</p>
            <p className='text-blue-600'>{`水: ${data.water}kg/m³`}</p>
            <p className='text-yellow-600'>{`砂: ${data.sand}kg/m³`}</p>
            <p className='text-gray-600'>{`石: ${data.gravel}kg/m³`}</p>
            <hr className='my-2' />
            <p className='text-green-600'>{`水胶比: ${data.waterCementRatio}`}</p>
            <p className='text-red-600'>{`强度: ${data.strength}MPa`}</p>
            <p className='text-purple-600'>{`坍落度: ${data.slump}mm`}</p>
          </div>
        </div>
      );
    }
    return null;
  };

  // 计算统计信息
  const stats = useMemo(() => {
    const avgStrength = chartData.reduce((sum, item) => sum + item.strength, 0) / chartData.length;
    const avgWaterCementRatio =
      chartData.reduce((sum, item) => sum + item.waterCementRatio, 0) / chartData.length;
    const avgSlump = chartData.reduce((sum, item) => sum + item.slump, 0) / chartData.length;

    return {
      avgStrength: Math.round(avgStrength * 10) / 10,
      avgWaterCementRatio: Math.round(avgWaterCementRatio * 100) / 100,
      avgSlump: Math.round(avgSlump),
    };
  }, [chartData]);

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
      style={{ width, height }}
    >
      {/* 图表标题和统计 */}
      <div className='mb-4'>
        <div className='flex items-center justify-between'>
          <div>
            <h3 className='text-lg font-semibold text-gray-900'>
              配比参数趋势
              {ratioId && <span className='text-sm text-gray-500 ml-2'>({ratioId})</span>}
            </h3>
            <p className='text-sm text-gray-600'>30天配比参数变化趋势</p>
          </div>
          <div className='text-right text-sm space-y-1'>
            <div className='text-gray-600'>
              平均强度: <span className='font-medium text-red-600'>{stats.avgStrength}MPa</span>
            </div>
            <div className='text-gray-600'>
              平均水胶比:{' '}
              <span className='font-medium text-green-600'>{stats.avgWaterCementRatio}</span>
            </div>
            <div className='text-gray-600'>
              平均坍落度: <span className='font-medium text-purple-600'>{stats.avgSlump}mm</span>
            </div>
          </div>
        </div>
      </div>

      {/* 图表容器 */}
      <div style={{ width: '100%', height: height - 120 }}>
        <ResponsiveContainer>
          <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
            <XAxis dataKey='date' stroke='#666' fontSize={12} interval='preserveStartEnd' />
            <YAxis
              yAxisId='strength'
              stroke='#666'
              fontSize={12}
              domain={[25, 35]}
              label={{ value: '强度 (MPa)', angle: -90, position: 'insideLeft' }}
            />
            <YAxis
              yAxisId='ratio'
              orientation='right'
              stroke='#666'
              fontSize={12}
              domain={[0.4, 0.6]}
              label={{ value: '水胶比', angle: 90, position: 'insideRight' }}
            />
            <Tooltip content={<CustomTooltip />} />

            {/* 强度趋势线 */}
            <Line
              yAxisId='strength'
              type='monotone'
              dataKey='strength'
              stroke='#ef4444'
              strokeWidth={3}
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}
              name='强度 (MPa)'
            />

            {/* 水胶比趋势线 */}
            <Line
              yAxisId='ratio'
              type='monotone'
              dataKey='waterCementRatio'
              stroke='#10b981'
              strokeWidth={2}
              strokeDasharray='5 5'
              dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
              name='水胶比'
            />

            <Legend
              verticalAlign='top'
              height={36}
              iconType='line'
              wrapperStyle={{ paddingBottom: '20px' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* 图表说明 */}
      <div className='mt-4 flex items-center justify-between text-xs text-gray-500'>
        <div className='flex items-center space-x-4'>
          <div className='flex items-center space-x-1'>
            <div className='w-3 h-0.5 bg-red-500'></div>
            <span>强度趋势</span>
          </div>
          <div className='flex items-center space-x-1'>
            <div className='w-3 h-0.5 bg-green-500 border-dashed border-t'></div>
            <span>水胶比趋势</span>
          </div>
        </div>
        <div className='text-right'>
          <div>数据周期: 30天</div>
          <div>更新时间: {isClient ? new Date().toLocaleTimeString('zh-CN') : '--:--:--'}</div>
        </div>
      </div>
    </div>
  );
};

export default RatioTrendChart;
