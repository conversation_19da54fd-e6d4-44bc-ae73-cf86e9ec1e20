'use client';

import React, { useMemo } from 'react';

// 数据类型定义
export interface ProgressPoint {
  day: string;
  date: string;
  volume: number;
  dailyVolume: number;
  dispatchCount: number;
  vehicles: string[];
}

export interface VehicleDispatchRecord {
  id: string;
  vehicleNumber: string;
  dispatchTime: string; // 格式: "2025-07-01 08:30"
  volume: number;
  day: string;
}

export interface TaskProgressChartProps {
  data: ProgressPoint[];
  vehicleRecords: VehicleDispatchRecord[];
  plannedVolume: number;
  completedVolume: number;
  width?: number;
  height?: number;
}

export const TaskProgressChart: React.FC<TaskProgressChartProps> = ({
  data = [],
  vehicleRecords = [],
  plannedVolume = 0,
  completedVolume = 0,
  width = 800,
  height = 400,
}) => {
  const [isClient, setIsClient] = React.useState(false);

  // 客户端挂载后设置标志
  React.useEffect(() => {
    setIsClient(true);
  }, []);
  // 图表配置 - 优化布局，X轴移到发车密度下方，右侧预留说明区域
  const config = useMemo(() => {
    const chartAreaWidth = width - 200; // 减去右侧说明区域宽度
    return {
      margin: { top: 30, right: 60, bottom: 80, left: 80 },
      chartWidth: chartAreaWidth - 80 - 60, // 减去左右边距
      chartHeight: height - 30 - 80, // 减去上下边距，为发车密度和X轴预留空间
      vehicleDensityHeight: 40, // 车辆密度区域高度（压缩）
      xAxisHeight: 30, // X轴区域高度
    };
  }, [width, height]);

  // 计算Y轴范围和刻度
  const yAxisConfig = useMemo(() => {
    // 安全地处理数据数组，防止 undefined 错误
    const dataVolumes = Array.isArray(data) ? data.map(d => d?.volume || 0) : [];
    const maxVolume = Math.max(
      plannedVolume || 0,
      completedVolume || 0,
      ...dataVolumes,
      100 // 最小值，防止全为0的情况
    );
    const yMax = Math.ceil((maxVolume * 1.2) / 10) * 10; // 向上取整到10的倍数
    const tickCount = 6;
    const tickInterval = yMax / (tickCount - 1);
    const ticks = Array.from({ length: tickCount }, (_, i) => Math.round(i * tickInterval));

    return { yMax, ticks, tickInterval };
  }, [data, plannedVolume, completedVolume]);

  // 动态计算时间跨度和X轴配置 - 基于车辆发车记录的实际时间范围
  const timeSpanConfig = useMemo(() => {
    // 如果没有车辆记录，使用进度数据的日期范围
    if (vehicleRecords.length === 0 && data.length === 0) {
      const defaultDate = isClient ? new Date() : new Date('2024-01-15T00:00:00Z');
      return {
        startDate: defaultDate,
        endDate: defaultDate,
        totalDays: 1,
        timeUnit: 'day' as const,
        tickInterval: 1,
      };
    }

    let startDate: Date;
    let endDate: Date;

    // 安全地处理车辆记录数组
    const safeVehicleRecords = Array.isArray(vehicleRecords) ? vehicleRecords : [];
    const safeData = Array.isArray(data) ? data : [];

    if (safeVehicleRecords.length > 0) {
      // 基于车辆发车记录计算实际时间范围
      const dispatchTimes = safeVehicleRecords
        .filter(v => v?.dispatchTime) // 过滤无效数据
        .map(v => new Date(v.dispatchTime));

      if (dispatchTimes.length > 0) {
        startDate = new Date(Math.min(...dispatchTimes.map(d => d.getTime())));
        endDate = new Date(Math.max(...dispatchTimes.map(d => d.getTime())));
      } else {
        // 如果没有有效的调度时间，使用默认时间
        const defaultDate = isClient ? new Date() : new Date('2024-01-15T00:00:00Z');
        startDate = defaultDate;
        endDate = defaultDate;
      }
    } else {
      // 回退到进度数据的日期范围
      const firstData = safeData[0];
      const lastData = safeData[safeData.length - 1];
      if (!firstData || !lastData) {
        const defaultDate = isClient ? new Date() : new Date('2024-01-15T00:00:00Z');
        return {
          startDate: defaultDate,
          endDate: defaultDate,
          totalDays: 1,
          timeUnit: 'day' as const,
          tickInterval: 1,
        };
      }
      startDate = new Date(firstData.date);
      endDate = new Date(lastData.date);
    }

    // 计算实际时间跨度
    const totalMilliseconds = endDate.getTime() - startDate.getTime();
    const totalDays = Math.ceil(totalMilliseconds / (1000 * 60 * 60 * 24)) + 1;
    const totalHours = totalMilliseconds / (1000 * 60 * 60);

    // 智能选择时间单位和刻度间隔
    let timeUnit: 'hour' | 'day' | 'week' | 'month' = 'day';
    let tickInterval = 1;

    if (totalHours <= 24) {
      // 24小时内：按小时显示
      timeUnit = 'hour';
      if (totalHours <= 8) {
        tickInterval = 2; // 每2小时一个刻度
      } else if (totalHours <= 12) {
        tickInterval = 3; // 每3小时一个刻度
      } else {
        tickInterval = 4; // 每4小时一个刻度
      }
    } else if (totalDays <= 7) {
      // 7天内：按天显示
      timeUnit = 'day';
      tickInterval = 1;
    } else if (totalDays <= 31) {
      // 31天内：按天显示，动态间隔
      timeUnit = 'day';
      tickInterval = Math.ceil(totalDays / 8); // 最多8个刻度
    } else if (totalDays <= 365) {
      // 365天内：按周显示
      timeUnit = 'week';
      tickInterval = Math.ceil(totalDays / 7 / 8); // 最多8个刻度
    } else {
      // 超过365天：按月显示
      timeUnit = 'month';
      const totalMonths = totalDays / 30;
      tickInterval = Math.ceil(totalMonths / 8); // 最多8个刻度
    }

    return { startDate, endDate, totalDays, timeUnit, tickInterval, totalHours };
  }, [data, vehicleRecords, isClient]);

  // 计算X轴配置
  const xAxisConfig = useMemo(() => {
    const days = data.map(d => d.day);
    const tickWidth = config.chartWidth / (days.length - 1);

    // 生成动态时间刻度 - 基于实际时间范围
    const ticks = [];
    const { startDate, endDate, totalDays, timeUnit, tickInterval, totalHours } = timeSpanConfig;
    const totalTimeSpan = endDate.getTime() - startDate.getTime();

    if (timeUnit === 'hour') {
      // 按小时刻度：基于实际开始时间
      const startHour = startDate.getHours();
      const endHour = endDate.getHours() + (endDate.getDate() !== startDate.getDate() ? 24 : 0);

      for (let hour = startHour; hour <= endHour; hour += tickInterval) {
        const tickDate = new Date(startDate);
        tickDate.setHours(hour % 24);
        if (hour >= 24) {
          tickDate.setDate(tickDate.getDate() + Math.floor(hour / 24));
        }

        const timeProgress =
          totalTimeSpan > 0 ? (tickDate.getTime() - startDate.getTime()) / totalTimeSpan : 0;
        ticks.push({
          id: `hour-${hour}`,
          date: tickDate,
          label: `${hour % 24}:00`,
          position: Math.min(100, timeProgress * 100),
        });
      }
    } else if (timeUnit === 'day') {
      // 按天刻度：基于实际日期范围
      for (let i = 0; i < totalDays; i += tickInterval) {
        const tickDate = new Date(startDate);
        tickDate.setDate(startDate.getDate() + i);

        const timeProgress =
          totalTimeSpan > 0 ? (tickDate.getTime() - startDate.getTime()) / totalTimeSpan : 0;
        const label =
          totalDays <= 31
            ? `${tickDate.getDate()}日`
            : `${tickDate.getMonth() + 1}/${tickDate.getDate()}`;

        ticks.push({
          id: `day-${i}`,
          date: tickDate,
          label,
          position: Math.min(100, timeProgress * 100),
        });
      }
    } else if (timeUnit === 'week') {
      // 按周刻度：每周的开始日期
      for (let i = 0; i < totalDays; i += 7 * tickInterval) {
        const tickDate = new Date(startDate);
        tickDate.setDate(startDate.getDate() + i);

        const timeProgress =
          totalTimeSpan > 0 ? (tickDate.getTime() - startDate.getTime()) / totalTimeSpan : 0;
        ticks.push({
          id: `week-${i}`,
          date: tickDate,
          label: `${tickDate.getMonth() + 1}/${tickDate.getDate()}`,
          position: Math.min(100, timeProgress * 100),
        });
      }
    } else if (timeUnit === 'month') {
      // 按月刻度：每月的开始日期
      const startMonth = startDate.getMonth();
      const startYear = startDate.getFullYear();
      const totalMonths = Math.ceil(totalDays / 30);

      for (let i = 0; i < totalMonths; i += tickInterval) {
        const tickDate = new Date(startYear, startMonth + i, 1);

        const timeProgress =
          totalTimeSpan > 0 ? (tickDate.getTime() - startDate.getTime()) / totalTimeSpan : 0;
        ticks.push({
          id: `month-${i}`,
          date: tickDate,
          label: `${tickDate.getFullYear()}/${tickDate.getMonth() + 1}`,
          position: Math.min(100, timeProgress * 100),
        });
      }
    }

    return { days, tickWidth, ticks, timeSpan: timeSpanConfig };
  }, [data, config.chartWidth, timeSpanConfig]);

  // 坐标转换函数 - 添加安全检查
  const getX = (index: number) => {
    if (typeof index !== 'number' || isNaN(index)) return config.margin.left;
    return config.margin.left + index * xAxisConfig.tickWidth;
  };

  const getY = (volume: number) => {
    if (typeof volume !== 'number' || isNaN(volume) || yAxisConfig.yMax === 0) {
      return config.margin.top + config.chartHeight;
    }
    return config.margin.top + (1 - volume / yAxisConfig.yMax) * config.chartHeight;
  };

  // 生成进度折线路径
  const progressLinePath = useMemo(() => {
    const points = data.map((point, index) => `${getX(index)},${getY(point.volume)}`);
    return `M ${points.join(' L ')}`;
  }, [data, getX, getY]);

  // 计算车辆密度位置 - 基于动态时间跨度
  const vehiclePositions = useMemo(() => {
    const { startDate, endDate } = timeSpanConfig;
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    const totalTimeSpan = endTime - startTime;

    // 安全地处理车辆记录
    const safeVehicleRecords = Array.isArray(vehicleRecords) ? vehicleRecords : [];

    return safeVehicleRecords
      .filter(vehicle => vehicle?.dispatchTime) // 过滤无效数据
      .map(vehicle => {
        const vehicleTime = new Date(vehicle.dispatchTime).getTime();
        // 确保车辆时间在范围内
        const clampedTime = Math.max(startTime, Math.min(endTime, vehicleTime));
        const timeProgress = totalTimeSpan > 0 ? (clampedTime - startTime) / totalTimeSpan : 0;
        const x = config.margin.left + timeProgress * config.chartWidth;
        return { ...vehicle, x };
      });
  }, [vehicleRecords, config, timeSpanConfig]);

  return (
    <div className='relative bg-white border rounded-lg overflow-hidden'>
      {/* 图表标题和完成率 - 紧凑布局 */}
      <div className='p-3 border-b bg-gray-50'>
        <div className='flex items-center justify-between'>
          <h3 className='text-base font-medium text-gray-800'>任务进度图表</h3>
          <div className='px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium'>
            完成率: {((completedVolume / plannedVolume) * 100).toFixed(1)}%
          </div>
        </div>
      </div>

      {/* 图表主体 - 左侧图表 + 右侧说明 */}
      <div className='flex'>
        {/* 左侧：主图表区域 */}
        <div className='flex-1 relative' style={{ width: width - 200, height }}>
          <svg width={width - 200} height={height} className='absolute inset-0'>
            {/* 背景网格线 */}
            <defs>
              <pattern
                id='grid'
                width={xAxisConfig.tickWidth}
                height={config.chartHeight / (yAxisConfig.ticks.length - 1)}
                patternUnits='userSpaceOnUse'
              >
                <path
                  d={`M ${xAxisConfig.tickWidth} 0 L 0 0 0 ${config.chartHeight / (yAxisConfig.ticks.length - 1)}`}
                  fill='none'
                  stroke='#f3f4f6'
                  strokeWidth='1'
                />
              </pattern>
            </defs>

            {/* 网格背景 */}
            <rect
              x={config.margin.left}
              y={config.margin.top}
              width={config.chartWidth}
              height={config.chartHeight}
              fill='url(#grid)'
            />

            {/* Y轴 */}
            <line
              x1={config.margin.left}
              y1={config.margin.top}
              x2={config.margin.left}
              y2={config.margin.top + config.chartHeight}
              stroke='#6b7280'
              strokeWidth='2'
            />

            {/* X轴 */}
            <line
              x1={config.margin.left}
              y1={config.margin.top + config.chartHeight}
              x2={config.margin.left + config.chartWidth}
              y2={config.margin.top + config.chartHeight}
              stroke='#6b7280'
              strokeWidth='2'
            />

            {/* Y轴刻度和标签 */}
            {yAxisConfig.ticks.map(tick => {
              const y = getY(tick);
              return (
                <g key={tick}>
                  <line
                    x1={config.margin.left - 5}
                    y1={y}
                    x2={config.margin.left}
                    y2={y}
                    stroke='#6b7280'
                    strokeWidth='1'
                  />
                  <text
                    x={config.margin.left - 10}
                    y={y + 4}
                    textAnchor='end'
                    fontSize='12'
                    fill='#6b7280'
                  >
                    {tick}
                  </text>
                </g>
              );
            })}

            {/* Y轴标题 */}
            <text
              x={20}
              y={config.margin.top + config.chartHeight / 2}
              textAnchor='middle'
              fontSize='14'
              fill='#374151'
              transform={`rotate(-90, 20, ${config.margin.top + config.chartHeight / 2})`}
            >
              方量 (m³)
            </text>

            {/* X轴刻度线移除 - 完整X轴移到发车密度下方 */}

            {/* 目标线 */}
            <line
              x1={config.margin.left}
              y1={getY(plannedVolume)}
              x2={config.margin.left + config.chartWidth}
              y2={getY(plannedVolume)}
              stroke='#dc2626'
              strokeWidth='2'
              strokeDasharray='6,3'
            />

            {/* 目标线标签 */}
            <text
              x={config.margin.left + config.chartWidth + 10}
              y={getY(plannedVolume) + 4}
              fontSize='12'
              fill='#dc2626'
              fontWeight='medium'
            >
              目标: {plannedVolume}m³
            </text>

            {/* 进度折线 */}
            <path d={progressLinePath} fill='none' stroke='#1d4ed8' strokeWidth='3' />

            {/* 进度点 - 添加安全检查 */}
            {data.map((point, index) => {
              const x = getX(index);
              const y = getY(point.volume);

              // 安全检查，确保坐标有效
              if (isNaN(x) || isNaN(y) || !isFinite(x) || !isFinite(y)) {
                console.warn('Invalid coordinates for point:', point, 'x:', x, 'y:', y);
                return null;
              }

              return (
                <g key={point.day}>
                  <circle cx={x} cy={y} r='4' fill='#1d4ed8' stroke='#ffffff' strokeWidth='2' />
                  {/* 悬浮区域 */}
                  <circle cx={x} cy={y} r='8' fill='transparent' className='cursor-pointer'>
                    <title>{`${point.day}: ${point.volume}m³ (当日: ${point.dailyVolume}m³)`}</title>
                  </circle>
                </g>
              );
            })}
          </svg>

          {/* 车辆密度区域 - 紧凑布局 */}
          <div
            className='absolute bg-white'
            style={{
              left: config.margin.left,
              right: config.margin.right,
              top: config.margin.top + config.chartHeight + 8,
              height: config.vehicleDensityHeight + config.xAxisHeight,
            }}
          >
            {/* 发车密度区域 */}
            <div
              className='relative border-t border-gray-300 bg-white'
              style={{ height: config.vehicleDensityHeight }}
            >
              {/* 车辆密度标题 */}
              <div className='absolute -left-16 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 font-medium'>
                发车密度
              </div>

              {/* 背景网格线 - 与X轴刻度对齐 */}
              {xAxisConfig.ticks.map(tick => {
                const x = (tick.position / 100) * config.chartWidth;
                return (
                  <div
                    key={`grid-${tick.id}`}
                    className='absolute top-0 bottom-0 border-l border-gray-200'
                    style={{ left: x, transform: 'translateX(-0.5px)' }}
                  />
                );
              })}

              {/* 车辆竖线 */}
              {vehiclePositions.map(vehicle => (
                <div
                  key={vehicle.id}
                  className='absolute top-0 bottom-0 bg-gray-800 hover:bg-blue-600 cursor-pointer transition-colors group z-10'
                  style={{
                    left: vehicle.x - config.margin.left,
                    width: '2px',
                    transform: 'translateX(-50%)',
                  }}
                  title={`${vehicle.vehicleNumber} - ${vehicle.dispatchTime} - ${vehicle.volume}m³`}
                >
                  {/* 悬浮信息卡片 */}
                  <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-20'>
                    <div className='font-medium'>{vehicle.vehicleNumber}</div>
                    <div>{vehicle.dispatchTime}</div>
                    <div>{vehicle.volume}m³</div>
                    <div className='absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-800'></div>
                  </div>
                </div>
              ))}
            </div>

            {/* X轴标签区域 - 动态时间刻度 */}
            <div
              className='relative border-t border-gray-200 bg-gray-50'
              style={{ height: config.xAxisHeight }}
            >
              {/* X轴刻度线和标签 */}
              {xAxisConfig.ticks.map(tick => {
                const x = (tick.position / 100) * config.chartWidth;
                return (
                  <div key={tick.id} className='absolute' style={{ left: x, height: '100%' }}>
                    {/* 主刻度线 - 从发车密度区域延伸到X轴区域 */}
                    <div
                      className='absolute w-px bg-gray-500'
                      style={{
                        left: '-0.5px',
                        top: `-${config.vehicleDensityHeight}px`, // 延伸到发车密度区域
                        height: `${config.vehicleDensityHeight + 6}px`, // 覆盖发车密度区域 + X轴顶部
                      }}
                    ></div>
                    {/* 刻度标记 - X轴区域内的短线 */}
                    <div
                      className='absolute w-px h-3 bg-gray-600'
                      style={{
                        left: '-0.5px',
                        top: '0px',
                      }}
                    ></div>
                    {/* 标签 */}
                    <div
                      className='absolute top-2 text-xs text-gray-600 text-center transform -translate-x-1/2 whitespace-nowrap'
                      style={{ left: '0px' }}
                    >
                      {tick.label}
                    </div>
                  </div>
                );
              })}

              {/* 时间跨度信息 */}
              <div className='absolute bottom-1 left-1 text-xs text-gray-500'>
                {timeSpanConfig.timeUnit === 'hour' && '按小时'}
                {timeSpanConfig.timeUnit === 'day' && '按天'}
                {timeSpanConfig.timeUnit === 'week' && '按周'}
                {timeSpanConfig.timeUnit === 'month' && '按月'}({timeSpanConfig.totalDays}天)
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：图表说明 - 纵向排列 */}
        <div className='w-48 border-l bg-gray-50'>
          <div className='p-3'>
            <h4 className='text-sm font-medium text-gray-800 mb-3'>图表说明</h4>

            {/* 图例项目 - 纵向排列 */}
            <div className='space-y-3'>
              <div className='flex items-center gap-2'>
                <div className='w-4 h-0.5 bg-blue-600'></div>
                <span className='text-xs text-gray-700'>累计进度曲线</span>
              </div>

              <div className='flex items-center gap-2'>
                <div className='w-4 h-0.5 bg-red-600' style={{ borderTop: '1px dashed' }}></div>
                <span className='text-xs text-gray-700'>计划目标线</span>
              </div>

              <div className='flex items-center gap-2'>
                <div className='w-0.5 h-4 bg-gray-800'></div>
                <span className='text-xs text-gray-700'>车辆发车密度</span>
              </div>

              <div className='flex items-center gap-2'>
                <div className='w-2 h-2 bg-blue-600 rounded-full'></div>
                <span className='text-xs text-gray-700'>进度节点</span>
              </div>
            </div>

            {/* 统计信息 */}
            <div className='mt-4 pt-3 border-t border-gray-200'>
              <h5 className='text-xs font-medium text-gray-700 mb-2'>统计信息</h5>
              <div className='space-y-2 text-xs text-gray-600'>
                <div className='flex justify-between'>
                  <span>计划量:</span>
                  <span className='font-medium'>{plannedVolume}m³</span>
                </div>
                <div className='flex justify-between'>
                  <span>完成量:</span>
                  <span className='font-medium text-blue-600'>{completedVolume}m³</span>
                </div>
                <div className='flex justify-between'>
                  <span>完成率:</span>
                  <span className='font-medium text-green-600'>
                    {((completedVolume / plannedVolume) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>发车次数:</span>
                  <span className='font-medium'>{vehicleRecords.length}车</span>
                </div>
              </div>
            </div>

            {/* 时间信息 */}
            <div className='mt-4 pt-3 border-t border-gray-200'>
              <h5 className='text-xs font-medium text-gray-700 mb-2'>时间信息</h5>
              <div className='space-y-1 text-xs text-gray-600'>
                <div>
                  <span className='text-gray-500'>时间单位:</span>
                  <span className='ml-1'>
                    {timeSpanConfig.timeUnit === 'hour' && '按小时'}
                    {timeSpanConfig.timeUnit === 'day' && '按天'}
                    {timeSpanConfig.timeUnit === 'week' && '按周'}
                    {timeSpanConfig.timeUnit === 'month' && '按月'}
                  </span>
                </div>
                <div>
                  <span className='text-gray-500'>总天数:</span>
                  <span className='ml-1'>{timeSpanConfig.totalDays}天</span>
                </div>
                <div>
                  <span className='text-gray-500'>更新时间:</span>
                  <span className='ml-1'>
                    {isClient
                      ? new Date().toLocaleString('zh-CN', {
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                        })
                      : '--/-- --:--'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskProgressChart;
