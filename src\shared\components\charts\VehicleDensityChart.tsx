'use client';

import React from 'react';

export interface VehicleDensityChartProps {
  data?: any;
  options?: any;
  width?: number;
  height?: number;
  className?: string;
  onLoad?: () => void;
  taskId?: string;
}

const VehicleDensityChart: React.FC<VehicleDensityChartProps> = ({
  width = 800,
  height = 300,
  className = '',
  onLoad,
  taskId,
}) => {
  React.useEffect(() => {
    onLoad?.();
  }, [onLoad]);

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
      style={{ width, height }}
    >
      <div className='mb-4'>
        <h3 className='text-lg font-semibold text-gray-900'>
          车辆调度密度
          {taskId && <span className='text-sm text-gray-500 ml-2'>({taskId})</span>}
        </h3>
        <p className='text-sm text-gray-600'>24小时车辆调度分布与效率</p>
      </div>

      <div className='flex items-center justify-center h-32 bg-gray-50 rounded'>
        <div className='text-center text-gray-500'>
          <div className='text-lg mb-2'>📊</div>
          <div>车辆密度图表</div>
          <div className='text-xs mt-1'>开发中...</div>
        </div>
      </div>

      <div className='mt-4 text-xs text-gray-500 text-center'>
        更新时间: {new Date().toLocaleTimeString('zh-CN')}
      </div>
    </div>
  );
};

export default VehicleDensityChart;
