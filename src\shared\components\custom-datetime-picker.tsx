'use client';

import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { cn } from '@/core/lib/utils';
import dayjs from 'dayjs';
import { Calendar, ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface CustomDateTimePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  showTime?: boolean;
  format?: string;
}

export const CustomDateTimePicker: React.FC<CustomDateTimePickerProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = '选择日期时间',
  className,
  showTime = true,
  format = 'YYYY-MM-DD HH:mm',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(() => {
    if (!value) return null;
    const parsed = dayjs(value);
    return parsed.isValid() ? parsed : null;
  });
  const [currentMonth, setCurrentMonth] = useState(() => {
    if (value) {
      const parsed = dayjs(value);
      return parsed.isValid() ? parsed : dayjs();
    }
    return dayjs();
  });
  const [timeValue, setTimeValue] = useState(() => {
    if (value) {
      const parsed = dayjs(value);
      if (parsed.isValid()) {
        return { hour: parsed.hour(), minute: parsed.minute() };
      }
    }
    return { hour: 0, minute: 0 };
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 处理外部 value 变化
  useEffect(() => {
    if (value) {
      const parsed = dayjs(value);
      if (parsed.isValid()) {
        setSelectedDate(parsed);
        setCurrentMonth(parsed);
        setTimeValue({ hour: parsed.hour(), minute: parsed.minute() });
      }
    } else {
      setSelectedDate(null);
    }
  }, [value]);

  // 关闭弹窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 生成日历天数
  const generateCalendarDays = () => {
    const startOfMonth = currentMonth.startOf('month');
    const endOfMonth = currentMonth.endOf('month');
    const startOfWeek = startOfMonth.startOf('week');
    const endOfWeek = endOfMonth.endOf('week');

    const days = [];
    let current = startOfWeek;

    while (current.isBefore(endOfWeek) || current.isSame(endOfWeek, 'day')) {
      days.push(current);
      current = current.add(1, 'day');
    }

    return days;
  };

  const handleDateSelect = (date: dayjs.Dayjs) => {
    // 如果是第一次选择日期且显示时间，使用当前时间作为默认值
    let finalHour = timeValue.hour;
    let finalMinute = timeValue.minute;

    if (!selectedDate && showTime) {
      const now = dayjs();
      finalHour = now.hour();
      finalMinute = now.minute();
      setTimeValue({ hour: finalHour, minute: finalMinute });
    }

    const newDate = date.hour(finalHour).minute(finalMinute);
    setSelectedDate(newDate);

    if (!showTime) {
      onChange?.(newDate.format(format));
      setIsOpen(false);
    }
  };

  const handleTimeChange = (type: 'hour' | 'minute', value: number) => {
    const newTimeValue = { ...timeValue, [type]: value };
    setTimeValue(newTimeValue);

    if (selectedDate) {
      const newDate = selectedDate.hour(newTimeValue.hour).minute(newTimeValue.minute);
      setSelectedDate(newDate);
    }
  };

  const handleConfirm = () => {
    if (selectedDate) {
      const finalDate = selectedDate.hour(timeValue.hour).minute(timeValue.minute);
      onChange?.(finalDate.format(format));
    }
    setIsOpen(false);
  };

  const handleToday = () => {
    const today = dayjs();
    setCurrentMonth(today);
    setSelectedDate(today);
    setTimeValue({
      hour: today.hour(),
      minute: today.minute(),
    });
  };

  const displayValue = selectedDate ? selectedDate.format(format) : '';

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      {/* 输入框 */}
      <div className='relative'>
        <Input
          ref={inputRef}
          value={displayValue}
          placeholder={placeholder}
          readOnly
          disabled={disabled}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={cn(
            'h-6 text-xs pr-7 border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors cursor-pointer',
            disabled && 'bg-gray-100 cursor-not-allowed'
          )}
        />
        <div
          className={cn(
            'absolute right-0 top-0 h-full w-7 flex items-center justify-center cursor-pointer',
            disabled && 'cursor-not-allowed'
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <Calendar
            className={cn(
              'w-3 h-3 transition-colors',
              disabled ? 'text-gray-400' : 'text-slate-500'
            )}
          />
        </div>
      </div>

      {/* 弹出面板 */}
      {isOpen && (
        <div className='absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-3 min-w-[300px] max-w-[320px]'>
          {/* 月份导航 */}
          <div className='flex items-center justify-between mb-3'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setCurrentMonth(currentMonth.subtract(1, 'month'))}
              className='h-6 w-6 p-0'
            >
              <ChevronLeft className='h-3 w-3' />
            </Button>
            <span className='text-sm font-medium'>{currentMonth.format('YYYY年MM月')}</span>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setCurrentMonth(currentMonth.add(1, 'month'))}
              className='h-6 w-6 p-0'
            >
              <ChevronRight className='h-3 w-3' />
            </Button>
          </div>

          {/* 星期标题 */}
          <div className='grid grid-cols-7 gap-1 mb-2'>
            {['日', '一', '二', '三', '四', '五', '六'].map(day => (
              <div key={day} className='text-xs text-gray-500 text-center py-1'>
                {day}
              </div>
            ))}
          </div>

          {/* 日期网格 */}
          <div className='grid grid-cols-7 gap-1 mb-3'>
            {generateCalendarDays().map((day, index) => {
              const isCurrentMonth = day.month() === currentMonth.month();
              const isSelected = selectedDate && day.isSame(selectedDate, 'day');
              const isToday = day.isSame(dayjs(), 'day');

              return (
                <button
                  key={index}
                  onClick={() => handleDateSelect(day)}
                  className={cn(
                    'h-7 w-7 text-xs rounded-md transition-all duration-200 font-medium',
                    isCurrentMonth ? 'text-gray-900' : 'text-gray-400',
                    isSelected && 'bg-blue-500 text-white shadow-sm',
                    isToday && !isSelected && 'bg-blue-100 text-blue-700 font-semibold',
                    !isSelected && 'hover:bg-blue-50 hover:text-blue-600',
                    isSelected && 'hover:bg-blue-600'
                  )}
                >
                  {day.date()}
                </button>
              );
            })}
          </div>

          {/* 时间选择 */}
          {showTime && (
            <div className='border-t pt-3 mb-3'>
              <div className='flex items-center gap-2 mb-2'>
                <Clock className='h-3 w-3 text-gray-500' />
                <span className='text-xs text-gray-600'>时间</span>
              </div>
              <div className='flex items-center gap-2 justify-center'>
                <select
                  value={timeValue.hour}
                  onChange={e => handleTimeChange('hour', parseInt(e.target.value))}
                  className='text-xs border border-gray-200 rounded-md px-2 py-1.5 w-16 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <option key={i} value={i}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
                <span className='text-sm text-gray-500 font-medium'>:</span>
                <select
                  value={timeValue.minute}
                  onChange={e => handleTimeChange('minute', parseInt(e.target.value))}
                  className='text-xs border border-gray-200 rounded-md px-2 py-1.5 w-16 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                >
                  {Array.from({ length: 60 }, (_, i) => (
                    <option key={i} value={i}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className='flex items-center justify-between border-t pt-3'>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleToday}
              className='h-7 text-xs px-3 text-blue-600 hover:text-blue-700 hover:bg-blue-50'
            >
              今天
            </Button>
            <div className='flex gap-2'>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setIsOpen(false)}
                className='h-7 text-xs px-3 hover:bg-gray-100'
              >
                取消
              </Button>
              <Button
                size='sm'
                onClick={handleConfirm}
                className='h-7 text-xs px-4 bg-blue-500 hover:bg-blue-600'
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
