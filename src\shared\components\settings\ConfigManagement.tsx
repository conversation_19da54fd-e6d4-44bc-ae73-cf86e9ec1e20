/**
 * ConfigManagement - 配置管理界面
 * 提供可视化的配置编辑、导入导出、验证等功能
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { Textarea } from '@/shared/components/textarea';
import { Switch } from '@/shared/components/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { Alert, AlertDescription } from '@/shared/components/alert';
import {
  Settings,
  Download,
  Upload,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Palette,
  Layout,
  Table,
  CreditCard,
  List,
  TestTube,
  Zap,
} from 'lucide-react';

import {
  useConfig,
  useThemeConfig,
  useDensityConfig,
  useTableConfig,
  useCardConfig,
  useTaskListConfig,
  useRatioConfig,
  usePerformanceConfig,
  DEFAULT_CONFIG,
} from '@/core/contexts/ConfigProvider';
import { validateConfig, fixConfig } from '@/core/utils/config-validator';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

/**
 * 主题配置面板
 */
function ThemeConfigPanel() {
  const { theme, updateTheme } = useThemeConfig();

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Palette className='w-5 h-5' />
          主题配置
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>主题模式</Label>
            <Select
              value={theme.mode}
              onValueChange={(value: 'light' | 'dark' | 'auto') => updateTheme({ mode: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='light'>浅色</SelectItem>
                <SelectItem value='dark'>深色</SelectItem>
                <SelectItem value='auto'>自动</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label>字体系列</Label>
            <Select
              value={theme.fontFamily}
              onValueChange={value => updateTheme({ fontFamily: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='system-ui'>系统字体</SelectItem>
                <SelectItem value='Inter'>Inter</SelectItem>
                <SelectItem value='Roboto'>Roboto</SelectItem>
                <SelectItem value='Noto Sans SC'>思源黑体</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>主色调</Label>
            <div className='flex items-center gap-2'>
              <Input
                type='color'
                value={theme.primaryColor}
                onChange={e => updateTheme({ primaryColor: e.target.value })}
                className='w-12 h-8 p-1'
              />
              <Input
                value={theme.primaryColor}
                onChange={e => updateTheme({ primaryColor: e.target.value })}
                placeholder='#2563eb'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label>强调色</Label>
            <div className='flex items-center gap-2'>
              <Input
                type='color'
                value={theme.accentColor}
                onChange={e => updateTheme({ accentColor: e.target.value })}
                className='w-12 h-8 p-1'
              />
              <Input
                value={theme.accentColor}
                onChange={e => updateTheme({ accentColor: e.target.value })}
                placeholder='#7c3aed'
              />
            </div>
          </div>
        </div>

        <div className='space-y-2'>
          <Label>圆角大小: {theme.borderRadius}px</Label>
          <Input
            type='range'
            min='0'
            max='20'
            value={theme.borderRadius}
            onChange={e => updateTheme({ borderRadius: parseInt(e.target.value) })}
          />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 密度配置面板
 */
function DensityConfigPanel() {
  const { density, updateDensity } = useDensityConfig();

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Layout className='w-5 h-5' />
          密度配置
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='space-y-2'>
          <Label>密度模式</Label>
          <Select
            value={density.mode}
            onValueChange={(value: 'compact' | 'comfortable' | 'spacious') =>
              updateDensity({ mode: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='compact'>紧凑</SelectItem>
              <SelectItem value='comfortable'>舒适</SelectItem>
              <SelectItem value='spacious'>宽松</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>内边距: {density.padding}px</Label>
            <Input
              type='range'
              min='0'
              max='32'
              value={density.padding}
              onChange={e => updateDensity({ padding: parseInt(e.target.value) })}
            />
          </div>

          <div className='space-y-2'>
            <Label>间距: {density.spacing}px</Label>
            <Input
              type='range'
              min='0'
              max='32'
              value={density.spacing}
              onChange={e => updateDensity({ spacing: parseInt(e.target.value) })}
            />
          </div>
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>字体大小: {density.fontSize}px</Label>
            <Input
              type='range'
              min='10'
              max='24'
              value={density.fontSize}
              onChange={e => updateDensity({ fontSize: parseInt(e.target.value) })}
            />
          </div>

          <div className='space-y-2'>
            <Label>图标大小: {density.iconSize}px</Label>
            <Input
              type='range'
              min='12'
              max='32'
              value={density.iconSize}
              onChange={e => updateDensity({ iconSize: parseInt(e.target.value) })}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 表格配置面板
 */
function TableConfigPanel() {
  const { table, updateTable } = useTableConfig();

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Table className='w-5 h-5' />
          表格配置
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>默认列宽: {table.defaultColumnWidth}px</Label>
            <Input
              type='range'
              min='80'
              max='400'
              value={table.defaultColumnWidth}
              onChange={e => updateTable({ defaultColumnWidth: parseInt(e.target.value) })}
            />
          </div>

          <div className='space-y-2'>
            <Label>最小字体: {table.minFontSize}px</Label>
            <Input
              type='range'
              min='8'
              max='20'
              value={table.minFontSize}
              onChange={e => updateTable({ minFontSize: parseInt(e.target.value) })}
            />
          </div>
        </div>

        <div className='flex items-center justify-between'>
          <Label>显示边框</Label>
          <Switch
            checked={table.showBorders}
            onCheckedChange={checked => updateTable({ showBorders: checked })}
          />
        </div>

        <div className='flex items-center justify-between'>
          <Label>交替行颜色</Label>
          <Switch
            checked={table.alternateRowColors}
            onCheckedChange={checked => updateTable({ alternateRowColors: checked })}
          />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 配置验证结果显示
 */
function ValidationResults({ validation }: { validation: any | null }) {
  if (!validation) return null;

  return (
    <div className='space-y-2'>
      {validation.isValid ? (
        <Alert>
          <CheckCircle className='h-4 w-4' />
          <AlertDescription>配置验证通过</AlertDescription>
        </Alert>
      ) : (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>发现 {validation.errors.length} 个错误</AlertDescription>
        </Alert>
      )}

      {validation.errors.map((error: any, index: number) => (
        <Alert key={index} variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            <strong>{error.path}:</strong> {error.message}
          </AlertDescription>
        </Alert>
      ))}

      {validation.warnings.map((warning: any, index: number) => (
        <Alert key={index}>
          <Info className='h-4 w-4' />
          <AlertDescription>
            <strong>{warning.path}:</strong> {warning.message}
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
}

/**
 * 配置管理主组件
 */
export function ConfigManagement() {
  const { config, updateConfig, resetConfig, exportConfig, importConfig, isLoading, error } =
    useConfig();
  const [validation, setValidation] = useState<any | null>(null);
  const [importText, setImportText] = useState('');

  // 验证当前配置
  const handleValidate = useCallback(() => {
    const result = validateConfig(config);
    setValidation(result);
  }, [config]);

  // 修复配置
  const handleFix = useCallback(() => {
    const fixedConfig = fixConfig(config);
    updateConfig(fixedConfig);
    setValidation(null);
  }, [config, updateConfig]);

  // 导出配置
  const handleExport = useCallback(() => {
    const configJson = exportConfig();
    // 使用安全的下载函数
    safeDownloadFile(configJson, 'tmh-config.json', 'application/json');
  }, [exportConfig]);

  // 导入配置
  const handleImport = useCallback(() => {
    if (importText.trim()) {
      const success = importConfig(importText);
      if (success) {
        setImportText('');
        setValidation(null);
      }
    }
  }, [importText, importConfig]);

  // 重置配置
  const handleReset = useCallback(() => {
    if (confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
      resetConfig();
      setValidation(null);
    }
  }, [resetConfig]);

  if (isLoading) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <RefreshCw className='w-8 h-8 animate-spin mx-auto mb-2' />
          <p>加载配置中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 头部操作栏 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Settings className='w-5 h-5' />
            配置管理
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center gap-2 flex-wrap'>
            <Button onClick={handleValidate} variant='outline' size='sm'>
              <CheckCircle className='w-4 h-4 mr-1' />
              验证配置
            </Button>
            <Button onClick={handleFix} variant='outline' size='sm'>
              <RefreshCw className='w-4 h-4 mr-1' />
              修复配置
            </Button>
            <Button onClick={handleExport} variant='outline' size='sm'>
              <Download className='w-4 h-4 mr-1' />
              导出配置
            </Button>
            <Button onClick={handleReset} variant='destructive' size='sm'>
              <RefreshCw className='w-4 h-4 mr-1' />
              重置配置
            </Button>
          </div>

          {error && (
            <Alert variant='destructive' className='mt-4'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 验证结果 */}
      {validation && <ValidationResults validation={validation} />}

      {/* 配置面板 */}
      <Tabs defaultValue='theme' className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='theme'>主题</TabsTrigger>
          <TabsTrigger value='density'>密度</TabsTrigger>
          <TabsTrigger value='table'>表格</TabsTrigger>
          <TabsTrigger value='import'>导入导出</TabsTrigger>
        </TabsList>

        <TabsContent value='theme'>
          <ThemeConfigPanel />
        </TabsContent>

        <TabsContent value='density'>
          <DensityConfigPanel />
        </TabsContent>

        <TabsContent value='table'>
          <TableConfigPanel />
        </TabsContent>

        <TabsContent value='import' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Upload className='w-5 h-5' />
                导入配置
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label>配置JSON</Label>
                <Textarea
                  value={importText}
                  onChange={e => setImportText(e.target.value)}
                  placeholder='粘贴配置JSON...'
                  rows={10}
                />
              </div>
              <Button onClick={handleImport} disabled={!importText.trim()}>
                <Upload className='w-4 h-4 mr-1' />
                导入配置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
